@import "bootstrap/variables.less";
@import "bootstrap/mixins.less";

@import "ace-features.less";
@import "variables.less";//there are also some variables on top of some other less files
@import "mixins.less";


@import "general.less";//includes general basic styling of page
@import "basic.less";//includes styling of some elements such as pagination, etc
@import "utility.less";//includes some utility classes such as headers, colors, font sizing, etc
@import "ace-nav.less";//ace top navigation

@import "sidebar.less";
@import "sidebar/ff_fix.less";//some firefox navbar and sidebar flicker fix

@import "scroll.less";


@import "breadcrumbs.less";
@import "searchbox.less";

@import "footer.less";


@import "buttons.less";
@import "label-badge.less";
@import "dropdown.less";
@import "form.less";

@import "tab-accordion.less";

@import "tables.less";
@import "widget.less";
@import "tooltip-popover.less";
@import "progressbar.less";

@import "infobox.less";


@import "page.pricing.less";
@import "page.login.less";

@import "page.invoice.less";



@import "page.error.less";
@import "gallery.less";
@import "items.less";
@import "page.profile.less";
@import "page.inbox.less";
@import "page.timeline.less";

@import "thirdparty-dataTables.less";
@import "thirdparty-calendar.less";
@import "thirdparty-chosen.less";
@import "thirdparty-select2.less";
@import "thirdparty-colorbox.less";
@import "thirdparty-fuelux.less";//fuelux spinner, tree & wizard
@import "thirdparty-gritter.less";
@import "thirdparty-wysiwyg.less";
@import "thirdparty-editable.less";

@import "thirdparty-date.less";//date & time
@import "thirdparty-slider.less";//jquery ui slider
@import "thirdparty-jquery-ui.less";//other jquery ui widgets & elements
@import "thirdparty-jqgrid.less";//jqGrid plugin
@import "thirdparty-nestable.less";//nestable list
@import "thirdparty-dropzone.less";//dropzone.js
@import "thirdparty-typeahead.less";//typeahead


@import "icon-animated.less";



@import "other.less";//setting box, etc

@import "ext/bootstrap-tag.less";//less files provided by the thirdparty plugin, sometimes modified


@import "bs3-reset.less";//resetting box-sizing to default content-box for some third party elements

@import "ace-responsive.less";



//navbar/sidebar color skin
//you can import a different skin
@selected-skin-1: "skins/empty.less";
@import "@{selected-skin-1}";

@selected-skin-2: "skins/no-skin.less";
@import "@{selected-skin-2}";





@import "onpage-help.less";//print friendly
@import "print.less";//print friendly

