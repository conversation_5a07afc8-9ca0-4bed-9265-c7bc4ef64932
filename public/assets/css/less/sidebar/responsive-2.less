//sidebar is automatically minimized in smaller views


.enable_minimized_responsive_menu() when(@enable-minimized-responsive-menu = true) {

	.sidebar-toggle.sidebar-expand {
		display: none;
	}
	@media (max-width: @grid-float-breakpoint-max) {
	 .menu-toggler.invisible {
		position: fixed;
		z-index: -999;
		visibility: hidden;
		opacity: 0;
		width: 1px;
		height: 1px;
	 }

	 .sidebar.responsive-min {
		.menu_min();
		
		&:before {
			display: block;
		}
		.sidebar-toggle.sidebar-collapse {
			display: none;
		}
		.sidebar-toggle.sidebar-expand {
			display: block;
		}

		+ .main-content {
			margin-left: @sidebar-min-width !important;
			.breadcrumb {
				margin-left: @breadcrumb-margin-left;
			}
		}
	 }

	 .sidebar.responsive-max {
		display: block;
		position: relative;
		left: @sidebar-width;
		margin-left: -(@sidebar-width);

		z-index: @zindex-sidebar-fixed;

		.sidebar-toggle {
			&.sidebar-collapse {
				display: none;
			}
			&.sidebar-expand {
				display: block;
			}
		}

		+ .main-content {
			margin-left: (@sidebar-min-width) !important;
			.breadcrumb {
				margin-left: @breadcrumb-margin-left;
			}
		}
	 }

	}


}
.enable_minimized_responsive_menu();