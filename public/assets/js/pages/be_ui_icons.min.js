/*!
 * dashmix - v3.1.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2020
 */
!function(){function n(n,e){for(var r=0;r<e.length;r++){var t=e[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(n,t.key,t)}}var e=function(){function e(){!function(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e)}var r,t;return r=e,t=[{key:"iconSearch",value:function(){var n,e=jQuery(".js-icon-list > div"),r="";jQuery(".js-form-icon-search").on("submit",(function(){return!1})),jQuery(".js-icon-search").on("keyup",(function(t){(r=jQuery(t.currentTarget).val().toLowerCase()).length>2?(e.hide(),jQuery("code",e).each((function(e,t){(n=jQuery(t)).text().match(r)&&n.parent("div").fadeIn(250)}))):0===r.length&&e.show()}))}},{key:"init",value:function(){this.iconSearch()}}],null&&n(r.prototype,null),t&&n(r,t),e}();jQuery((function(){e.init()}))}();