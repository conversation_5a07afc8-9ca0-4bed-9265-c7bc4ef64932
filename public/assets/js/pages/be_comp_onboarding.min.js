/*!
 * dashmix - v3.1.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2020
 */
!function(){function n(n,o){for(var e=0;e<o.length;e++){var i=o[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(n,i.key,i)}}var o=function(){function o(){!function(n,o){if(!(n instanceof o))throw new TypeError("Cannot call a class as a function")}(this,o)}var e,i;return e=o,i=[{key:"initOnboardingModal",value:function(){jQuery("#modal-onboarding").modal("show"),jQuery("#modal-onboarding").on("shown.bs.modal",(function(n){jQuery("js-slider","#modal-onboarding").removeClass("js-slider-enabled"),Dashmix.helpers("slick")}))}},{key:"init",value:function(){this.initOnboardingModal()}}],null&&n(e.prototype,null),i&&n(e,i),o}();jQuery((function(){o.init()}))}();