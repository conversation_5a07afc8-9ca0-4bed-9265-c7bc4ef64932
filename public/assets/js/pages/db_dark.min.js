/*!
 * dashmix - v3.1.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2020
 */
!function(){function a(a,o){for(var r=0;r<o.length;r++){var t=o[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(a,t.key,t)}}var o=function(){function o(){!function(a,o){if(!(a instanceof o))throw new TypeError("Cannot call a class as a function")}(this,o)}var r,t;return r=o,t=[{key:"initChartsMain",value:function(){Chart.defaults.global.defaultFontColor="rgba(255,255,255,.75)",Chart.defaults.scale.gridLines.color="transparent",Chart.defaults.scale.gridLines.zeroLineColor="transparent",Chart.defaults.scale.display=!1,Chart.defaults.scale.ticks.beginAtZero=!0,Chart.defaults.global.elements.line.borderWidth=0,Chart.defaults.global.elements.point.radius=0,Chart.defaults.global.elements.point.hoverRadius=0,Chart.defaults.global.tooltips.cornerRadius=3,Chart.defaults.global.legend.labels.boxWidth=12,Chart.defaults.global.legend.labels.fontColor="rgba(255,255,255,.75)";var a,o,r,t,e,l,n=jQuery(".js-chartjs-dashboard-earnings");o={maintainAspectRatio:!1,scales:{yAxes:[{ticks:{suggestedMax:260}}]},tooltips:{intersect:!1,callbacks:{label:function(a,o){return" "+a.yLabel+" Sales"}}}},r={labels:["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"],datasets:[{label:"This Year",fill:!0,backgroundColor:"rgba(255, 255, 255, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[50,210,110,90,230,130,190,75,155,120,140,230]},{label:"Last Year",fill:!0,backgroundColor:"rgba(255, 255, 255, .15)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[210,150,90,220,150,216,143,150,240,230,136,150]}]},t={labels:["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"],datasets:[{label:"This Year",fill:!0,backgroundColor:"rgba(255, 255, 255, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[50,210,110,90,230,130,190,75,155,120,140,230]},{label:"Last Year",fill:!0,backgroundColor:"rgba(255, 255, 255, .15)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[210,150,90,220,150,216,143,150,240,230,136,150]}]};for(var d=[],s=0;s<30;s++)d[s]=29===s?"1 day ago":30-s+" days ago";e={labels:d,datasets:[{label:"This Month",fill:!0,backgroundColor:"rgba(255, 255, 255, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[50,210,110,90,230,130,190,75,155,120,140,230,50,210,110,90,230,130,155,120,140,230,50,210,110,90,230,130,190,75]},{label:"Last Month",fill:!0,backgroundColor:"rgba(255, 255, 255, .15)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[210,150,90,220,150,216,143,150,136,150,210,150,90,220,150,216,240,230,136,150,210,150,90,220,150,216,143,150,240,230]}]},l={labels:["MON","TUE","WED","THU","FRI","SAT","SUN"],datasets:[{label:"This Week",fill:!0,backgroundColor:"rgba(255, 255, 255, .5)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[34,42,62,78,39,83,98]},{label:"Last Week",fill:!0,backgroundColor:"rgba(255, 255, 255, .15)",borderColor:"transparent",pointBackgroundColor:"rgba(255, 255, 255, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255, 255, 255, 1)",data:[130,95,125,160,187,110,143]}]},n.length&&(a=new Chart(n,{type:"line",data:r,options:o})),jQuery('[data-toggle="dashboard-chart-set-week"]').on("click",(function(){a.data.labels=l.labels,a.data.datasets[0]=l.datasets[0],a.data.datasets[1]=l.datasets[1],a.update()})),jQuery('[data-toggle="dashboard-chart-set-month"]').on("click",(function(){a.data.labels=e.labels,a.data.datasets[0]=e.datasets[0],a.data.datasets[1]=e.datasets[1],a.update()})),jQuery('[data-toggle="dashboard-chart-set-year"]').on("click",(function(){a.data.labels=t.labels,a.data.datasets[0]=t.datasets[0],a.data.datasets[1]=t.datasets[1],a.update()}))}},{key:"init",value:function(){this.initChartsMain()}}],null&&a(r.prototype,null),t&&a(r,t),o}();jQuery((function(){o.init()}))}();