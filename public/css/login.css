@charset "utf-8";
*{margin:0; padding:0;-webkit-font-smoothing:antialiased;}
div{}
.header{padding:20px 0;}
.header .logo{background: url(../favicon.ico) no-repeat ;background-size:38px 38px;padding-left:40px;}
.header .logo a{text-decoration: none;color: #000;font-size: 32px;letter-spacing: -2px; font-weight: bolder;}

.login{overflow: hidden;height:500px;position: relative;/*background: #5597CE;*/
  box-shadow: 0px 1px 3px 2px #CDCDCD;
  -moz-box-shadow: 0px 1px 3px 2px #CDCDCD;
  -webkit-box-shadow: 0px 1px 3px 2px #CDCDCD;
  background: linear-gradient(330deg, #5597CE 0%, #6AACE3 40%, #7DBFF6 100%);
  background: -webkit-linear-gradient(330deg, #5597CE 0%, #6AACE3 40%, #7DBFF6 100%);
}
.login .box{padding:22px;background: #FFFFFF;z-index:1000;border:1px solid #FCFCFC;}
.login .box .title{font-size: 15px;height:22px;}
.login .box .input-group{margin-bottom: 22px;}
.login .box .input-group i{width:15px;}
.login .box input{height:40px;border-radius: 0;}
.login .box span{border-radius: 0;}
.login .box button{margin-top: 15px;border-radius: 0;}
.login .box button b{font-size: 16px;}
.login .box input[name="password"]{border-right:none;}

.login .box .showpassword{cursor:pointer;background: none;}

#SafetyCode{height:35px;}
.login .box a.regist{font-size: 15px;color: #3D84C1;}
.login .box a.forgot{font-size: 12px;line-height:55px;height:35px;color: #666;}
.login .box label{border:none;box-shadow:none;padding-left:0;}

#show{height:500px;width:100%;float: left;z-index:1;position:absolute;top:0;left:0;}

.login .content{border-radius: 3px;z-index:1000;transition:all 2s;}
.login .content .content-title{height:50px; line-height: 50px;border-bottom:1px solid #F0F0F0;font-weight: bolder;font-size: 14px; padding:0 15px;color:#666;background: rgba(255,255,255,0.8);/*background:transparent;*/}
.login .content .content-main{padding:10px 5px;background: rgba(255,255,255,1);height:300px;overflow-y:auto; }
.login .content .content-main li{padding:10px 5px;border-bottom:1px dotted #CCC;}

.blur{   
-webkit-filter: blur(2px); /* Chrome, Opera */
-moz-filter: blur(2px);
-ms-filter: blur(2px);   
filter: blur(2px);
}

.footer{text-align: center;padding:15px 0;}
.footer a{text-decoration: none;color: #666;font-size: 12px;}
.footer .menu a{padding:0px 15px;border-right:1px solid #999;}
.footer p{font-size: 12px;color: #666;margin-top:5px;}
.footer .menu a:hover{text-decoration:underline;color: #5FA1DC;}
.footer .menu a:last-child{border:none;}

.login-mini{overflow: hidden;position: relative;}
.login-mini{padding:10px 20px 0 20px;background: #FFFFFF;z-index:1000;}
.login-mini .title{font-size: 15px;height:22px;}
.login-mini .input-group{margin-bottom: 10px;}
.login-mini .input-group i{width:15px;}
.login-mini input{height:40px;border-radius: 0;}
.login-mini span{border-radius: 0;}
.login-mini button{margin-top: 10px;border-radius: 0;}
.login-mini button b{font-size: 16px;}
.login-mini input[name="password"]{border-right:none;}

.login-mini .showpassword{cursor:pointer;background: none;}

#SafetyCode{height:35px;}
.login-mini a.regist{font-size: 15px;color: #3D84C1;}
.login-mini a.forgot{font-size: 12px;line-height:55px;height:35px;color: #666;}
.login-mini label{border:none;box-shadow:none;padding-left:0;}

@media (min-width: 768px) {
  .header{padding-left:200px;}
  .login .box {width: 350px;position:absolute;top:60px;right:200px;}
  .login .content {width:38%;position: absolute;top:60px;left:150px;}
}
@media (max-width: 1119px) {
  .login .content{display: none;}
}
@media (max-width: 767px) {
  #show{display: none;}
}
@media (max-width: 480px) {
  .header{text-align:center;width:380px;}
  .footer{width:380px;}
  .footer p:last-child{display: none;}
  .login{width: 380px;margin:0 auto;background: none;}
  .login .box{margin-top:50px;}

}