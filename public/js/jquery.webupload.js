/**
 * 示例
 * <div id="upload" class="uploader-list"></div>
 * $('#upload').webupload(options,callbacks);
 * options:所有参数沿用WebUploader参数，格式如下：
 * {
 * 	swf: baseurl+"js/webuploader/Uploader.swf",
    server: baseurl+"common/webupload",
    resize:true,
    ...
 * }
 * callbacks:所有回调沿用WebUploader回调，格式如下：
 * {
 * 	success:function(file, response){},
 * 	complete:function(file){},
 * 	error:function(file,reason){},
 * 	accept:function(file, response){},
 * 	progress:function(file, percentage){},
 * }
 * @param  {[type]} $ [description]
 * @return {[type]}   [description]
 */

(function ($) {
	$.fn.extend({
	    webupload: function (options,callbacks) {
	    	$this = this;
			let key = $(this).attr('id')
			if(!key){
				console.error('缺少id');
				return error(this);
			}

	    	if(!baseurl){
				console.error('缺少必要参数');
				return error(this);
			}
	    	var opts = $.extend({}, getDefaluts(key), options);
	    	var cbs = $.extend({}, getDefaultCallback(key), callbacks);

	        createNode($this, key);

	        loadLib($this);

	        return haddle($this,opts,cbs);
	    }
	});

	const getDefaluts = key=>{
		return {
			swf: baseurl+"js/webuploader/Uploader.swf",
			server: baseurl+"common/webupload",
			pick: {
				id: '#'+key+'picker',
				multiple:true,
				label: '选择文件'
			},
			auto:true,
			fileVal:'file[]',
			fileNumLimit:5,
			fileSingleSizeLimit:'500KB',
			// 不压缩image, 默认如果是jpeg，文件上传前会压缩一把再上传！
			resize: true,
			formData:{},
			accept:{
				title: 'PDF',
				extensions: 'jpg,doc,docx,xls,xlsx,ppt,pptx,pdf,png',
				mimeTypes: '.jpg,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.png'
			}
		}
	}

	const getDefaultCallback = key=>{
		return {
			success:function(file, response){
				$( '#'+key+'_'+file.id ).find('p.state').text('已上传');
				setTimeout(function(){
					$('#'+key+'_' + file.id + '').fadeOut();
				},2000);
			},
			complete:function( file ) {
				$( '#'+key+'_'+file.id ).find('.progress').fadeOut();
				
				$( '#'+key+'_'+file.id ).find('p.state').text('已上传');
				setTimeout(function(){
				  $( '#'+key+'_'+file.id ).fadeOut();
				},2000);
			},
			uploadError:function( file,reason) {
				$( '#'+key+'_'+file.id ).find('p.state').text('上传出错');
			},
			error:function( code, file) {
				switch (code) {
					case 'Q_TYPE_DENIED':
						showError("请上传正确的文件格式："+this.options.accept[0]['extensions']);
						break;
					case 'Q_EXCEED_NUM_LIMIT':
						showError("上传文件数量超过限制");
						break;
					case 'Q_EXCEED_SIZE_LIMIT':
						showError("累计上传文件体积过大，不能超过："+_sizeFormat(this.options.fileSizeLimit));
						break;
					case 'F_DUPLICATE':
						showError("请不要重复选择文件");
						break;
					case 'F_EXCEED_SIZE':
						showError("上传文件体积过大，不能超过："+_sizeFormat(this.options.fileSingleSizeLimit));
						break;
				}
			},
			accept:function( file, response ) {
				var data = $.parseJSON(response._raw);
				if ( data.hasError ) {
					showMsg(data.msg,{icon:2});
					$('#'+key+'_' + file.id + '').fadeOut();
					return false;
				}
			},
			progress:function( file, percentage ) {
				var $li = $( '#'+key+'_'+file.id ),
					$percent = $li.find('.progress-bar');
				// 避免重复创建
				if ( !$percent.length ) {
					$percent = $('<div class="progress-bar progress-bar-striped progress-bar-animated bg-info">' +
						'<span class="font-size-sm font-w600">'+(percentage * 100)+'%</span>' +
						'</div>').appendTo( $li ).find('.progress-bar');
				}
	
				$li.find('p.state').text('上传中');
	
				$percent.css( 'width', percentage * 100 + '%' );
				$percent.find('span').text((percentage * 100).toFixed(2) + '%' );
			},
			// progress:function( file, percentage ) {
			//     var $li = $( '#'+key+'_'+file.id ),
			//         $percent = $li.find('.progress .progress-bar');
			//     // 避免重复创建
			//     if ( !$percent.length ) {
			//         $percent = $('<div class="progress progress-striped active">' +
			//             '<div class="progress-bar" role="progressbar" style="width: 0%">' +
			//             '</div>' +
			//             '</div>').appendTo( $li ).find('.progress-bar');
			//     }
			//
			//     $li.find('p.state').text('上传中');
			//
			//     $percent.css( 'width', percentage * 100 + '%' );
			// },
			queued:function( file ) {
				$('#'+key+'_list').append( '<div id="'+key+'_' + file.id + '" class="item">' +
					'<h4 class="info">' + file.name + '</h4>' +
					'<p class="state">等待上传...</p>' +
					'</div>' );
			}
		}
	}

	var baseurl = $('#baseUrl').val();

	/**
	 * <AUTHOR>
	 * @DateTime  2018-10-25
	 * @copyright 创建节点
	 * @license   [license]
	 * @version   [version]
	 * @param     {[type]}    _this [description]
	 * @return    {[type]}          [description]
	 */
	function createNode(_this, key){
		var html = '<div class="btns"><div id="'+key+'picker">选择文件</div></div><div id="'+key+'_list" class="uploader-list"></div>';
		_this.html(html);
	}

	function loadLib(_this){
		var libs='';
		libs += '<link rel="stylesheet" type="text/css" href="'+baseurl+'js/webuploader/webuploader.css">';
		libs += '<script type="text/javascript" src="'+baseurl+'js/webuploader/webuploader.nolog.min.js"></script>';
		_this.parent().append(libs);
	}

    /**
     * 执行
     * @param  {[type]} opts [description]
     * @return {[type]}      [description]
     */
	function haddle(_this,opts,cbs){
		if(typeof WebUploader=='undefined') return error(_this,'控件未正常加载');
		uploader = WebUploader.create(opts).on('ready', function () {
	        $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
	    });

	    uploader.on('fileQueued', cbs.queued);
	    uploader.on('uploadProgress', cbs.progress);
	    uploader.on('uploadSuccess', cbs.success);
	    uploader.on('uploadError', cbs.uploadError);
	    uploader.on('error', cbs.error);
	    uploader.on('uploadComplete', cbs.complete);
	    uploader.on('uploadAccept', cbs.accept);

		return uploader
	}

	/**
	 * <AUTHOR>
	 * @DateTime  2018-10-25
	 * @copyright 报错处理
	 * @license   [license]
	 * @version   [version]
	 * @return    {[type]}    [description]
	 */
	function error(_this,message){
		if(typeof message=='undefined' || !message)
			message = '控件加载失败';
		_this.html(message);
		return false;
	}

	/**
	 * <AUTHOR>
	 * @DateTime  2018-10-26
	 * @copyright 产生随机key
	 * @license   [license]
	 * @version   [version]
	 * @param     {[type]}    l [description]
	 * @return    {[type]}      [description]
	 */
	function generateKey(l){
		var  x="0123456789qwertyuioplkjhgfdsazxcvbnm";
		var  tmp="";
		for(var i=0;i<l;i++)  {
			tmp  +=  x.charAt(Math.ceil(Math.random()*100000000)%x.length);
		}
		return tmp;
	}	
})(jQuery);