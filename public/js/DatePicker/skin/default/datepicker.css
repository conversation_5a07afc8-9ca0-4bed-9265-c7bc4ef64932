/**
* yyh & kimi from mb518.com
* 2019-3-25
**/
.WdateDiv {
    width: 216px;
    background-color: #FFF;
    border-radius: 5px;
    border: 1px solid #cecece;
}

.WdateDiv2 {
    width: 432px;
}

.WdateDiv > div:nth-child(3) {
    padding: 0px 0.375em;
}

.WdateDiv .NavImg a {
    cursor: pointer;
    display: block;
    width: 1em;
    height: 1em;
    margin-top: 0.5em;
}

.WdateDiv .NavImgll a {
    float: left;
    background: url(img.gif) no-repeat;
    background-size: 400%;
}

.WdateDiv .NavImgl a {
    float: left;
    background: url(img.gif) no-repeat -1em 0;
    background-size: 400%;
}

.WdateDiv .NavImgr a {
    float: right;
    background: url(img.gif) no-repeat -2em 0;
    background-size: 400%;
}

.WdateDiv .NavImgrr a {
    float: right;
    background: url(img.gif) no-repeat -3em 0;
    background-size: 400%;
}

.WdateDiv #dpTitle {
    height: 1.875em;
    padding: 0.1875em 0.3125em;
}

.WdateDiv #dpTitle > div:nth-child(3),
.WdateDiv #dpTitle > div:nth-child(4) {
    margin: 0px 0.8125em;
}

.WdateDiv .yminput {
    margin-top: 0.375em;
    text-align: center;
    border: 0px;
    height: 1.75em;
    width: 3.125em;
    color: #333;
    background-color: #eefaff;
    outline: none;
    cursor: pointer;
    font-size: 0.8125em;
    border-radius: 0;
}

.WdateDiv .yminputfocus {
    font-size: 0.8125em;
    margin-top: 0.375em;
    text-align: center;
    border: 0;
    color: #333;
    height: 1.25em;
    width: 3.125em;
    outline: none;
    background-color: #eefaff;
}

.WdateDiv .menuSel {
    z-index: 1;
    position: absolute;
    background-color: #FFFFFF;
    display: none;
    padding: 0.3125em;
    border-radius: 3px;
    box-shadow: rgb(204, 204, 204) 0px 0px 2px 2px;
}

.WdateDiv .menu {
    cursor: pointer;
    background-color: #fff;
    color: #333;
    text-align: center;
}

.WdateDiv .menuOn {
    cursor: pointer;
    text-align: center;
    background-color: #d6f2ff;
    border-radius: 3px;
}

.WdateDiv .invalidMenu {
    color: #aaa;
}

.WdateDiv .YMenu {
    margin-top: 1.875em;
}

.WdateDiv .MMenu {
    margin-top: 1.875em;
    box-shadow: rgb(204, 204, 204) 0px 0px 2px 2px;
    padding: 0.3125em;
    border-radius: 3px;
    *width: 3.875em;
}

.WdateDiv .hhMenu {
    margin-top: -5.625em;
    margin-left: 1.625em;
}

.WdateDiv .mmMenu {
    margin-top: -2.875em;
    margin-left: 1.625em;
}

.WdateDiv .ssMenu {
    margin-top: -1.5em;
    margin-left: 1.625em;
}

.WdateDiv .Wweek {
    text-align: center;
    background: #DAF3F5;
    border-right: #BDEBEE 1px solid;
}

.WdateDiv .MTitle {
    background-color: #f3f3f3;
    border-radius: 5px;
    color: #666;
}

.WdateDiv .WdayTable2 {
    border-collapse: collapse;
}

.WdateDiv .WdayTable2 table {
    border: 0;
}

.WdateDiv .WdayTable {
    font-size: 0.75em;
    line-height: 1.5em;
    color: #333;
}

.WdateDiv .WdayTable td {
    text-align: center;
    border-bottom: 1px solid #f3f3f3;
    padding: 0.1875em 0;
}

.WdateDiv .Wday {
    cursor: pointer;
}

.WdateDiv .WdayOn {
    cursor: pointer;
    border-radius: 3px;
    background-color: #C0EBEF;
}

.WdateDiv .Wwday {
    cursor: pointer;
    color: #ab1e1e;
}

.WdateDiv .WwdayOn {
    cursor: pointer;
    border-radius: 3px;
    background-color: #C0EBEF;
}

.WdateDiv .Wtoday {
    cursor: pointer;
    color: blue;
}

.WdateDiv .Wselday {
    background-color: #35baf6;
    border-radius: 3px;
    color: #fff;
}

.WdateDiv .WspecialDay {
    background-color: #66F4DF;
}

.WdateDiv .WotherDay {
    cursor: pointer;
    color: #8585e1;
}

.WdateDiv .WotherDayOn {
    cursor: pointer;
    background-color: #d6f2ff;
    border-radius: 3px;
}

.WdateDiv .WinvalidDay {
    color: #aaa;
}

.WdateDiv #dpTime {
    margin: 0.1875em 0 0.1875em 0.625em;
}

.WdateDiv #dpTime #dpTimeStr {
    margin-left: 0.0625em;
    color: #333;
    font-size: 0.75em;
}

.WdateDiv #dpTime table:nth-child(4) {
    font-size: 100%;
}

.WdateDiv #dpTime table:nth-child(-n+3) {
    font-size: 0.75em;
}

.WdateDiv #dpTime input {
    -webkit-appearance: none;
    font-size: 0.75em;
    height: 1.25em;
    width: 1.875em;
    text-align: center;
    color: #333;
    border: 0;
    background-color: #eefaff;
    border-radius: 0;
    padding: 0;
}

.WdateDiv #dpTime input:disabled {
    background-color: #e7e7e7;
}

.WdateDiv #dpTime .tB {
    border-right: 0px;
}

.WdateDiv #dpTime .tE {
    border-left: 0;
    border-right: 0;
}

.WdateDiv #dpTime .tm {
    width: 0.9375em;
    border-left: 0;
    border-right: 0;
}

/*.WdateDiv #dpTime button {
    font-size: 0.75em;
}*/

.WdateDiv #dpTime #dpTimeUp {
    height: 0.625em;
    width: 0.8125em;
    border: 0px;
    background: url(img.gif) no-repeat -2em -1em;
    background-size: 450%;
    overflow: hidden;
}

.WdateDiv #dpTime #dpTimeDown {
    height: 0.625em;
    width: 0.8125em;
    border: 0px;
    background: url(img.gif) no-repeat -2em -1.625em;
    background-size: 450%;
}

.WdateDiv #dpQS {
    float: left;
    margin-right: 0.1875em;
    margin-top: 0.3125em;
    background: url(img.gif) no-repeat 0px -1em;
    background-size: 320%;
    width: 1.25em;
    height: 1.25em;
    cursor: pointer;
    margin-left: 0.375em;
}

.WdateDiv #dpControl {
    text-align: right;
    margin-top: 0.1875em;
    padding: 0 0.3125em;
    padding-bottom: 0.1875em;
}

.WdateDiv .dpButton {
    font-size: 0.75em;
    -webkit-appearance: none;
    padding-top: 0;
    height: 1.75em;
    width: 2.8125em;
    border: 0;
    margin-top: 0.1875em;
    margin-right: 0.1875em;
    background: #35baf6;
    color: #fff;
    border-radius: 0;
}
