<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseProjectContents;

class ProjectContents extends BaseProjectContents
{
	function selectByProjectId($project_id='',$widget_name=''){
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `project_id` = '".$project_id."' AND `widget_name` = '".$widget_name."' ");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
			$this->setProjectId($project_id);
			$this->setWidgetName($widget_name);	
		}
        return $this;
    }
	
	/**
	 * 获取PDF文件
	 */
	function getPdfPathx($f=false)
	{
		if(parent::getPdfPath()) return parent::getPdfPath();
		//尝试转化
		if($word = parent::getFilePath()){
			$ext = strtolower(array_pop(explode('.',$word)));
			if($ext == 'pdf') $pdf = $word;
			else if(in_array($ext,array('doc','docx')) && $f) $pdf = sf::getLib("Dcs",site_path("up_files/".$word))->getPdfPath();
			else $pdf = '';
			if($pdf){
				$this->setPdfPath($pdf);
				$this->save();
				return $pdf;
			}	
		}
		return '';
	}
	
	/**
	 * 获取PDF文件
	 */
	function getPdfPath($f=false)
	{
		if($pdf_path = parent::getPdfPath()){
			$pdf_path_array = explode(':',$pdf_path);
			if(count($pdf_path_array)> 1) return $pdf_path_array[1];	
			else return $pdf_path;
		}else return '';
	}
	
	/**
	 * 获取PDF文件
	 */
	function getRealPdfPath($f=false)
	{
		if($pdf_path = parent::getPdfPath()){
			$pdf_path_array = explode(':',$pdf_path);
			if(count($pdf_path_array)> 1){
				if($f) return "http://".$pdf_path_array[0].'/up_files/'.$pdf_path_array[1];
				else return $pdf_path_array[1];	
			}else return $pdf_path;
		}
		//尝试转化
		if($word = parent::getFilePath()){
			$ext = strtolower(array_pop(explode('.',$word)));
			if($ext == 'pdf') $pdf = $word;
			else if(in_array($ext,array('doc','docx')) && $f){
				 if($this->getServerIp()) $sourceFile = "http://".$this->getServerIp().'/up_files/'.$word;
				 else $sourceFile = site_path("up_files/".$word);
				$pdf = sf::getLib("Dcs",$sourceFile)->getPdfPath();
			}else $pdf = '';
			//保存起来
			if($pdf){
				$server_ip = trim($_SERVER['SERVER_ADDR']);
				$this->setPdfPath($server_ip.':'.$pdf);
				$this->save();
				return $pdf;
			}	
		}
		return '';
	}

    function deleteFile($file='')
    {
        $file = $file ? $file : parent::getFilePath();
        $file = WEBROOT."/up_files/".$file;
        if(is_file($file)) return @unlink($file);
        else return false;
    }

	public function delete()
    {
        $this->deleteFile();
        parent::delete();
    }
}