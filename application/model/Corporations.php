<?php
namespace App\Model;
use Sofast\Core\router;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\Config;
use App\Model\BaseCorporations;
use App\Facades\Button;
use Sofast\Support\Pager;

class Corporations extends BaseCorporations
{
    private $user                   = NULL;
    private $department             = NULL;
    private $assets                 = NULL;
    private $staffs                 = NULL;
    private $products               = NULL;
    private $projects               = NULL;
    private $corporation_statistics = NULL;

    function selectByUserId($user_id = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `user_id` = '" . $user_id . "' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setUserId($user_id ? $user_id : sf::getLib("MyString")->getRandString());
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }
    function selectByZdsysUserId($zdsysUserId = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `zdsys_user_id` = '" . $zdsysUserId . "' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setZdsysUserId($zdsysUserId);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    function selectByCode($code = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `code` = '" . $code . "' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setCode($code);
        return $this;
    }

    function selectByUnitId($unitId)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `unit_id` = '" . $unitId . "' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setUnitId($unitId);
        return $this;
    }

    function selectBySubject($subject = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `subject` = '" . $subject . "' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setSubject($subject);
        return $this;
    }

    function hasByCode($code = '')
    {
        if ($card_id == '') return true;
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `code` = '" . $code . "' ");
        if ($db->num_rows($query)) return true;
        else return false;
    }

    public function getUser($f = false)
    {
        if ($this->user === NULL || $f)
            $this->user = sf::getModel("Users")->selectByUserId($this->getAccountId());
        return $this->user;
    }

    public function getTalents($type='docter',$f = false)
    {
        if ($this->talent[$type] === NULL || $f)
            $this->talent[$type] = sf::getModel("CorporationTalents")->selectByCompanyId($this->getUserId(),$type);
        return $this->talent[$type];
    }

    function getAccountId()
    {
        $db = sf::getLib("db");
        $sql = "select user_id from user_roles where user_role_id = '" . $this->getUserId() . "' and role_id = '3' ";
        $row = $db->fetch_first($sql);
        return $row['user_id'] ? $row['user_id'] : $this->getUserId();
    }

    /**
     * 是否有项目
     */
    function hasProject($type='',$catIds=[],$sujectCode='')
    {
        $addWhere = "corporation_id = '" . parent::getUserId() . "' ";
        if($catIds) {
            $addWhere.=" and cat_id IN (".implode(',',$catIds).")";
        }
        if($sujectCode) $addWhere.=" and subject_code = '{$sujectCode}'";
        switch(strtoupper($type))
        {
            case 'COMPLETE':
                $addWhere .= "AND statement IN (29,30) ";
                break;
            default:
                break;
        }

        if (sf::getModel("Projects")->selectAll($addWhere, 'order by id desc', 1)->getTotal()) return true;
        else return false;
    }

    public function cleanObject()
    {
        $this->user = NULL;
        parent::cleanObject();
    }

    public function remove($ids = '')
    {
        return false;//屏蔽该方法
        return parent::remove("user_id in('" . $ids . "')");
    }

    public function delete()
    {
        if ($this->hasProject()) return false;
        sf::getModel("UserRoles")->removeRole($this->getUserId(), 3);//删除角色
        return parent::delete();
    }

    /**
     * 取得快到期的项目列表
     */
    function selectExpires()
    {
        return sf::getModel("Projects")->selectAll("corporation_id = '".$this->getUserId()."' and `statement` = 29 and real_end_at < '".date("Y-m-d",time() + 30*24*60*60)."' and real_end_at > '".date("Y-m-d")."'");
    }

    /**
     * 取得已到期的项目列表
     */
    function selectHasExpires()
    {
        return sf::getModel("Projects")->selectAll("corporation_id = '".$this->getUserId()."' and `statement` = 29 and real_end_at <= '".date("Y-m-d")."' ");
    }

    /**
     * 取得该填写季度报告的项目数量
     */
    function getNeedQuartersCount()
    {
        $db = sf::getLib('db');
        //该写季度报告的项目数量
        $count = $db->result_first("select count(*) c from projects where corporation_id = '".$this->getUserId()."' and `statement` = 29 and (project_type in (3782,3783,3786) or cat_id = 16)");
        //已填写的数量
        $season = (int)ceil((date('n'))/3)-1;//上季度是第几季度
        $year = date('Y', mktime(0, 0, 0,$season*3-3+1,1,date('Y')));
        $hasCount = $db->result_first("select count(*) c from project_quarters where company_id = '".$this->getUserId()."' and `year` = '{$year}' and `quarter` = '{$season}'");

        return $count-$hasCount;
    }

    public function decoupling($userid = '')
    {
        if (!$userid) return false;
        return sf::getModel("UserRoles")->remove("user_role_id = '" . $this->getUserId() . "' and role_id = '3' AND user_id = '" . $userid . "' ");
    }

    public function coupling($userid = '')
    {
        if (!$userid) return false;
        $userrole = sf::getModel("UserRoles")->selectByRole($userid, 3);
        $userrole->setUserRoleId($this->getUserId());
        return $userrole->save();
    }

    /**
     * 设置默认管理员
     */
    public function setManager($userid = '')
    {
        if (!$userid) return false;
        $user = sf::getModel("Users")->selectByUserId($userid);
        if ($user->isNew()) return false;
        //更新联系人信息
        $this->setLinkman($user->getUserUsername());
        $this->setMobile($user->getUserMobile());
        $this->setLinkmanEmail($user->getUserEmail());
        $this->setPhone($user->getUserPhone());
        $this->setManagerUserId($user->getUserId());
        $this->setUpdatedAt(date('Y-m-d H:i:s'));
        $this->save();//保存结果
        return true;
    }

    public function isManager($userid = '')
    {
        if (!$userid) return false;
        if ($this->getManagerUserId() == $userid) return true;
        else return false;
    }

    public function getManagerUserId()
    {
        if (parent::getManagerUserId()) return parent::getManagerUserId();
        else return $this->getUserId();
    }

    public function getDepartmentName()
    {
        return sf::getModel("departments")->selectByUserId(parent::getDepartmentId())->getSubject();
    }

    public function getDepartmentShortName()
    {
        return sf::getModel("departments")->selectByUserId(parent::getDepartmentId())->getShortName();
    }

    public function getParentName()
    {
        return sf::getModel("corporations")->selectByUserId(parent::getParentId())->getSubject();
    }

    public function getCorporationPersons()
    {
        return sf::getModel("CorporationPersons")->selectByUserId(parent::getUserId());
    }


    function isHas($code = '')
    {
        if (strlen($code) > 10) {             //社会信用代码
            $code_first = substr($code, 8, 8);//前8位
        } else {                              //组织机构代码
            $code_first = substr($code, 0, 8);//前8位
        }
        if ($this->selectAll("(code = '" . $code . "' OR code LIKE '%" . $code_first . "%') AND user_id <> '" . $this->getUserId() . "'")->getTotal()) return true;
        else return false;
    }

    function isHave($subject = '', $department_id = '')
    {
        $addWhere = "subject = '" . trim($subject) . "' and department_id = '" . $department_id . "'";
        if ($this->selectAll($addWhere)->getTotal()) return true;
        else return false;
    }

    function hasUser($userName = '')
    {
        return $this->getUser(true)->hasUser($userName);
    }

    function getStateByYear($year = '')
    {
        if (!$year) $year = date('Y');
        $statistics = sf::getModel('CorporationStatistics')->getStateByYear(parent::getUserId(), $year);
        if ($statistics->isNew())
            return '未填写';
        else return $statistics->getState();
    }

    function getState()
    {
        switch (parent::getIsLock()) {
            case 0:
                return '<span class="badge badge-success">已认证</span>';
            case 1:
                return '<span class="badge badge-warning">待主管部门审核</span>';
            case 2:
                return '<span class="badge badge-danger">主管部门退回</span>';
            case 3:
                return '<span class="badge badge-danger">认证退回</span>';
            case 4:
                return '<s style="color:#F00;">列入黑名单</s>';
            case 5:
                return '<span class="badge badge-warning">待科技厅审核</span>';
            case 6:
                return '<span class="badge badge-warning">更新证件待核</span>';
            case 7:
                return '<span style="color:#F93;">未认证</span>';
            case 9:
                return '<span class="badge badge-info">资料修改中</span>';
            default:
                return '<s style="color:#F00;">未知状态</s>';
        }
    }

    //基本信息审核状态
    function getStatemen()
    {
        switch (parent::getStatement()) {
            case 1:
                return '<span class="badge badge-warning">完善中</span>';
                break;
            case 11:
                return '<span class="badge badge-info">等待审核</span>';
                break;
            case 7:
                return '<span class="badge badge-danger">审核退回</span>';
                break;
            case 10:
                return '<span class="badge badge-success">审核通过</span>';
                break;
            default:
                return '<s style="color:#F00;">待完善</s>';
                break;
        }
    }

    //经费情况审核状态
    function getStateForMone()
    {
        switch (parent::getStateForMoney()) {
            case 1:
                return '<label class="label label-warning">完善中</label>';
                break;
            case 11:
                return '<label class="label label-info">等待审核</label>';
                break;
            case 7:
                return '<label class="label label-danger">审核退回</label>';
                break;
            case 10:
                return '<label class="label label-success">审核通过</label>';
                break;
            default:
                return '<s style="color:#F00;">待完善</s>';
                break;
        }
    }

    //人才情况审核状态
    function getStateForPerso()
    {
        switch (parent::getStateForPerson()) {
            case 1:
                return '<label class="label label-warning">完善中</label>';
                break;
            case 11:
                return '<label class="label label-info">等待审核</label>';
                break;
            case 7:
                return '<label class="label label-danger">审核退回</label>';
                break;
            case 10:
                return '<label class="label label-success">审核通过</label>';
                break;
            default:
                return '<s style="color:#F00;">待完善</s>';
                break;
        }
    }

    //管理层级
    function getManageLeve()
    {
        switch (parent::getManageLevel()) {
            case 1:
                return '一级企业';
                break;
            case 2:
                return '二级企业';
                break;
            case 3:
                return '三级企业';
                break;
            case 4:
                return '四级企业';
                break;
            case 5:
                return '五级及以上企业';
                break;
            default:
                return '';
                break;
        }
    }

    //是否已通过高企认定
    function getIsHightec()
    {
        switch (parent::getIsHightech()) {
            case 1:
                return '已认定企业';
                break;
            case 2:
                return '培育企业';
                break;
            default:
                return '';
                break;
        }
    }

    /**
     *等待审核的项目数
     */
    function getWaitNum()
    {
        $addWhere = "statement IN (2,21) and `corporation_id` = '".input::session('roleuserid')."'";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的基本资料数
     */
    function getBaseinfoWaitNum()
    {
        $addWhere = "statement = 2 and `corporation_id` = '".input::session('roleuserid')."'";
        return sf::getModel("Platforms")->selectAll($addWhere)->getTotal();

    }

    /**
     *已立项的项目数
     */
    function getWaitNum1()
    {
        $addWhere = "statement = 29 and `corporation_id` = '".input::session('roleuserid')."'";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();

    }

    /**
     *已结题的项目数
     */
    function getWaitNum2()
    {
        $addWhere = "statement = 31 and `corporation_id` = '".input::session('roleuserid')."'";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的预算书数
     */
    function getBudgetWaitnum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and state_for_budget_book = 10")->getTotal();

    }

    /**
     *等待审核的计划任务书数
     */
    function getTaskWaitnum()
    {
        $addWhere = "state_for_plan_book = 2 and corporation_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的年度考核
     */
    function getSummaryWaitnum()
    {
        $addWhere = "statement = 2 and company_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Summarys")->selectAll($addWhere)->getTotal();
    }


    /**
     *等待审核的中期报告数
     */
    function getStageWaitnum()
    {
        $addWhere = "statement = 2 and company_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Stages")->selectAll($addWhere)->getTotal();
    }

    /**
     *等待审核的年度考核数
     */
    function getReviewWaitnum()
    {
        $addWhere = "statement = 2 and company_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Reviews")->selectAll($addWhere)->getTotal();
    }

    /**
     *等待审核的季度监测数
     */
    function getReviewquarterWaitnum()
    {
        $addWhere = "statement = 2 and company_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Reviewquarters")->selectAll($addWhere)->getTotal();
    }

    /**
     *等待审核的科技奖励数
     */
    function getAwardWaitNum()
    {
        if (isParent(input::getInput("session.roleuserid"))) {
            $addWhere = " (statement = 5 and corporation_id in (select user_id from corporations where parent_id = '" . input::getInput("session.roleuserid") . "')) or (statement = 2 and corporation_id = '" . input::getInput("session.roleuserid") . "')";
        } else {
            $addWhere = "statement = 2 and corporation_id = '" . input::getInput("session.roleuserid") . "' ";
        }
        return sf::getModel("Awards")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的科技奖励数
     */
    function getRewardWaitNum()
    {
        $addWhere = "statement = 2 and corporation_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Rewards")->selectAll($addWhere)->getTotal();

    }
    /**
     *审核通过的科技奖励数
     */
    function getRewardWaitNum1()
    {
        $addWhere = "statement = 10 and corporation_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Rewards")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的中期报告数
     */
    function getZqbgWaitnum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and statement >= 0 and state_for_interimreport = 1")->getTotal();

    }

    /**
     *等待审核的验收书数
     */
    function getCompleteWaitnum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and statement = 29 and state_for_complete_book = 2")->getTotal();

    }

    /**
     *待审核人员数
     */
    function getDeclareWaitnum()
    {
        return sf::getModel("declarers")->selectAll("corporation_id = '" . $this->getUserId() . "' and `is_lock` = 1")->getTotal();

    }

    /**
     *已注册的人员数
     */
    function getDeclareNum()
    {
        return sf::getModel("declarers")->selectAll("corporation_id = '" . $this->getUserId()."'")->getTotal();
    }

    /**
     *已认证人员数
     */
    function getDeclareSubmitNum()
    {
        return sf::getModel("declarers")->selectAll("corporation_id = '" . $this->getUserId() . "' and `is_lock` = 0")->getTotal();

    }

    /**
     *等待审核的专家数
     */
    function getExpertWaitnum()
    {
        return sf::getModel("Experts")->selectAll("corporation_id = '" . $this->getUserId() . "' and `is_lock` = 7")->getTotal();

    }
    /**
     *已注册的专家数
     */
    function getExpertNum()
    {
        return sf::getModel("Experts")->selectAll("corporation_id = '" . $this->getUserId() . "'")->getTotal();

    }
    /**
     *已认证的专家数
     */
    function getExpertSubmitNum()
    {
        return sf::getModel("Experts")->selectAll("corporation_id = '" . $this->getUserId() . "' and `is_lock` = 0")->getTotal();

    }

    /**
     *驳回的预算申报书数
     */
    function getBudgetRejectednum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and `state_for_budget_book` = 2")->getTotal();

    }

    /**
     *驳回的计划任务书数
     */
    function getTaskRejectednum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and statement = 29 and `state_for_plan_book` = 3")->getTotal();

    }

    /**
     *驳回的验收书数
     */
    function getCompleteRejectednum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and statement = 29 and `state_for_complete_book` = 3")->getTotal();

    }

    /**
     *等待审核的项目外协申请
     */
    function getProjectOutNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `corporation_id`  = '".$this->getUserId()."' and `state_for_out` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的项目外协申请
     */
    function getPaymentNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_payments WHERE `corporation_id`  = '".$this->getUserId()."' and `is_lock` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的新产品数
     */
    function getProductNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的科技著作数
     */
    function getWorkNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *审核通过的科技著作数
     */
    function getAcceptWorkNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 10 ");
        return $row['num'];
    }

    /**
     *等待审核的科技论文数
     */
    function getPaperNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }
    function getAcceptPaperNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 10 ");
        return $row['num'];
    }

    /**
     *等待审核的工法数
     */
    function getMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *审核通过的工法数
     */
    function getAcceptMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 10 ");
        return $row['num'];
    }

    /**
     *等待审核的专利成果数
     */
    function getPatentNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的著作权数
     */
    function getSoftNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的奖励数
     */
    function getRewardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE statement = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的文章数
     */
    function getArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getStandardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM standards WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的季度执行报告
     */
    function getQuarterWaitnum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_quarters WHERE company_id = '".$this->getUserId()."' and statement = 2");
        return $row['num'];
    }

    /**
     *等待依托单位审核的变更申请
     */
    function getChangeWaitnum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platform_changes WHERE company_id = '".$this->getUserId()."' and statement = 2");
        return $row['num'];
    }

    /**
     *审核通过的专利成果数
     */
    function getAcceptPatentNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 10 ");
        return $row['num'];
    }

    /**
     * 已审核的新产品数
     */
    function getAcceptProductNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE corporation_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
    /**
     * 已审核的科普文章数
     */
    function getAcceptArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles WHERE corporation_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }

    /**
     * 已审核的软著数
     */
    function getAcceptSoftNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs WHERE corporation_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }



    function getCode($len = 0, $is_verify = false)
    {
        if (!$is_verify) return parent::getCode();

        if (isUnitCode(parent::getCode())) return parent::getCode();
        elseif (isCreditCode(parent::getCode())) return parent::getCode();
        else return '<s style="color:red">' . parent::getCode() . '</s>';
    }

    /**
     * 检查身份证是否正确
     */
    function getVerify()
    {
        if (isUnitCode(parent::getCode())) return true;
        elseif (isCreditCode(parent::getCode())) return true;
        else return false;
    }

    function selectHistorys($showMax = 0)
    {
        return sf::getModel("historys")->selectAll("project_id = '" . $this->getUserId() . "' ", 'ORDER BY `updated_at` DESC', $showMax);
    }

    function getAttachments()
    {
        return sf::getModel("filemanager")->selectAll("item_id = '" . $this->getUserId() . "' AND item_type IN ('company','corporation')", "ORDER BY created_at ASC");
    }

    /**
     * 取得项目列表
     */
    function selectProject($num = 0)
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "'", "ORDER BY updated_at DESC", $num);
    }

    /**
     * 取得负责人列表
     */
    function selectDeclarers($num = 0)
    {
        return sf::getModel("Declarers")->selectAll("corporation_id = '" . $this->getUserId() . "'", "ORDER BY updated_at DESC", $num);
    }

    /**
     * 取得专家列表
     */
    function selectExperts($num = 0)
    {
        return sf::getModel("Experts")->selectAll("corporation_id = '" . $this->getUserId() . "'", "ORDER BY updated_at DESC", $num);
    }

    /**
     * 取得项目数量
     */
    function getProjectCount()
    {
        return sf::getLib("db")->result_first("select count(*) c from projects where corporation_id = '" . $this->getUserId() . "'");
    }

    /**
     * 取得项目负责人数量
     */
    function getDeclarerCount()
    {
        return sf::getLib("db")->result_first("select count(*) c from declarers where corporation_id = '" . $this->getUserId() . "'");
    }

    /**
     * 取得专家数量
     */
    function getExpertCount()
    {
        return sf::getLib("db")->result_first("select count(*) c from experts where corporation_id = '" . $this->getUserId() . "'");
    }

    /**
     * 验证信息是否完整
     */
    public function validate()
    {
        $message = array();

        if (!$this->getSubject()) $message[] = '【基本信息】中的【单位名称】必须正确填写；';
        // if(!$this->getVerify()) 		$message[] = '申报单位机构代码必须正确填写！';
//        if (!$this->getParentId()) $message[] = '【基本信息】中的【上级单位】必须填写；';
        if (!trim($this->getProperty())) $message[] = '【基本信息】中的【单位性质】必须填写；';
        if (!trim($this->getType())) $message[] = '【基本信息】中的【医院类别】必须填写；';
        if (!trim($this->getLevel())) $message[] = '【基本信息】中的【医院等次】必须填写；';
        if (!trim($this->getPrincipal())) $message[] = '【基本信息】中的【法人代表姓名】必须填写；';
        if (!$this->getAreaCode()) $message[] = '【基本信息】中的【所属地区】必须选择！';
        if (substr($this->getAreaCode(),-2)=='00') $message[] = '【基本信息】中的【所属地区】必须选择到最后一级！';
        if (!$this->getPostalcode()) $message[] = '【基本信息】中的【邮政编码】必须填写！';
        if (!trim($this->getAddress())) $message[] = '【基本信息】中的【单位地址】必须填写；';
        if (!trim($this->getBankName())) $message[] = '【基本信息】中的【开户银行】必须填写；';
        if (!trim($this->getBankId())) $message[] = '【基本信息】中的【银行卡账号】必须填写；';
        if ((int)$this->getBedcount()==0) $message[] = '【基本信息】中的【实际开放床位数】必须填写；';
        if ((int)$this->getFloorage()==0) $message[] = '【基本信息】中的【业务用房建筑面积】必须填写；';
        if (strlen($this->getPersoncount())==0) $message[] = '【基本信息】中的【在编人数】必须填写；';
        if (!trim($this->getLinkman())) $message[] = '【基本信息】中的【联系人】必须填写；';
        if (!isMobile($this->getMobile())) $message[] = '【基本信息】中的【联系人手机号码】必须正确填写；';
        if (!trim($this->getPhone())) $message[] = '【基本信息】中的【联系人座机】必须填写；';
        if (!trim($this->getLinkmanEmail())) $message[] = '【基本信息】中的【联系人电子邮箱】必须填写；';
        if ($this->getIsLock() == 4) $message[] = '申报单位被列入黑名单！';
         if($this->getAttachments()->getTotal()<2) 	$message[] = '请在上传单位认证的必要附件（事业单位法人证书扫描件(或营业执照扫描件)）！';
        $user = $this->getUser();
        if (!trim($user->getUserUsername())) $message[] = '【账号信息】中的【姓名】必须填写；';
        if (!trim($user->getUserIdcard())) $message[] = '【账号信息】中的【身份证号】必须填写；';
        if (!trim($user->getUserMobile())) $message[] = '【账号信息】中的【手机号码】必须填写；';
        if (!trim($user->getUserEmail())) $message[] = '【账号信息】中的【安全邮箱】必须填写；';

        //如果找不到上一年的统计信息或统计信息没有审核通过，则不允许上报项目(明年奶奶3月份)
        return $message;
    }

    /**
     * 发送短消息给用户
     */
    public function sendMessage($message = '', $item_id = '', $item_type = 'projects', $send_at = '')
    {
        if (!isMobile($this->getMobile())) return false;
        return sf::getModel("ShortMessages")->sendSms($this->getMobile(), $message, $item_id, $item_type, $send_at);
    }

    function selectByUserName($userName = '')
    {
        $this->cleanObject();//清空对象
        $db = sf::getLib("db");
        $result = $db->fetch_first("SELECT * FROM " . $this->table . "  WHERE user_id IN (SELECT user_id FROM unit_users WHERE user_name = '" . $userName . "') ");
        if ($result) $this->fillObject($result);
        return $this;
    }

    /**
     * 查找相识的用户
     */
    public function selectSameUser()
    {
        return $this->selectAll("user_id != '" . parent::getUserId() . "' AND (subject LIKE '%" . parent::getSubject() . "%' OR code = '" . parent::getCode() . "')", "ORDER BY id ASC");
    }

    function getRepeatCount()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM " . $this->table . " WHERE trim(code) = '" . $this->getCode() . "' ");
        return $row['num'];
    }

    /**
     * 是否锁定
     */
    function hasLock()
    {
        if (in_array($this->getIsLock(), [1, 4, 5, 6])) return true;
        return false;
    }

    /**
     * 获取集团公司单位
     * @return mixed
     */
    public function getDepartments()
    {
        return sf::getModel('Categorys', '', 'department')->selectAll();
    }

    public function getDepartmentType()
    {
        $obj = M('Departments')->selectAll("user_id = '{$this->getDepartmentId()}'")->getObject();
        if ($obj !== false) return $obj->getType();
        else return 0;
    }

    public function getCompanyProperty()
    {
        return $this->getProperty();
    }

    public function getDepartmentOption()
    {
        $type = $this->getDepartmentType();
        $departments = M('Departments')->selectAll("type = '{$type}'");
        $htmlStr = '';
        while ($item = $departments->getObject()) {
            $selected = ($item->getUserId() == $this->getDepartmentId()) ? 'selected' : '';
            $htmlStr .= "<option value={$item->getUserId()} {$selected}>{$item->getSubject()}</option>";
        }

        return $htmlStr;
    }

    public function getDepartmentModel($f = false)
    {
        if ($this->department === NULL || $f) $this->department = M('Departments')->selectAll("user_id = '{$this->getDepartmentId()}'")->getObject();
        return $this->department;
    }

    /**
     * 根据权限定义操作菜单
     */
    function getCustomButton($type = 'show')
    {
        $htmlStr = Button::back();
        //$htmlStr .= Button::window('查看记录', site_url("admin/history/index/type/corporations/id/" . $this->getUserId()), 'time', 500, 360);
        $htmlStr .= Button::setName('查看记录')->setUrl(site_url("admin/history/index/type/corporations/id/" . $this->getUserId()))->setWidth('550px')->setHeight('80%')->window();
        switch (input::getInput("session.userlevel")) {
            case 1:
                if ($this->getIsLock() != 0)
                    $htmlStr .= Button::link("认证", site_url("admin/corporation/dosubmit/userid/" . $this->getUserId()), "doit");
//                $htmlStr .= Button::window("退回", site_url("admin/corporation/doBack/userid/" . $this->getUserId()), "delete", 500, 360, 'btn-sm', 'btn-danger');
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("admin/corporation/doBack/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("发送短消息", site_url("message/short/edit/item_type/experts/item_id/" . $this->getUserId() . '/mobile/' . $this->getMobile()), "send", 400, 300);
                $htmlStr .= Button::setName('发送短消息')->setUrl(site_url("message/short/edit/item_type/experts/item_id/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("用户合并", site_url("admin/corporation/more/userid/" . $this->getUserId()), "mark", 600, 460);
//                $htmlStr .= Button::setName('用户合并')->setUrl(site_url("admin/corporation/more/userid/" . $this->getUserId()))->setWidth('550px')->setHeight('80%')->window();
//                $htmlStr .= Button::window("编辑用户", site_url("admin/corporation/edit/userid/" . $this->getUserId()), "edit", 600, 460);
                $htmlStr .= Button::setName('编辑用户')->setUrl(site_url("admin/corporation/edit/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window('打入黑名单', site_url("admin/corporation/doBlack/userid/" . $this->getUserId()), 'lock', 500, 360, 'btn-sm', 'btn-danger');
                $htmlStr .= Button::setName('打入黑名单')->setUrl(site_url("admin/corporation/doBlack/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("重置密码", site_url("admin/corporation/users/userid/" . $this->getUserId()), "list", 600, 460, 'btn-sm', 'btn-danger');
                $htmlStr .= Button::setName('重置密码')->setUrl(site_url("admin/corporation/users/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("删除", site_url("admin/corporation/delete/userid/" . $this->getUserId()), "list", 500, 360, 'btn-sm', 'btn-danger');
                break;
            case 3:
                if ($this->getUserId() == input::getInput("session.roleuserid")) {
                    if($this->getIsLock()==0){
                        $htmlStr .= Button::setIcon('edit')->setEvent("return showConfirm('修改后要重新认证，确定要修改资料吗？','".site_url("unit/profile/base/unlock/yes/userid/".$this->getUserId())."')")->button("修改资料");
                    }
                    if (!in_array($this->getIsLock(), [0,1, 4, 5, 6, 7]))
                        $htmlStr .= Button::link("申请认证", site_url("unit/profile/submit/userid/" . $this->getUserId()), "submit");
                }
                break;
            case 4:
                if ($this->getDepartmentId() == input::getInput("session.roleuserid")) {
                    if (in_array($this->getIsLock(), [1, 2]))
                        $htmlStr .= Button::setUrl(site_url("gather/corporation/dosubmit/userid/" . $this->getUserId()))->setIcon('check')->link("审核");
                    $htmlStr .= Button::setUrl(site_url("gather/corporation/doBack/userid/" . $this->getUserId()))->setIcon('undo')->setClass('btn-alt-danger')->setWidth('800px')->setHeight('600px')->window("退回");
                    $htmlStr .= Button::setClass('btn-alt-danger')->window("重置密码", site_url("gather/corporation/users/userid/" . $this->getUserId()));
                }
                break;
            case 6:
                $htmlStr .= Button::setClass('btn-alt-danger')->window("重置密码", site_url("gather/corporation/users/userid/" . $this->getUserId()), "refresh");
                break;
            default:
                break;
        }
        return $htmlStr;
    }

    public function getAreaCode()
    {
        if (!parent::getAreaCode()) return '510000';
        else return parent::getAreaCode();
    }

    /**
     * 获得企业资产信息模型
     * @param bool $f
     * @return null
     */
    public function getAssets($f = false, $year = '')
    {
        $assetModel = $this->getAssetsModel();
        if ($f && $year) {
            $this->assets = $assetModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->assets === NULL || $f) {
            $this->assets = $assetModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->assets->getTotal() == 0) $this->assets = NULL;
        }
        return $this->assets;
    }

    /**
     * 检查企业是否已存在某年度的资产信息
     */
    public function checkAssetYear($year)
    {
        $assets = $this->getAssets(true, $year);
        $count = $assets->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }


    public function getAssetsModel()
    {
        return M('CorporationsAsset');
    }

    public function getStaffsModel()
    {
        return M('CorporationsStaff');
    }

    public function getProductModel()
    {
        return M('CorporationsProduct');
    }

    public function getProjectModel()
    {
        return M('CorporationsProject');
    }

    /**
     * 获得企业员工信息模型
     * @param bool $f
     * @return null
     */
    public function getStaffs($f = false, $year = '')
    {
        $staffModel = $this->getStaffsModel();
        if ($f && $year) {
            $this->staffs = $staffModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->staffs === NULL || $f) {
            $this->staffs = $staffModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->staffs->getTotal() == 0) $this->staffs = NULL;
        }
        return $this->staffs;
    }

    /**
     * 检查企业是否已存在某年度的职工概况
     */
    public function checkStaffYear($year)
    {
        $staffs = $this->getStaffs(true, $year);
        $count = $staffs->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }

    /**
     * 检查企业是否已存在某年度的产品信息
     */
    public function checkProductYear($year)
    {
        $products = $this->getProducts(true, $year);
        $count = $products->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }

    /**
     * 获得企业产品模型
     * @param bool $f
     * @return null
     */
    public function getProducts($f = false, $year = '')
    {
        $productModel = $this->getProductModel();
        if ($f && $year) {
            $this->products = $productModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->products === NULL || $f) {
            $this->products = $productModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->products->getTotal() == 0) $this->products = NULL;
        }
        return $this->products;
    }

    /**
     * 获得企业产品模型
     * @param bool $f
     * @return null
     */
    public function getProjects($f = false, $year = '')
    {
        $projectModel = $this->getProjectModel();
        if ($f && $year) {
            $this->projects = $projectModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->projects === NULL || $f) {
            $this->projects = $projectModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->projects->getTotal() == 0) $this->projects = NULL;
        }
        return $this->projects;
    }

    public function getZdzks($levels=[])
    {
        $addWhere = getMonthProjectCondition();
        $addWhere .= " and corporation_id = '".$this->getUserId()."'";
        if($levels) {
            $querys = [];
            foreach ($levels as $level){
                $querys[] = "'".$level."'";
            }
            $addWhere.=" and level IN (".implode(',',$querys).")";
        }
        return sf::getModel('Projects')->selectAll($addWhere, "ORDER BY `id` DESC");
    }

    public function getZdzkNames($levels=[])
    {
        $zdsks = $this->getZdzks($levels);
        $names = [];
        while($zdsk = $zdsks->getObject()){
            $names[] = $zdsk->getSubject();
        }
        return implode('、',$names);
    }

    public function getMonthZdzkNames($level='')
    {
        $levels = [];
        if($level) $levels[] = $level;
        $zdsks = $this->getZdzks($levels);
        $names = [];
        while($zdsk = $zdsks->getObject()){
            $names[] = $zdsk->getSubject();
        }
        return implode('、',$names);
    }


    public function getQuarterZdzkNames()
    {
        $levels=['国家级'];
        $zdsks = $this->getZdzks($levels);
        $names = [];
        while($zdsk = $zdsks->getObject()){
            $names[] = $zdsk->getSubject();
        }
        return implode('、',$names);
    }

    /**
     * 检查企业是否已存在某年度的项目信息
     */
    public function checkProjectYear($year)
    {
        $projects = $this->getProjects(true, $year);
        $count = $projects->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }

    public function selectPatents()
    {
        return sf::getModel("CorporationsPatent")->selectAll("user_id = '" . $this->getUserId() . "'");
    }

    public function selectUsers()
    {
        return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '" . $this->getUserId() . "' AND role_id = '3')");
    }

    public function users()
    {
        return $this->selectUsers();
    }

    public function getMark()
    {
        $html = '';
        if(input::session('userlevel')==1){
            $this->getIgnoreEndat() && $html .= '<span style="color:red;">[时]</span>';
            $this->getIgnoreCountLimit() && $html .= '<span style="color:red;">[数]</span>';
        }
        return $html;
        $htmlStr = '[' . $this->getLevel() . ']';
        if(!empty($this->getProperty()) && $this->getProperty()!='O') $htmlStr .= '['.$this->getProperty().']';
        return '<mark>'.$htmlStr.'</mark>';
    }

    function toBlack()
    {
        $this->setIsLock(4);//设置为黑名单
        //设置账号不可登录
        $users = $this->users();
        while ($user = $users->getObject()) {
            $user->setIsLock(4);
            $user->save();
        }
        return $this->save();
    }

    public function getUnitArea()
    {
        if (empty(parent::getUnitArea())) {
            //如果区域为空，默认选中四川省
            return '510000';
        } else {
            return parent::getUnitArea();
        }
    }

    /**
     * 获得企业专利信息
     * @param bool $f
     * @return null
     */
    public function getPatents($f = false)
    {
        $patentModel = $this->getPatentModel();
        if ($this->patents === NULL || $f) {
            $this->patents = $patentModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `created_at` DESC");
            if ($this->patents->getTotal() == 0) $this->patents = NULL;
        }
        return $this->patents;
    }

    public function getPatentModel()
    {
        return M('CorporationsPatent');
    }

    /**
     * 获取企业年度职工状态
     * @param $year
     * @return mixed
     */
    public function getStaffState($year)
    {
        $staffModel = $this->getStaffs(true, $year);
        return $staffModel->getIsLock();
    }

    /**
     * 获取企业年度产品状态
     * @param $year
     * @return mixed
     */
    public function getProductState($year)
    {
        $productModel = $this->getProducts(true, $year);
        return $productModel->getIsLock();
    }

    /**
     * 获取企业年度项目状态
     * @param $year
     * @return mixed
     */
    public function getProjectState($year)
    {
        $projectModel = $this->getProjects(true, $year);
        return $projectModel->getIsLock();
    }

    public function getAreas()
    {
        if (!parent::getAreaCode()) return '/';
        return sf::getModel('Citys')->selectByCode(parent::getAreaCode())->getSubject();
    }

    function getStatistics()
    {
        $statistics = sf::getModel('CorporationStatistics')->getPager(" corporation_id='" . parent::getUserId() . "' ORDER BY year ASC ");
        return $statistics;
    }

    function getUnitManagers()
    {
        $users = [];
        $db = sf::getLib("db");
        $sql = "select c.manager_user_id as manager_user_id from corporations as c,corporation_statistics as cs where c.user_id=cs.corporation_id AND c.property IN('企业','国有企业','外资企业','民营企业','高新企业')";
        $query = $db->query($sql);
        while ($row = $db->fetch_array($query)) {
            if (!array_key_exists($row['manager_user_id'], $users))
                $users[$row['manager_user_id']] = sf::getModel("Users")->selectByUserId($row['manager_user_id']);
        }
        return $users;
    }

    //科研经费
    function getMoneyByYear($year = '')
    {
        return sf::getModel("CorporationMoneys")->selectByUserId(parent::getUserId(), $year ?: date('Y'));
    }

    /**
     * 单位财务信息数据集
     */
    function finances()
    {
        return sf::getModel("CorporationFinances")->selectAll("user_id = '" . $this->getUserId() . "' ", "ORDER BY year DESC");
    }

    /**
     * 取得指定年度的财务数据
     */
    function getFinance($year)
    {
        if ($this->isNew()) return false;
        return sf::getModel("CorporationFinances")->selectByYear($year, $this->getUserId());
    }

    /**
     * 获取法人手机号码
     * @return [type] [description]
     */
    function getLegalPersonMobile()
    {
        return $this->getPrincipalPhone();
    }

    /**
     * 设置法人手机号码
     * @param string $v 手机号码
     */
    function setLegalPersonMobile($v)
    {
        parent::setPrincipalPhone($v);
        return $this;
    }

    /**
     * 获取法人姓名
     * @return [type] [description]
     */
    function getLegalPersonName()
    {
        return $this->getPrincipal();
    }

    /**
     * 设置法人姓名
     * @param string $v 手机号码
     */
    function setLegalPersonName($v)
    {
        parent::setPrincipal($v);
        return $this;
    }

    /**
     * 获取法人身份证号
     * @return [type] [description]
     */
    function getLegalPersonIdcard()
    {
        return $this->getPrincipalCardid();
    }

    /**
     * 设置法人身份证号
     * @param string $v 手机号码
     */
    function setLegalPersonIdcard($v)
    {
        parent::setPrincipalCardid($v);
        return $this;
    }

    /**
     * 是否是叶子节点
     */
    function isLeaf()
    {
        $db = sf::getLib('db');
        $count = $db->result_first("select count(*) c from `" . $this->table . "` where parent_id = '" . $this->getUserId() . "'");
        return $count == 0;
    }

    function getChilds()
    {
        $childs = $this->selectAll("parent_id = '" . $this->getUserId() . "'", "order by id asc");
        $childArrs = [];
        $i=0;
//        if($this->getLevel()==1){
            $childArrs[$i]['n'] = '本部';
            $childArrs[$i]['v'] = $this->getUserId();
            $i++;
//        }
        while ($child = $childs->getObject()) {

            $childArrs[$i]['n'] = $child->getSubject();
            $childArrs[$i]['v'] = $child->getUserId();
            if ($child->isLeaf() === false) {
                $childArrs[$i]['s'] = $child->getChilds();
            }
            $i++;
        }
        return $childArrs;
    }

    function getChildCompanys()
    {
        return $this->selectAll("`".$this->getCompanyField()."` = '" . $this->getUserId() . "' and level > '".$this->getLevel()."'", "order by id asc");;
    }

    function getCompanyField()
    {
        switch ($this->getLevel()){
            case 1:
                return 'first_id';
            case 2:
                return 'second_id';
            case 3:
                return 'third_id';
            case 4:
                return 'fourth_id';
        }
    }

    function getManagerNames()
    {
        $users = $this->selectUsers();
        $userNames = [];
        $i=0;
        while($user = $users->getObject()){
            if($this->isManager($user->getUserId())){
                $userNames[0] = $user->getUserUsername();
            }else{
                $userNames[$i+1] = $user->getUserUsername();
            }
            $i++;
        }
        ksort($userNames);
        return implode('、',$userNames);
    }

    public function getLevelStr()
    {
        return $this->getBigNumber(parent::getLevel()).'级';
    }

    private function getBigNumber($num)
    {
        $arr = array("零","一","二","三","四","五","六","七","八","九","十");
        return $arr[$num];
    }

    public function getMoneys($year=5)
    {
        $years = [];
        for($year=date('Y',strtotime('-'.($year-1).' year'));$year<=date('Y');$year++){
            $years[] = $year;
        }

        $moneys =  sf::getModel('CorporationMoneys')->selectAll("user_id = '".$this->getUserId()."' and `year` in (".implode(',',$years).")");
        $datas = [];
        while($money = $moneys->getObject()){
            $datas[$money->getYear()] = $money->toArray();
        }
        return $datas;
    }

    public function setProperty($arr)
    {
        if(is_array($arr)) {
            return parent::setProperty(implode('|',$arr));
        }
        return parent::setProperty($arr);
    }

    public function getProperty()
    {
        return str_replace('|','、',parent::getProperty());
    }

    public function getPropertyArr()
    {
        return explode('|',parent::getProperty());
    }

    public function getPropertySubject()
    {
        if($this->isO()){
            return '都不是';
        }
        $categorySubjectArr = [];
        if($this->isG()){
            $categorySubjectArr[] = '高新技术企业（已认定）';
        }
        if($this->isP()){
            $categorySubjectArr[] = '高新技术企业（培育）';
        }
        if($this->isK()){
            $categorySubjectArr[] = '科技型中小企业';
        }
        if($this->isD()){
            $categorySubjectArr[] = '四川省瞪羚企业';
        }
        if($this->isQ()){
            $categorySubjectArr[] = '其他科技型企业';
        }
        return getCheckedStr(['高新技术企业（已认定）','高新技术企业（培育）','科技型中小企业','四川省瞪羚企业','其他科技型企业','都不是'], $categorySubjectArr);
    }

    public function getPropertyStr()
    {
        if($this->isO()){
            return '都不是';
        }
        $categorySubjectArr = [];
        if($this->isG()){
            $categorySubjectArr[] = '高新技术企业';
        }
        if($this->isP()){
            $categorySubjectArr[] = '高新技术企业（培育）';
        }
        if($this->isK()){
            $categorySubjectArr[] = '科技型中小企业';
        }
        if($this->isD()){
            $categorySubjectArr[] = '四川省瞪羚企业';
        }
        if($this->isQ()){
            $categorySubjectArr[] = '其他科技型企业';
        }
        return implode('、',$categorySubjectArr);
    }

    public function getFmzl()
    {
        return sf::getModel('Patents')->selectAll("corporation_id = '".$this->getUserId()."' and `type` = 1 and statement = 10") ->getTotal();
    }

    public function getSyxx()
    {
        return sf::getModel('Patents')->selectAll("corporation_id = '".$this->getUserId()."' and `type` = 2 and statement = 10") ->getTotal();
    }

    public function getWgsj()
    {
        return sf::getModel('Patents')->selectAll("corporation_id = '".$this->getUserId()."' and `type` = 3 and statement = 10") ->getTotal();
    }

    public function getRjzzq()
    {
        return sf::getModel('Softs')->selectAll("corporation_id = '".$this->getUserId()."' and statement = 10") ->getTotal();
    }


    /**
     * 是否是高新技术企业
     * @return bool|string
     */
    public function isG()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('G',$categorys);
    }


    /**
     * 是否是高新技术企业(培育)
     * @return bool|string
     */
    public function isP()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('P',$categorys);
    }


    /**
     * 是否是科技型中小企业
     * @return bool|string
     */
    public function isK()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('K',$categorys);
    }

    /**
     * 是否是瞪羚企业
     * @return bool
     */
    public function isD()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('D',$categorys);
    }

    /**
     * 是否是其他科技型企业
     * @return bool
     */
    public function isQ()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('Q',$categorys);
    }

    /**
     * 不属于任何科技型企业
     * @return bool
     */
    public function isO()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('O',$categorys);
    }

    public function getMoneyUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_moneys` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'<i class="text-danger">未填写</i>';
    }

    public function getPersonUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_persons` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'<i class="text-danger">未填写</i>';
    }

    public function getInnovateUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_innovates` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'-';
    }

    public function getPlatformUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_platforms` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'-';
    }

    public function getInnovateCount()
    {
        $count =  sf::getLib('db')->result_first("SELECT count(*) c FROM `corporation_innovates` where user_id = '".$this->getUserId()."'");
        return (int)$count;
    }

    public function getPlatformCount()
    {
        $count =  sf::getLib('db')->result_first("SELECT count(*) c FROM `corporation_platforms` where user_id = '".$this->getUserId()."'");
        return (int)$count;
    }

    /**
     * 是否需要归口部门审核
     * @param $project object
     * @return bool true:需要 false:不需要
     */
    function isNeedDepartmentCheck()
    {
        if(isDirectlyUnit($this->getDepartmentId())){
            //是省直属单位 无须归口审核
            return false;
        }
        return true;

    }

    function checkMonthReport()
    {
        //是否有已立项的项目
        $count = sf::getLib('db')->result_first("select count(*) c from projects where corporation_id = '".$this->getUserId()."' and statement = 29");
        if($count==0) return true;
        //检查月度报告是否填写
        $count = sf::getLib('db')->result_first("select count(*) c from months where company_id = '".$this->getUserId()."' and declare_year = '".date('Y')."' and declare_month = '".getLastMonth()."'");
        return $count>0;
    }

    function checkWeekReport()
    {
        //是否有已立项的项目
        $count = sf::getLib('db')->result_first("select count(*) c from projects where corporation_id = '".$this->getUserId()."' and statement = 29 and month_open = 1");
        if($count==0) return true;
        //检查周报是否填写
        $count = sf::getLib('db')->result_first("select count(*) c from weeks where company_id = '".$this->getUserId()."' and declare_year = '".date('Y')."' and declare_week = '".getLastWeek()."'");
        return $count>0;
    }

    function getNeedUserWriteStageCount()
    {
        $needCount = sf::getLib('db')->result_first("select count(*) c from stages where user_role = 2 and company_id = '".$this->getUserId()."' and worker_id > 0 and statement in (-1,0,1,3,6,12) and project_id in (select project_id from projects where corporation_id = '".$this->getUserId()."' and statement=29 and stage_open = 1)");
        return $needCount;
    }

    function getNeedUserWriteReviewCount()
    {
        $needCount = sf::getLib('db')->result_first("select count(*) c from reviews where user_role = 2 and company_id = '".$this->getUserId()."' and statement in (-1,0,1,3,6,12)");
        return $needCount;
    }

    function getNeedUserWriteReviewQuarterCount()
    {
        $needCount = sf::getLib('db')->result_first("select count(*) c from reviews where company_id = '".$this->getUserId()."' and project_id not in (select project_id from reviewquarters where review_year = ".getLastYear()." and review_quarter = '".getLastQuarter()."') and company_id = '".$this->getUserId()."'");
        return $needCount;
    }

    function getNeedCompanyWriteStageCount()
    {
        $needCount = sf::getLib('db')->result_first("select count(*) c from stages where user_role = 2 and company_id = '".$this->getUserId()."' and declare_year = '2022'");
        return $needCount;
    }

    function getNeedCompanyWriteReviewquarterCount()
    {
        $needCount = sf::getLib('db')->result_first("select count(*) c from reviews where user_role = 3 and company_id = '".$this->getUserId()."' and declare_year = '2023'");
        return $needCount;
    }

    function getNeedCompanyWriteInspectCount()
    {
        $needCount = sf::getLib('db')->result_first("select count(*) c from inspects where user_role = 2 and company_id = '".$this->getUserId()."' and declare_year = '2023'");
        return $needCount;
    }

    function checkNeedCompanyWriteStage()
    {
        $count = sf::getLib('db')->result_first("select count(*) c from stages where user_role = 3 and company_id = '".$this->getUserId()."' and declare_year = '2022' and statement = -1");
        return $count>0;
    }

    function getNoUserProjectsCount()
    {
        //是否有已立项的且负责人还未关联的项目
        $count = sf::getLib('db')->result_first("select count(*) c from projects where corporation_id = '".$this->getUserId()."' and statement IN (29,30) and user_id = '0'");
        return $count;
    }

    /**
     * 国考等级
     * @return void
     */
    public function getExamNation()
    {
        $datas = [];
        $exams = sf::getModel('IndexExams')->selectAll("company_id = '".$this->getUserId()."' and index_code = 'strength_performance_level'","order by index_year desc");
        while($exam = $exams->getObject()){
            if($exam->getData()=='-') continue;
            $datas[] = $exam->getData().'('.$exam->getIndexYear().')';
        }
        return empty($datas) ? '无' : implode('、',$datas);
    }

    /**
     * 省考排名
     * @return void
     */
    public function getExamProvince()
    {
        $datas = [];
        $exams = sf::getModel('IndexExams')->selectAll("company_id = '".$this->getUserId()."' and index_code = 'strength_performance_rank'","order by index_year desc");
        while($exam = $exams->getObject()){
            if($exam->getData()=='-') continue;
            $datas[] = $exam->getData().'('.$exam->getIndexYear().')';
        }
        return empty($datas) ? '无' : implode('、',$datas);
    }

    public function getPager2($addWhere = '',$addSql = '',$showMax = 20,$select = '',$key = '',$form_vars=array())
    {
        $db = sf::getLib("db");

        if($select) $sql = $select." ";
        else $sql = "SELECT $this->fields FROM `".$this->table."` ";
        $addWhere && $sql .= "WHERE ".$addWhere." ";
        $addSql && $sql .= $addSql." ";

        if(!router::get("totalnum".$key) || count($_POST) > 0){
            $_sql = "SELECT COUNT(*) AS NUM FROM `".$this->table."` c ";
            $addWhere && $_sql .= "WHERE ".$addWhere." ";
            $addSql && $_sql .= $addSql." ";
            $row = $db->fetch_first($_sql);
            $total = $row['NUM'];
        }else $total = router::get("totalnum".$key);
        if($_GET['open_model']){
            $_POST['open_model'] = 'frame';
        }
        $form_vars[] = 'open_model';
        $pager = new pager($total,$showMax,$key,$form_vars);
        $sql .= "LIMIT ".$pager->getStartNum().",".$pager->getShowNum();
        $query = $db->query($sql);

        $pager->setField($db->result_array($query));
        $pager->setObject(clone $this);

        return $pager;
    }

    public function checkType($allowTypes=array())
    {
        if(count($allowTypes) == 0) return true;//无判断类型及全部允许
        foreach ($allowTypes as $allowType){
            //三甲
            if($allowType=='3j' && $this->getLevel()!='三级甲等'){
                return '贵单位等级('.$this->getLevel().')不在申报范围内';
            }
            //省市级单位
            if($allowType=='ss' && !in_array($this->getProperty(),['省级单位','市（州）级单位'])){
                return '贵单位性质('.$this->getProperty().')不在申报范围内';
            }
        }
        return true;
    }
}