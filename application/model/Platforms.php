<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BasePlatforms;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Platforms extends BasePlatforms
{
    private $user = NULL;
    private $leader = NULL;
    function selectByPlatformId($platformId='')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `platform_id` = '".$platformId."' ");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setPlatformId($platformId ? $platformId : sf::getLib("MyString")->getRandString());
        return $this;
    }
    function selectBySubject($subject)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `subject` = '".$subject."' ");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setPlatformId(sf::getLib("MyString")->getRandString());
            $this->setSubject($subject);
        }
        return $this;
    }

    public function getUser($f=false)
    {
        if($this->user === NULL || $f)
            $this->user = sf::getModel("Users")->selectByUserId($this->getAccountId());
        return $this->user;
    }

    function getLeader($f = false)
    {
        if ($this->leader === NULL || $f) $this->leader = sf::getModel("PlatformLeaders")->selectByPlatformId($this->getPlatformId());
        return $this->leader;
    }

    function getAccountId()
    {
        $db = sf::getLib("db");
        $sql = "select user_id from user_roles where user_role_id = '".$this->getPlatformId()."' and role_id = '5' ";
        $row = $db->fetch_first($sql);
        return $row['user_id'] ? $row['user_id'] : $this->getPlatformId();
    }

    function getIsLock()
    {
        return $this->getStatement();
    }

    public function getCooperatationNameArray()
    {
        $cooperatations = parent::getCooperatation();
        $cooperatations = json_decode($cooperatations,true);
        return array_column($cooperatations,'subject');
    }

    public function getCooperatationArray()
    {
        $cooperatations = parent::getCooperatation();
        $cooperatations = json_decode($cooperatations,true);
        return $cooperatations;
    }

    public function getOtherCooperatationNames()
    {
        $others = $this->getCooperatationArray();
        if(empty($others)) return '无';
        $names = [];
        foreach ($others as $other){
            $names[] = $other['subject'];
        }
        return implode('、',$names);
    }

    public function setCooperatation($names,$propertys=[])
    {
        $datas = [];
        $i=0;
        foreach ($names as $k=>$name){
            if(empty($name)) continue;
            $datas[$i]['subject'] = $name;
            $datas[$i]['property'] = $propertys[$k];
            $i++;
        }
        return parent::setCooperatation(json_encode($datas, JSON_UNESCAPED_UNICODE));
    }

    public function getAllCorporation()
    {
        $names[] = $this->getCorporationName();
        $others = $this->getCooperatationNameArray();
        foreach ($others as $other){
            $names[] = $other;
        }
        $names = array_filter($names);
        return implode('、',$names);
    }

    function getMark()
    {
        return;
    }

    function getDepartmentName()
    {
        return sf::getModel('Departments')->selectByUserId(parent::getDepartmentId())->getSubject();
    }
    function getState()
    {
        switch(parent::getStatement())
        {
            case 0:
            case 1:
                return '填写中';
            case 2:
                return '待依托单位审核';
            case 3:
                return '依托单位退回';
            case 5:
                return '待主管部门审核';
            case 6:
                return '主管部门退回';
            case 9:
                return '待科技厅审核';
            case 10:
                return '<span class="badge badge-success">已审核</span>';
            default:
                return '<s style="color:#F00;">未知状态</s>';
        }
    }

    /**
     * 是否锁定
     */
    function hasLock()
    {
        if(in_array($this->getIsLock(),[1,4,5,6,10])) return true;
        return false;
    }

    public function getQuarterCount()
    {
        return (int) sf::getLib('db')->result_first("select count(*) c from quarters where platform_id = '".parent::getPlatformId()."'");
    }

    public function getNewUserName()
    {
        return 'platform'.getPlatformTotal();
    }

    /**
     * 是否有报表
     */
    function hasQuarter()
    {
        if(sf::getModel("Quarters")->selectAll("platform_id = '".parent::getPlatformId()."' ",'order by id desc',1)->getTotal()) return true;
        else return false;
    }

    public function delete()
    {
        if($this->hasQuarter()) return false;
        //删除角色
        sf::getModel("UserRoles")->removeRole($this->getPlatformId(),5);
        //删除账号
        $user = sf::getModel('Users')->selectByUserId($this->getAccountId());
        $user->delete();
        return parent::delete();
    }

    public function validate(){
        $message = array();
        if(!$this->getSubject()) $message[] = '请填写平台名称';
        if(!$this->getPlatformType()) $message[] = '请选择平台类型';
        if(!$this->getCorporationProperty()) $message[] = '请选择单位性质';
        if(!$this->getIndustry()) $message[] = '请选择产业领域';
        if(!$this->getPrincipalName()) $message[] = '请填写负责人姓名';
        if(!$this->getLinkmanName()) $message[] = '请填写联系人姓名';
        if(!$this->getLinkmanMobile()) $message[] = '请填写联系人手机';
        if(!$this->getAreaCode()) $message[] = '请选择所在地区';
        if(!$this->getAddress()) $message[] = '请填写详细地址';
        return $message;
    }

    /**
     * 判断是否重复填报
     * @return int
     */
    function isRepeat()
    {
        $addwhere = "platform_id!='" . $this->getPlatformId() . "' and subject = '" . $this->getSubject() . "'";
        $count = (int)sf::getLib('db')->result_first("select count(*) c from `{$this->table}` where {$addwhere}");
        return $count > 0;
    }

    public function selectUsers()
    {
        return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '" . $this->getPlatformId() . "' AND role_id = '5')");
    }

    public function isManager($userid = '')
    {
        if (!$userid) return false;
        if ($this->getManagerUserId() == $userid) return true;
        else return false;
    }

    function getManagerNames()
    {
        $users = $this->selectUsers();
        $userNames = [];
        $i=0;
        while($user = $users->getObject()){
            if($this->isManager($user->getUserId())){
                $userNames[0] = $user->getUserUsername();
            }else{
                $userNames[$i+1] = $user->getUserUsername();
            }
            $i++;
        }
        ksort($userNames);
        return implode('、',$userNames);
    }

    /**
     * 是否需要归口部门审核
     * @param $project object
     * @return bool true:需要 false:不需要
     */
    function isNeedDepartmentCheck()
    {
        if(isDirectlyUnit($this->getDepartmentId())){
            //是省直属单位 无须归口审核
            return false;
        }
        return true;

    }

    public function getData($code,$indexYear='')
    {
        return sf::getModel('PlatformDatas')->selectByPlatformId($this->getPlatformId(),$code);
    }

    public function setData($data,$code)
    {
        $platformData = sf::getModel('PlatformDatas')->selectByPlatformId($this->getPlatformId(),$code);
        if($platformData->isNew()){
            $platformData->setCreatedAt(date('Y-m-d H:i:s'));
        }
        $platformData->setData($data);
        $platformData->save();
        return $platformData;
    }

    public function setDatas($datas)
    {
        foreach ($datas as $code=>$data){
            $this->setData($data,$code);
        }
    }

    public function getResearchDirectionArray()
    {
        return array_filter(explode('$',parent::getResearchDirection()));
    }

    public function getResearchDirectionStr()
    {
        return implode('、',$this->getResearchDirectionArray());
    }

    function getStaffCount($researchDirection='')
    {
        $addwhere = "`platform_id` = '".$this->getPlatformId()."'";
        if($researchDirection) $addwhere.=" and research_direction = '{$researchDirection}'";
        return sf::getModel("PlatformStaffs")->selectAll($addwhere,"order by id asc")->getTotal();
    }

    function doReplaceStaffResearch($oldResearch,$newResarch)
    {
        $db = sf::getLib("db");
        $db->exec("UPDATE `platform_staffs` SET `research_direction` = '{$newResarch}' WHERE `platform_id` = '".$this->getPlatformId()."' and `research_direction` = '{$oldResearch}'");
    }

    public function setResearchDirection($arr)
    {
        if(is_array($arr)){
            $arr = array_filter($arr);
            $arr = array_unique($arr);
            $arr = $this->removeEmpty($arr);
            return parent::setResearchDirection(implode('$',$arr));
        }
        return parent::setResearchDirection($arr);
    }

    function getStaffByResearch($researchDirection)
    {
        return sf::getModel("PlatformStaffs")->selectAll("`platform_id` = '".$this->getPlatformId()."' and `research_direction` = '{$researchDirection}'","order by id asc");
    }

    function getStaffs()
    {
        return sf::getModel("PlatformStaffs")->selectAll("`platform_id` = '".$this->getPlatformId()."'","order by id asc");
    }

    function getStaffById($id)
    {
        return sf::getModel("PlatformStaffs",$id);
    }

    function getCommittees()
    {
        return sf::getModel("PlatformCommittees")->selectAll("`platform_id` = '".$this->getPlatformId()."'","order by type asc,id asc");
    }

    function getCommitteeById($id)
    {
        return sf::getModel("PlatformCommittees",$id);
    }
    function getCommitteeCount()
    {
        $addwhere = "`platform_id` = '".$this->getPlatformId()."'";
        return sf::getModel("PlatformCommittees")->selectAll($addwhere,"order by id asc")->getTotal();
    }

    function getCommitteeLeaderCount()
    {
        $addwhere = "`platform_id` = '".$this->getPlatformId()."' AND `type` = '主任'";
        return sf::getModel("PlatformCommittees")->selectAll($addwhere,"order by id asc")->getTotal();
    }

    function getStaffByNoResearch()
    {
        $researchDirections = $this->getResearchDirectionArray();
        $arr = [];
        foreach ($researchDirections as $researchDirection){
            $arr[] = "'".$researchDirection."'";
        }
        if($arr){
            return sf::getModel("PlatformStaffs")->selectAll("`platform_id` = '".$this->getPlatformId()."' and `research_direction` not in  (".implode(',',$arr).")","order by id asc");
        }
        return false;
    }

    public function decoupling($userid = '')
    {
        if (!$userid) return false;
        return sf::getModel("UserRoles")->remove("user_role_id = '" . $this->getPlatformId() . "' and role_id = '5' AND user_id = '" . $userid . "' ");
    }

    public function coupling($userid = '')
    {
        if (!$userid) return false;
        $userrole = sf::getModel("UserRoles")->selectByRole($userid, 5);
        $userrole->setUserRoleId($this->getPlatformId());
        return $userrole->save();
    }


    private function removeEmpty($arr, $trim = TRUE)
    {
        foreach ($arr as $key => $value){
            if (is_array($value)){
                $this->removeEmpty($arr[$key]);
            }
            else{
                $value = trim($value);
                $value = str_replace(' ','',$value);
                $value = str_replace('　','',$value);
                if ($value == ''){
                    unset($arr[$key]);
                }
                elseif ($trim){
                    $arr[$key] = $value;
                }
            }
        }
        return $arr;
    }
}