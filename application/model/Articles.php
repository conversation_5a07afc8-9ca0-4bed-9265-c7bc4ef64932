<?php

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z lwj $
 */

namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use App\Model\BaseArticles;
class Articles extends BaseArticles
{
	function selectByCategoryId($id=0,$showMax=6)
	{
		return $this->selectAll("category_id = '".$id."' ","ORDER BY `updated_at` DESC",$showMax);	
	}
	
	function getCategoryName()
	{
		return sf::getModel("categorys",parent::getCategoryId(),'tool')->getSubject();
	}
	
	function getSubject($len=0,$f=false)
	{
		if($f) return parent::getSubject($len);
		if((strtotime(parent::getUpdatedAt()) + 60*60*24*7) > time())
//			return '<span style="color:red">'.parent::getSubject($len).'<span>';
            return parent::getSubject($len);
		else return parent::getSubject($len);
	}

	function setChannelId($channelIds)
    {
        $articleChannels = sf::getModel('ArticleChannel')->selectAll("article_id = '".$this->getId()."'");
        while($articleChannel = $articleChannels->getObject()){
            if(!in_array($articleChannel->getChannelId(),$channelIds)){
                $articleChannel->delete();
            }
        }
        foreach ($channelIds as $cid){
            $articleChannel = sf::getModel('ArticleChannel')->selectByAidAndCid($this->getId(),$cid);
            $articleChannel->save();
        }
    }

	function getChannelId()
    {
        $articleChannels = sf::getModel('ArticleChannel')->selectAll("article_id = '".$this->getId()."'");
        $channelIds = [];
        while($articleChannel = $articleChannels->getObject()){
            $channelIds[] = $articleChannel->getChannelId();
        }
        return $channelIds;
    }

    function getFile()
    {
        return explode(',',parent::getFile());
    }

    function setFile($arr)
    {
        return parent::setFile(implode(',',$arr));
    }

    function setCreatedAt($timeStr)
    {
        return parent::setCreatedAt(str_replace('T',' ',$timeStr));
    }

    //附件
    function getAttachments()
    {
        $html='';
        $file_id = $this->getFile();
        $file_id = array_filter($file_id);
        if($file_id)
        {
            $html .= '<span>附件下载</span><br/>';
            foreach ($file_id as $item){
                $filemanager = sf::getModel('Filemanager',$item);
                $html .= '<a download="'.$filemanager->getFileName().'" href="'.site_path('up_files/'.$filemanager->getFilePath()).'">'.$filemanager->getFileIcon().' '.$filemanager->getFileName().'</a><br/>';
            }
        }
        return $html;
    }

    function getChannelName()
    {
        $channelIds = $this->getChannelId();
        foreach($channelIds as $channelId){
            $channel = sf::getModel('Channels',$channelId);
            return $channel->getSubject();
        }
    }

    function getChannel()
    {
        $channelIds = $this->getChannelId();
        foreach($channelIds as $channelId){
            $channel = sf::getModel('Channels',$channelId);
            return $channel;
        }
    }

    function getIsReleaseStr()
    {
        if($this->getIsRelease()==1) return '<span class="green">已发布</span>';
        else return '<span class="red">未发布</span>';
    }

    function getSubjectByKeyWord($keyword)
    {
        return str_replace($keyword,'<span class="text-danger">'.$keyword.'</span>',parent::getSubject());
    }
}