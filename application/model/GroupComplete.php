<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseGroupComplete;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class GroupComplete extends BaseGroupComplete
{
    public function exist($group_id='',$projectId='')
    {
        if(!$group_id || !$projectId) return false;
        $db = sf::getLib("db");
        $query = $db->query("select id from ".$this->table." WHERE group_id = ".$group_id." AND project_id = '".$projectId."'");
        $num = $db->num_rows($query);
        if($num==0) return false;
        else return true;
    }

    function selectByProjectId($projectId='',$type='complete')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE type='".$type."' AND `project_id` = '".$projectId."' ");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        return $this;
    }
}