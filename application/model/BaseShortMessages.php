<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseShortMessages extends BaseModel
{
	private $id;
	private $item_id;
	private $item_type  = 'projects';
	private $task_id;
	private $mobile;
	private $message;
	private $reply;
	private $user_id;
	private $user_name;
	private $user_ip;
	private $send_at;
	private $statement;
	private $error_info;
	private $updated_at;
	private $get_at;
	public $table = "short_messages";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getItemId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->item_id,0,$len,"utf-8");
			else return substr($this->item_id,0,$len);
		}
		return $this->item_id;
	}

	public function getItemType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->item_type,0,$len,"utf-8");
			else return substr($this->item_type,0,$len);
		}
		return $this->item_type;
	}

	public function getTaskId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->task_id,0,$len,"utf-8");
			else return substr($this->task_id,0,$len);
		}
		return $this->task_id;
	}

	public function getMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->mobile,0,$len,"utf-8");
			else return substr($this->mobile,0,$len);
		}
		return $this->mobile;
	}

	public function getMessage($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->message,0,$len,"utf-8");
			else return substr($this->message,0,$len);
		}
		return $this->message;
	}

	public function getReply($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->reply,0,$len,"utf-8");
			else return substr($this->reply,0,$len);
		}
		return $this->reply;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getUserName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name,0,$len,"utf-8");
			else return substr($this->user_name,0,$len);
		}
		return $this->user_name;
	}

	public function getUserIp($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_ip,0,$len,"utf-8");
			else return substr($this->user_ip,0,$len);
		}
		return $this->user_ip;
	}

	public function getSendAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->send_at));
		else return $this->send_at;
	}

	public function getStatement()
	{
		return $this->statement;
	}

	public function getErrorInfo($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->error_info,0,$len,"utf-8");
			else return substr($this->error_info,0,$len);
		}
		return $this->error_info;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getGetAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->get_at));
		else return $this->get_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setItemId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->item_id !== $v)
		{
			$this->item_id = $v;
			$this->fieldData["item_id"] = $v;
		}
		return $this;

	}

	public function setItemType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->item_type !== $v)
		{
			$this->item_type = $v;
			$this->fieldData["item_type"] = $v;
		}
		return $this;

	}

	public function setTaskId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->task_id !== $v)
		{
			$this->task_id = $v;
			$this->fieldData["task_id"] = $v;
		}
		return $this;

	}

	public function setMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->mobile !== $v)
		{
			$this->mobile = $v;
			$this->fieldData["mobile"] = $v;
		}
		return $this;

	}

	public function setMessage($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->message !== $v)
		{
			$this->message = $v;
			$this->fieldData["message"] = $v;
		}
		return $this;

	}

	public function setReply($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->reply !== $v)
		{
			$this->reply = $v;
			$this->fieldData["reply"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setUserIp($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_ip !== $v)
		{
			$this->user_ip = $v;
			$this->fieldData["user_ip"] = $v;
		}
		return $this;

	}

	public function setSendAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->send_at !== $v)
		{
			$this->send_at = $v;
			$this->fieldData["send_at"] = $v;
		}
		return $this;

	}

	public function setStatement($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->statement !== $v)
		{
			$this->statement = $v;
			$this->fieldData["statement"] = $v;
		}
		return $this;

	}

	public function setErrorInfo($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->error_info !== $v)
		{
			$this->error_info = $v;
			$this->fieldData["error_info"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setGetAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->get_at !== $v)
		{
			$this->get_at = $v;
			$this->fieldData["get_at"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `short_messages` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"item_id" => $this->getItemId(),
			"item_type" => $this->getItemType(),
			"task_id" => $this->getTaskId(),
			"mobile" => $this->getMobile(),
			"message" => $this->getMessage(),
			"reply" => $this->getReply(),
			"user_id" => $this->getUserId(),
			"user_name" => $this->getUserName(),
			"user_ip" => $this->getUserIp(),
			"send_at" => $this->getSendAt(),
			"statement" => $this->getStatement(),
			"error_info" => $this->getErrorInfo(),
			"updated_at" => $this->getUpdatedAt(),
			"get_at" => $this->getGetAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->item_id = '';
		$this->item_type = '';
		$this->task_id = '';
		$this->mobile = '';
		$this->message = '';
		$this->reply = '';
		$this->user_id = '';
		$this->user_name = '';
		$this->user_ip = '';
		$this->send_at = '';
		$this->statement = '';
		$this->error_info = '';
		$this->updated_at = '';
		$this->get_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["item_id"]) && $this->item_id = $data["item_id"];
		isset($data["item_type"]) && $this->item_type = $data["item_type"];
		isset($data["task_id"]) && $this->task_id = $data["task_id"];
		isset($data["mobile"]) && $this->mobile = $data["mobile"];
		isset($data["message"]) && $this->message = $data["message"];
		isset($data["reply"]) && $this->reply = $data["reply"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["user_ip"]) && $this->user_ip = $data["user_ip"];
		isset($data["send_at"]) && $this->send_at = $data["send_at"];
		isset($data["statement"]) && $this->statement = $data["statement"];
		isset($data["error_info"]) && $this->error_info = $data["error_info"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["get_at"]) && $this->get_at = $data["get_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}