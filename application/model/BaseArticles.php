<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseArticles extends BaseModel
{
	private $id;
	private $subject;
	private $brief;
	private $litpic;
	private $keywords;
	private $source;
	private $content;
	private $category_id;
	private $user_group_id  = '0';
	private $company_type  = 'ALL';
	private $types;
	private $cover;
	private $file;
	private $allow_comment  = '0';
	private $is_top  = '0';
	private $is_useful  = '0';
	private $is_common  = '0';
	private $is_release  = '0';
	private $suggestion;
	private $link;
	private $hits;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	private $created_at;
	private $deleted_at;
	public $table = "articles";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getBrief($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->brief,0,$len,"utf-8");
			else return substr($this->brief,0,$len);
		}
		return $this->brief;
	}

	public function getLitpic($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->litpic,0,$len,"utf-8");
			else return substr($this->litpic,0,$len);
		}
		return $this->litpic;
	}

	public function getKeywords($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->keywords,0,$len,"utf-8");
			else return substr($this->keywords,0,$len);
		}
		return $this->keywords;
	}

	public function getSource($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->source,0,$len,"utf-8");
			else return substr($this->source,0,$len);
		}
		return $this->source;
	}

	public function getContent($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->content,0,$len,"utf-8");
			else return substr($this->content,0,$len);
		}
		return $this->content;
	}

	public function getCategoryId()
	{
		return $this->category_id;
	}

	public function getUserGroupId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_group_id,0,$len,"utf-8");
			else return substr($this->user_group_id,0,$len);
		}
		return $this->user_group_id;
	}

	public function getCompanyType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->company_type,0,$len,"utf-8");
			else return substr($this->company_type,0,$len);
		}
		return $this->company_type;
	}

	public function getTypes($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->types,0,$len,"utf-8");
			else return substr($this->types,0,$len);
		}
		return $this->types;
	}

	public function getCover($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->cover,0,$len,"utf-8");
			else return substr($this->cover,0,$len);
		}
		return $this->cover;
	}

	public function getFile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->file,0,$len,"utf-8");
			else return substr($this->file,0,$len);
		}
		return $this->file;
	}

	public function getAllowComment()
	{
		return $this->allow_comment;
	}

	public function getIsTop()
	{
		return $this->is_top;
	}

	public function getIsUseful()
	{
		return $this->is_useful;
	}

	public function getIsCommon()
	{
		return $this->is_common;
	}

	public function getIsRelease()
	{
		return $this->is_release;
	}

	public function getSuggestion($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->suggestion,0,$len,"utf-8");
			else return substr($this->suggestion,0,$len);
		}
		return $this->suggestion;
	}

	public function getLink($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->link,0,$len,"utf-8");
			else return substr($this->link,0,$len);
		}
		return $this->link;
	}

	public function getHits()
	{
		return $this->hits;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getDeletedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->deleted_at));
		else return $this->deleted_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setBrief($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->brief !== $v)
		{
			$this->brief = $v;
			$this->fieldData["brief"] = $v;
		}
		return $this;

	}

	public function setLitpic($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->litpic !== $v)
		{
			$this->litpic = $v;
			$this->fieldData["litpic"] = $v;
		}
		return $this;

	}

	public function setKeywords($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->keywords !== $v)
		{
			$this->keywords = $v;
			$this->fieldData["keywords"] = $v;
		}
		return $this;

	}

	public function setSource($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->source !== $v)
		{
			$this->source = $v;
			$this->fieldData["source"] = $v;
		}
		return $this;

	}

	public function setContent($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->content !== $v)
		{
			$this->content = $v;
			$this->fieldData["content"] = $v;
		}
		return $this;

	}

	public function setCategoryId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->category_id !== $v)
		{
			$this->category_id = $v;
			$this->fieldData["category_id"] = $v;
		}
		return $this;

	}

	public function setUserGroupId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_group_id !== $v)
		{
			$this->user_group_id = $v;
			$this->fieldData["user_group_id"] = $v;
		}
		return $this;

	}

	public function setCompanyType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_type !== $v)
		{
			$this->company_type = $v;
			$this->fieldData["company_type"] = $v;
		}
		return $this;

	}

	public function setTypes($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->types !== $v)
		{
			$this->types = $v;
			$this->fieldData["types"] = $v;
		}
		return $this;

	}

	public function setCover($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->cover !== $v)
		{
			$this->cover = $v;
			$this->fieldData["cover"] = $v;
		}
		return $this;

	}

	public function setFile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->file !== $v)
		{
			$this->file = $v;
			$this->fieldData["file"] = $v;
		}
		return $this;

	}

	public function setAllowComment($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->allow_comment !== $v)
		{
			$this->allow_comment = $v;
			$this->fieldData["allow_comment"] = $v;
		}
		return $this;

	}

	public function setIsTop($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_top !== $v)
		{
			$this->is_top = $v;
			$this->fieldData["is_top"] = $v;
		}
		return $this;

	}

	public function setIsUseful($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_useful !== $v)
		{
			$this->is_useful = $v;
			$this->fieldData["is_useful"] = $v;
		}
		return $this;

	}

	public function setIsCommon($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_common !== $v)
		{
			$this->is_common = $v;
			$this->fieldData["is_common"] = $v;
		}
		return $this;

	}

	public function setIsRelease($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_release !== $v)
		{
			$this->is_release = $v;
			$this->fieldData["is_release"] = $v;
		}
		return $this;

	}

	public function setSuggestion($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->suggestion !== $v)
		{
			$this->suggestion = $v;
			$this->fieldData["suggestion"] = $v;
		}
		return $this;

	}

	public function setLink($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->link !== $v)
		{
			$this->link = $v;
			$this->fieldData["link"] = $v;
		}
		return $this;

	}

	public function setHits($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->hits !== $v)
		{
			$this->hits = $v;
			$this->fieldData["hits"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setDeletedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->deleted_at !== $v)
		{
			$this->deleted_at = $v;
			$this->fieldData["deleted_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `articles` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"subject" => $this->getSubject(),
			"brief" => $this->getBrief(),
			"litpic" => $this->getLitpic(),
			"keywords" => $this->getKeywords(),
			"source" => $this->getSource(),
			"content" => $this->getContent(),
			"category_id" => $this->getCategoryId(),
			"user_group_id" => $this->getUserGroupId(),
			"company_type" => $this->getCompanyType(),
			"types" => $this->getTypes(),
			"cover" => $this->getCover(),
			"file" => $this->getFile(),
			"allow_comment" => $this->getAllowComment(),
			"is_top" => $this->getIsTop(),
			"is_useful" => $this->getIsUseful(),
			"is_common" => $this->getIsCommon(),
			"is_release" => $this->getIsRelease(),
			"suggestion" => $this->getSuggestion(),
			"link" => $this->getLink(),
			"hits" => $this->getHits(),
			"updated_at" => $this->getUpdatedAt(),
			"created_at" => $this->getCreatedAt(),
			"deleted_at" => $this->getDeletedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->subject = '';
		$this->brief = '';
		$this->litpic = '';
		$this->keywords = '';
		$this->source = '';
		$this->content = '';
		$this->category_id = '';
		$this->user_group_id = '';
		$this->company_type = '';
		$this->types = '';
		$this->cover = '';
		$this->file = '';
		$this->allow_comment = '';
		$this->is_top = '';
		$this->is_useful = '';
		$this->is_common = '';
		$this->is_release = '';
		$this->suggestion = '';
		$this->link = '';
		$this->hits = '';
		$this->updated_at = '';
		$this->created_at = '';
		$this->deleted_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["brief"]) && $this->brief = $data["brief"];
		isset($data["litpic"]) && $this->litpic = $data["litpic"];
		isset($data["keywords"]) && $this->keywords = $data["keywords"];
		isset($data["source"]) && $this->source = $data["source"];
		isset($data["content"]) && $this->content = $data["content"];
		isset($data["category_id"]) && $this->category_id = $data["category_id"];
		isset($data["user_group_id"]) && $this->user_group_id = $data["user_group_id"];
		isset($data["company_type"]) && $this->company_type = $data["company_type"];
		isset($data["types"]) && $this->types = $data["types"];
		isset($data["cover"]) && $this->cover = $data["cover"];
		isset($data["file"]) && $this->file = $data["file"];
		isset($data["allow_comment"]) && $this->allow_comment = $data["allow_comment"];
		isset($data["is_top"]) && $this->is_top = $data["is_top"];
		isset($data["is_useful"]) && $this->is_useful = $data["is_useful"];
		isset($data["is_common"]) && $this->is_common = $data["is_common"];
		isset($data["is_release"]) && $this->is_release = $data["is_release"];
		isset($data["suggestion"]) && $this->suggestion = $data["suggestion"];
		isset($data["link"]) && $this->link = $data["link"];
		isset($data["hits"]) && $this->hits = $data["hits"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["deleted_at"]) && $this->deleted_at = $data["deleted_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}