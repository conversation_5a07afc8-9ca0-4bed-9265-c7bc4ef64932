<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseCorporationFinances extends BaseModel
{
	private $id;
	private $user_id;
	private $year;
	private $total_assets  = '0.00';
	private $total_liabilities  = '0.00';
	private $current_liabilities  = '0.00';
	private $sales_revenue  = '0.00';
	private $main_revenue  = '0.00';
	private $owner_equity  = '0.00';
	private $net_profit  = '0.00';
	private $RD  = '0.00';
	private $employees_number  = '0';
	private $researcher_number  = '0';
	private $finance_leader_full_name;
	private $finance_leader_phone;
	private $finance_leader_mobile;
	private $finance_leader_email;
	private $created_at  = 'CURRENT_TIMESTAMP';
	private $updated_at  = 'CURRENT_TIMESTAMP';
	private $is_lock  = '0';
	public $table = "corporation_finances";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->year,0,$len,"utf-8");
			else return substr($this->year,0,$len);
		}
		return $this->year;
	}

	public function getTotalAssets($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->total_assets,0,$len,"utf-8");
			else return substr($this->total_assets,0,$len);
		}
		return $this->total_assets;
	}

	public function getTotalLiabilities($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->total_liabilities,0,$len,"utf-8");
			else return substr($this->total_liabilities,0,$len);
		}
		return $this->total_liabilities;
	}

	public function getCurrentLiabilities($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->current_liabilities,0,$len,"utf-8");
			else return substr($this->current_liabilities,0,$len);
		}
		return $this->current_liabilities;
	}

	public function getSalesRevenue($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->sales_revenue,0,$len,"utf-8");
			else return substr($this->sales_revenue,0,$len);
		}
		return $this->sales_revenue;
	}

	public function getMainRevenue($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->main_revenue,0,$len,"utf-8");
			else return substr($this->main_revenue,0,$len);
		}
		return $this->main_revenue;
	}

	public function getOwnerEquity($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->owner_equity,0,$len,"utf-8");
			else return substr($this->owner_equity,0,$len);
		}
		return $this->owner_equity;
	}

	public function getNetProfit($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->net_profit,0,$len,"utf-8");
			else return substr($this->net_profit,0,$len);
		}
		return $this->net_profit;
	}

	public function getRD($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->RD,0,$len,"utf-8");
			else return substr($this->RD,0,$len);
		}
		return $this->RD;
	}

	public function getEmployeesNumber()
	{
		return $this->employees_number;
	}

	public function getResearcherNumber()
	{
		return $this->researcher_number;
	}

	public function getFinanceLeaderFullName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->finance_leader_full_name,0,$len,"utf-8");
			else return substr($this->finance_leader_full_name,0,$len);
		}
		return $this->finance_leader_full_name;
	}

	public function getFinanceLeaderPhone($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->finance_leader_phone,0,$len,"utf-8");
			else return substr($this->finance_leader_phone,0,$len);
		}
		return $this->finance_leader_phone;
	}

	public function getFinanceLeaderMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->finance_leader_mobile,0,$len,"utf-8");
			else return substr($this->finance_leader_mobile,0,$len);
		}
		return $this->finance_leader_mobile;
	}

	public function getFinanceLeaderEmail($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->finance_leader_email,0,$len,"utf-8");
			else return substr($this->finance_leader_email,0,$len);
		}
		return $this->finance_leader_email;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getIsLock()
	{
		return $this->is_lock;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->year !== $v)
		{
			$this->year = $v;
			$this->fieldData["year"] = $v;
		}
		return $this;

	}

	public function setTotalAssets($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->total_assets !== $v)
		{
			$this->total_assets = $v;
			$this->fieldData["total_assets"] = $v;
		}
		return $this;

	}

	public function setTotalLiabilities($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->total_liabilities !== $v)
		{
			$this->total_liabilities = $v;
			$this->fieldData["total_liabilities"] = $v;
		}
		return $this;

	}

	public function setCurrentLiabilities($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->current_liabilities !== $v)
		{
			$this->current_liabilities = $v;
			$this->fieldData["current_liabilities"] = $v;
		}
		return $this;

	}

	public function setSalesRevenue($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->sales_revenue !== $v)
		{
			$this->sales_revenue = $v;
			$this->fieldData["sales_revenue"] = $v;
		}
		return $this;

	}

	public function setMainRevenue($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->main_revenue !== $v)
		{
			$this->main_revenue = $v;
			$this->fieldData["main_revenue"] = $v;
		}
		return $this;

	}

	public function setOwnerEquity($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->owner_equity !== $v)
		{
			$this->owner_equity = $v;
			$this->fieldData["owner_equity"] = $v;
		}
		return $this;

	}

	public function setNetProfit($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->net_profit !== $v)
		{
			$this->net_profit = $v;
			$this->fieldData["net_profit"] = $v;
		}
		return $this;

	}

	public function setRD($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->RD !== $v)
		{
			$this->RD = $v;
			$this->fieldData["RD"] = $v;
		}
		return $this;

	}

	public function setEmployeesNumber($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->employees_number !== $v)
		{
			$this->employees_number = $v;
			$this->fieldData["employees_number"] = $v;
		}
		return $this;

	}

	public function setResearcherNumber($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->researcher_number !== $v)
		{
			$this->researcher_number = $v;
			$this->fieldData["researcher_number"] = $v;
		}
		return $this;

	}

	public function setFinanceLeaderFullName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->finance_leader_full_name !== $v)
		{
			$this->finance_leader_full_name = $v;
			$this->fieldData["finance_leader_full_name"] = $v;
		}
		return $this;

	}

	public function setFinanceLeaderPhone($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->finance_leader_phone !== $v)
		{
			$this->finance_leader_phone = $v;
			$this->fieldData["finance_leader_phone"] = $v;
		}
		return $this;

	}

	public function setFinanceLeaderMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->finance_leader_mobile !== $v)
		{
			$this->finance_leader_mobile = $v;
			$this->fieldData["finance_leader_mobile"] = $v;
		}
		return $this;

	}

	public function setFinanceLeaderEmail($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->finance_leader_email !== $v)
		{
			$this->finance_leader_email = $v;
			$this->fieldData["finance_leader_email"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setIsLock($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_lock !== $v)
		{
			$this->is_lock = $v;
			$this->fieldData["is_lock"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `corporation_finances` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"user_id" => $this->getUserId(),
			"year" => $this->getYear(),
			"total_assets" => $this->getTotalAssets(),
			"total_liabilities" => $this->getTotalLiabilities(),
			"current_liabilities" => $this->getCurrentLiabilities(),
			"sales_revenue" => $this->getSalesRevenue(),
			"main_revenue" => $this->getMainRevenue(),
			"owner_equity" => $this->getOwnerEquity(),
			"net_profit" => $this->getNetProfit(),
			"RD" => $this->getRD(),
			"employees_number" => $this->getEmployeesNumber(),
			"researcher_number" => $this->getResearcherNumber(),
			"finance_leader_full_name" => $this->getFinanceLeaderFullName(),
			"finance_leader_phone" => $this->getFinanceLeaderPhone(),
			"finance_leader_mobile" => $this->getFinanceLeaderMobile(),
			"finance_leader_email" => $this->getFinanceLeaderEmail(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			"is_lock" => $this->getIsLock(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->user_id = '';
		$this->year = '';
		$this->total_assets = '';
		$this->total_liabilities = '';
		$this->current_liabilities = '';
		$this->sales_revenue = '';
		$this->main_revenue = '';
		$this->owner_equity = '';
		$this->net_profit = '';
		$this->RD = '';
		$this->employees_number = '';
		$this->researcher_number = '';
		$this->finance_leader_full_name = '';
		$this->finance_leader_phone = '';
		$this->finance_leader_mobile = '';
		$this->finance_leader_email = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->is_lock = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["year"]) && $this->year = $data["year"];
		isset($data["total_assets"]) && $this->total_assets = $data["total_assets"];
		isset($data["total_liabilities"]) && $this->total_liabilities = $data["total_liabilities"];
		isset($data["current_liabilities"]) && $this->current_liabilities = $data["current_liabilities"];
		isset($data["sales_revenue"]) && $this->sales_revenue = $data["sales_revenue"];
		isset($data["main_revenue"]) && $this->main_revenue = $data["main_revenue"];
		isset($data["owner_equity"]) && $this->owner_equity = $data["owner_equity"];
		isset($data["net_profit"]) && $this->net_profit = $data["net_profit"];
		isset($data["RD"]) && $this->RD = $data["RD"];
		isset($data["employees_number"]) && $this->employees_number = $data["employees_number"];
		isset($data["researcher_number"]) && $this->researcher_number = $data["researcher_number"];
		isset($data["finance_leader_full_name"]) && $this->finance_leader_full_name = $data["finance_leader_full_name"];
		isset($data["finance_leader_phone"]) && $this->finance_leader_phone = $data["finance_leader_phone"];
		isset($data["finance_leader_mobile"]) && $this->finance_leader_mobile = $data["finance_leader_mobile"];
		isset($data["finance_leader_email"]) && $this->finance_leader_email = $data["finance_leader_email"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["is_lock"]) && $this->is_lock = $data["is_lock"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}