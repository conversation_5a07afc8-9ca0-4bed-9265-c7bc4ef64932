<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseEngineWorkerWidgets extends BaseModel
{
	private $id;
	private $engine_worker_id;
	private $engine_widget_id;
	private $subject;
	private $widget_name;
	private $class_name;
	private $note;
	private $configs;
	private $orders  = '0';
	public $table = "engine_worker_widgets";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getEngineWorkerId()
	{
		return $this->engine_worker_id;
	}

	public function getEngineWidgetId()
	{
		return $this->engine_widget_id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getWidgetName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->widget_name,0,$len,"utf-8");
			else return substr($this->widget_name,0,$len);
		}
		return $this->widget_name;
	}

	public function getClassName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->class_name,0,$len,"utf-8");
			else return substr($this->class_name,0,$len);
		}
		return $this->class_name;
	}

	public function getNote($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->note,0,$len,"utf-8");
			else return substr($this->note,0,$len);
		}
		return $this->note;
	}

	public function getConfigs($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->configs,0,$len,"utf-8");
			else return substr($this->configs,0,$len);
		}
		return $this->configs;
	}

	public function getOrders()
	{
		return $this->orders;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setEngineWorkerId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->engine_worker_id !== $v)
		{
			$this->engine_worker_id = $v;
			$this->fieldData["engine_worker_id"] = $v;
		}
		return $this;

	}

	public function setEngineWidgetId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->engine_widget_id !== $v)
		{
			$this->engine_widget_id = $v;
			$this->fieldData["engine_widget_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setWidgetName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->widget_name !== $v)
		{
			$this->widget_name = $v;
			$this->fieldData["widget_name"] = $v;
		}
		return $this;

	}

	public function setClassName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->class_name !== $v)
		{
			$this->class_name = $v;
			$this->fieldData["class_name"] = $v;
		}
		return $this;

	}

	public function setNote($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->note !== $v)
		{
			$this->note = $v;
			$this->fieldData["note"] = $v;
		}
		return $this;

	}

	public function setConfigs($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->configs !== $v)
		{
			$this->configs = $v;
			$this->fieldData["configs"] = $v;
		}
		return $this;

	}

	public function setOrders($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->orders !== $v)
		{
			$this->orders = $v;
			$this->fieldData["orders"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `engine_worker_widgets` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"engine_worker_id" => $this->getEngineWorkerId(),
			"engine_widget_id" => $this->getEngineWidgetId(),
			"subject" => $this->getSubject(),
			"widget_name" => $this->getWidgetName(),
			"class_name" => $this->getClassName(),
			"note" => $this->getNote(),
			"configs" => $this->getConfigs(),
			"orders" => $this->getOrders(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->engine_worker_id = '';
		$this->engine_widget_id = '';
		$this->subject = '';
		$this->widget_name = '';
		$this->class_name = '';
		$this->note = '';
		$this->configs = '';
		$this->orders = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["engine_worker_id"]) && $this->engine_worker_id = $data["engine_worker_id"];
		isset($data["engine_widget_id"]) && $this->engine_widget_id = $data["engine_widget_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["widget_name"]) && $this->widget_name = $data["widget_name"];
		isset($data["class_name"]) && $this->class_name = $data["class_name"];
		isset($data["note"]) && $this->note = $data["note"];
		isset($data["configs"]) && $this->configs = $data["configs"];
		isset($data["orders"]) && $this->orders = $data["orders"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}