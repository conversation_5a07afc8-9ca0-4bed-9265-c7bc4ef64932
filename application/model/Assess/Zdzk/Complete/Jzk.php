<?php
namespace App\model\Assess\Zdzk\Complete;
class Jzk extends Common
{
    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得5分；≥20%-29%，得3分；≥10%-19%，得1分；其余情况不得分。
     * 满分：5
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = 'DRGs组数低于全省该专科平均水平10%，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 5;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得5分';
        }elseif($percent>=20){
            $data['score'] = 3;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得3分';
        }elseif($percent>=10){
            $data['score'] = 1;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得1分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）
     * 与全省该专科平均水平比较：≥30%，得5分；≥20%-29%，得3分；≥10%-19%，得1分；其余情况不得分。
     * 满分：5
     * @return array
     */
    function getAbilityCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = 'CMI低于全省该专科平均水平10%，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 5;
            $data['desc'] = 'CMI≥全省该专科平均水平30%，得5分';
        }elseif($percent>=20){
            $data['score'] = 3;
            $data['desc'] = 'CMI≥全省该专科平均水平20%-29%，得3分';
        }elseif($percent>=10){
            $data['score'] = 1;
            $data['desc'] = 'CMI≥全省该专科平均水平10%-19%，得1分';
        }
        return $data;
    }

    /**
     * 本专科急危重病例救治能力（高风险组病例死亡率）
     * 高风险组病例死亡率=高风险组死亡例数/高风险组病例数× 100%
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得10分；低于平均水平20-29%，得8分；低于平均水平10-19%，得6分；低于平均水平0-9%，得4分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyGfxScore()
    {
        $data['score'] = 0;
        $data['desc'] = '高风险组病例死亡率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_gfx');
        $avgData = $this->project->getAvgDataByIndexCode('safety_gfx');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 10;
            $data['desc'] = '全省该专科平均水平为0，该专科高风险组病例死亡率也为0，得10分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 10;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平30%以上，得10分';
        }elseif($percent>=20){
            $data['score'] = 8;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平20-29%，得8分';
        }elseif($percent>=10){
            $data['score'] = 6;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平10-19%，得6分';
        }elseif($percent>=0){
            $data['score'] = 4;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平0-9%，得4分';
        }
        return $data;
    }

    /**
     * 人员基本结构情况
     * 分值：45
     * 1.急诊科应当有固定的急诊医师，且不少于在岗医师的75%得5分；急诊监护室固定医师与床位比不低于0.6:1得10分，护士与床位比不少于2.5:1得5分。
    2.医师队伍年龄结构合理，年龄50岁及以上的医师比例为10%-20%得5分，40-49岁的医师比例为30%-50%得5分，不符合要求不得分。
    3.医师队伍研究生学历人员比例≥70%，得15分；60-69%，得10分；50-59%，得5分；≤49%，不得分。
     * @return array
     */
    function getTalentPersonStructureScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $desc = [];
        //在岗医师数量
        $zgysCount = $this->project->getDataByIndexCode('talent_bed_zgys')->getData();
        //固定的急诊医师数量
        $jzysCount = $this->project->getDataByIndexCode('talent_bed_jzys')->getData();
        if($jzysCount/$zgysCount*100>=75){
            $data['score']+=5;
            $desc[] = '急诊科应当有固定的急诊医师，且不少于在岗医师的75%，得5分';
        }
        //急诊监护室固定医师与床位比
        $ysPercent = $this->project->getDataByIndexCode('talent_bed_ysbl')->getData();
        if($ysPercent>=0.6){
            $data['score']+=10;
            $desc[] = '急诊监护室固定医师与床位比不低于0.6:1，得10分';
        }
        //护士与床位比
        $hsPercent = $this->project->getDataByIndexCode('talent_bed_hsbl')->getData();
        if($hsPercent>=2.5){
            $data['score']+=5;
            $desc[] = '护士与床位比不少于2.5:1，得5分';
        }

        $members = $this->project->memberStructures('apply',true);
        $ysCount = 0;
        $yjsCount = 0;
        $oldCount = 0;
        $youngCount = 0;
        while($member = $members->getObject()){
            if($member->getPersonnelType()=='医师') $ysCount++;
            if($member->getPersonnelType()=='医师' && strstr($member->getEducation(),'研究生')!==false) $yjsCount++;
            if($member->getPersonnelType()=='医师' && $member->getAge()>=50) $oldCount++;
            if($member->getPersonnelType()=='医师' && $member->getAge()>=40 && $member->getAge()<50) $youngCount++;
        }
        $yjsPercent = (float)sprintf('%.2f',$yjsCount/$ysCount*100);
        $oldPercent = (float)sprintf('%.2f',$oldCount/$ysCount*100);
        $youngPercent = (float)sprintf('%.2f',$youngCount/$ysCount*100);

        if($oldPercent>=10 && $oldPercent<=20) {
            $data['score']+=5;
            $desc[] = '年龄50岁及以上的医师比例为10%-20%，得5分';
        }
        if($youngPercent>=30 && $youngPercent<=50){
            $data['score']+=5;
            $desc[] = '40-49岁的医师比例为30%-50%，得5分';
        }

        if($yjsPercent>=70){
            $data['score']+=15;
            $desc[] = '医师队伍研究生学历人员比例≥70%，得15分';
        }elseif($yjsPercent>=60){
            $data['score']+=10;
            $desc[] = '医师队伍研究生学历人员比例60-69%，得10分';
        }elseif($yjsPercent>=50){
            $data['score']+=8;
            $desc[] = '医师队伍研究生学历人员比例50-59%，得5分';
        }
        $data['desc'] = implode('；',$desc);
        return $data;
    }


    /**
     * 年接受下级医院急危重症和疑难病患者转诊数量
     * 分值：15
     * ≥20%，得15分；≥10-19%，10-19%得10分；低于10%，不得分。
    上报非急诊科数据，该项不得分。
     * @return array
     */
    function getInfluenceFsnlZzslScore()
    {
        $data['score'] = 0;
        $data['desc'] = '低于10%，不得分';
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_zzsl');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData>=20){
            $data['score']+=15;
            $data['desc'] = '≥20%，得15分';
        }elseif($avgData>=10){
            $data['score']+=10;
            $data['desc'] = '≥10-19%，得10分';
        }
        return $data;
    }

    /**
     * 牵头或参与制定国家级诊疗规范、指南等的数量（个）
     * 分值：21
     * 牵头制定诊疗规范/指南15分/个，参与制定（署名）6分/个，累计不超过21分。
     * @return array
     */
    function getInfluenceYxlZlgfScore()
    {
        $guidelines = $this->project->guidelines('apply',true);
        $count['lead'] = 0;
        $count['join'] = 0;
        while($guideline = $guidelines->getObject()){
            if($guideline->getType()=='牵头') $count['lead']++;
            if($guideline->getType()=='参与') $count['join']++;
        }
        $score = 0;
        $desc = [];
        if($count['lead']){
            $score+=$count['lead']*15;
            $desc[] = '牵头制定诊疗规范/指南'.$count['lead'].'个';
        }
        if($count['join']){
            $score+=$count['join']*6;
            $desc[] = '参与制定（署名）'.$count['join'].'个';
        }
        return ['score'=>$score,'desc'=>implode('：',$desc)];
    }

    /**
     * 承担国家、省、市级质控中心工作个数
     * 分值：28
     * 承担国家级质控中心28分/个，省级质控中心20分/个，市质控中心10分/个，没有则不得分。承担多个级别质控中心时，按照就高不就低原则得分。
     * @return array
     */
    function getInfluenceYxlZkzxScore()
    {
        $centers = $this->project->centers('apply',true);
        $count['nation'] = 0;
        $count['province'] = 0;
        $count['city'] = 0;
        while($center = $centers->getObject()){
            if($center->getLevel()=='国家级') $count['nation']++;
            if($center->getLevel()=='省级') $count['province']++;
            if($center->getLevel()=='市级') $count['city']++;
        }
        $score = 0;
        $desc = [];
        if($count['nation']){
            $score+=$count['nation']*28;
            $desc[] = '承担国家级质控中心'.$count['nation'].'个';
        }
        if($count['province']){
            $score+=$count['province']*20;
            $desc[] = '承担省级质控中心'.$count['province'].'个';
        }
        if($count['city']){
            $score+=$count['city']*10;
            $desc[] = '承担市级质控中心'.$count['city'].'个';
        }
        return ['score'=>$score,'desc'=>implode('：',$desc)];
    }

    /**
     * 省级医学重点学科（甲级）
     * 属于省级医学重点专科（甲级）得4分，属于省级医学重点专科（乙级）得2分，否则不得分。
     * @return array
     */
//    function getSpecialtyCxjcZdxkScore()
//    {
//        $data['score'] = 0;
//        $data['desc'] = '不属于省级医学重点学科（甲级/乙级），不得分';
//        if($this->project->getDataByIndexCode('specialty_cxjc_zdxk_yi')->getData()=='是'){
//            $data['score'] = 2;
//            $data['desc'] = '属于省级医学重点学科（乙级），得2分';
//        }
//        if($this->project->getDataByIndexCode('specialty_cxjc_zdxk')->getData()=='是'){
//            $data['score'] = 4;
//            $data['desc'] = '属于省级医学重点学科（甲级），得4分';
//        }
//        return $data;
//    }

}