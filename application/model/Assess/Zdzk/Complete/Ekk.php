<?php
namespace App\model\Assess\Zdzk\Complete;
use Sofast\Core\Log;
use Sofast\Core\sf;

class Ekk extends Common
{
    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得20分；≥20%-29%，得14分；≥10%-19%，得7分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得20分';
        }elseif($percent>=20){
            $data['score'] = 14;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得14分';
        }elseif($percent>=10){
            $data['score'] = 7;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得7分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'DRGs组数≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）
     * 与全省该专科平均水平比较：≥30%，得20分；≥20%-29%，得14分；≥10%-19%，得7分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = 'CMI≥全省该专科平均水平30%，得20分';
        }elseif($percent>=20){
            $data['score'] = 14;
            $data['desc'] = 'CMI≥全省该专科平均水平20%-29%，得14分';
        }elseif($percent>=10){
            $data['score'] = 7;
            $data['desc'] = 'CMI≥全省该专科平均水平10%-19%，得7分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'CMI≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 四级手术占比
     * 40%及以上得20分。小于40%的按照降序赋分：专科四级手术占比/40%*20。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=40){
            $data['score'] = 20;
            $data['desc'] = '四级手术占比≥40%，得20分';
        }else{
            $score = $percent/40*20;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比小于40%的按照降序赋分：{$percent}/40*20";
        }
        return $data;
    }

    /**
     * 微创手术占比
     * 25% 及以上得20分。小于25%的按照降序赋分：专科微创手术占比/25%*20。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=25){
            $data['score'] = 20;
            $data['desc'] = '微创手术占比≥25%，得20分';
        }else{
            $score = $percent/25*20;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于25%的按照降序赋分：{$percent}/25*20";
        }
        return $data;
    }

    /**
     * 低龄儿手术占比
     * 该专科申报单位排名：
     * 新生儿手术占比排名前25%（含25%），得10分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得6分；其余得3分。
     * 婴儿手术占比排名前25%（含25%），得10分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得6分；其余得3分。
     * @return array
     */
    function getAbilityXessScore()
    {
        //新生儿手术占比
        $item['xsr'] = $this->getAbilityXessXsrScore();
        //婴儿手术占比
        $item['yr'] = $this->getAbilityXessYrScore();
        $data['score'] = $item['xsr']['score'] + $item['yr']['score'];
        if($data['score']>20){
            $data['score'] = 20;
        }
        $desc[] = $item['xsr']['desc'];
        $desc[] = $item['yr']['desc'];
        $data['desc'] = implode(';',$desc);
        return $data;
    }

    /**
     * 新生儿手术占比排名前25%（含25%），得10分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得6分；其余得3分。
     * @return array
     */
    function getAbilityXessXsrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '新生儿手术占比无数据，不得分';
        $desc = [];
        $indexCode = 'ability_xsss';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '新生儿手术占比无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();
        $db = sf::getLib('db');
        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `complete_datas` where data != '无' and index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and project_id in (select project_id from completes where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."') GROUP BY project_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数


        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `complete_datas` where data != '无' and index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and project_id in (select project_id from completes where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."') GROUP BY project_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 10;
            $desc[] = '新生儿手术占比排名前25%（'.$rank.'/'.$count.'），得10分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '新生儿手术占比排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '新生儿手术占比排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 3;
            $desc[] = '新生儿手术占比排名未进前75%，得3分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 婴儿手术占比排名前25%（含25%），得10分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得6分；其余得3分。
     * @return array
     */
    function getAbilityXessYrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '婴儿手术占比无数据，不得分';
        $desc = [];
        $indexCode = 'ability_yess';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '婴儿手术占比无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();
        $db = sf::getLib('db');
        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `complete_datas` where data != '无' and index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and project_id in (select project_id from completes where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."') GROUP BY project_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `complete_datas` where data != '无' and index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and project_id in (select project_id from completes where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."') GROUP BY project_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 10;
            $desc[] = '婴儿手术占比排名前25%（'.$rank.'/'.$count.'），得10分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '婴儿手术占比排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '婴儿手术占比排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 3;
            $desc[] = '婴儿手术占比排名未进前75%，得3分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 本专科RW≥2的病例占比（高优指标）
     * 与全省该专科平均水平比较：≥30%，得20分；≥20%-29%，得14分；≥10%-19%，得7分；其余情况不得分。
     * @return array
     */
    function getAbilityRwScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_rw');
        $avgData = $this->project->getAvgDataByIndexCode('ability_rw');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平30%，得20分';
        }elseif($percent>=20){
            $data['score'] = 14;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平20%-29%，得14分';
        }elseif($percent>=10){
            $data['score'] = 7;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平10%-19%，得7分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'RW≥2的病例占比≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 中低风险组死亡率
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得16分；低于平均水平20-29%，得12分；低于平均水平10-19%，得8分；低于平均水平0-9%，得4分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyZdfxScore()
    {
        $data['score'] = 0;
        $data['desc'] = '中低风险组死亡率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_zdfx');
        $avgData = $this->project->getAvgDataByIndexCode('safety_zdfx');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 16;
            $data['desc'] = '全省该专科平均水平为0，该专科中低风险组死亡率也为0，得16分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 16;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平30%以上，得16分';
        }elseif($percent>=20){
            $data['score'] = 12;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平20-29%，得12分';
        }elseif($percent>=10){
            $data['score'] = 8;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平10-19%，得8分';
        }elseif($percent>=0){
            $data['score'] = 4;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平0-9%，得4分';
        }
        return $data;
    }

    /**
     * 本专科急危重病例救治能力（高风险组病例死亡率）
     * 高风险组病例死亡率=高风险组死亡例数/高风险组病例数× 100%
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得16分；低于平均水平20-29%，得12分；低于平均水平10-19%，得8分；低于平均水平0-9%，得4分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyGfxScore()
    {
        $data['score'] = 0;
        $data['desc'] = '高风险组病例死亡率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_gfx');
        $avgData = $this->project->getAvgDataByIndexCode('safety_gfx');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 16;
            $data['desc'] = '全省该专科平均水平为0，该专科高风险组病例死亡率也为0，得16分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 16;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平30%以上，得16分';
        }elseif($percent>=20){
            $data['score'] = 12;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平20-29%，得12分';
        }elseif($percent>=10){
            $data['score'] = 8;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平10-19%，得8分';
        }elseif($percent>=0){
            $data['score'] = 4;
            $data['desc'] = '高风险组病例死亡率低于全省该专科平均水平0-9%，得4分';
        }
        return $data;
    }

    /**
     * 本专业重点病种（单病种）医疗质量管理情况
     * 累计不超过16分
     * @return array
     */
    function getSafetyYlzlScore()
    {
        $worker = $this->project->worker('complete',true);
        $configs = $worker->getConfigs('safety');
        $safetyData = [];
        foreach ($configs['zdbz'] as $k=>$config){
            if($config==0) continue;
            switch ($k){
                case 'cjfy':
                    //单病种次均费用
                    $safetyData['cjfy'] = $this->getSafetyYlzlCjfyScore();
                    break;
                case 'cfl':
                    //单病种出院31天重返率
                    $safetyData['cfl'] = $this->getSafetyYlzlCflScore();
                    break;
                case 'swl':
                    //患者死亡率
                    $safetyData['swl'] = $this->getSafetyYlzlSwlScore();
                    break;
                case 'bls':
                    //单病种例数
                    $safetyData['bls'] = $this->getSafetyYlzlBlsScore();
                    break;
                case 'zyr':
                    //单病种平均住院日
                    $safetyData['zyr'] = $this->getSafetyYlzlZyrScore();
                    break;
                case 'bsl':
                    //单病种病死率
                    $safetyData['bsl'] = $this->getSafetyYlzlBslScore();
                    break;
            }
        }
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $desc = [];
        foreach ($safetyData as $k=>$v){
            $data['score'] +=  $v['score'];
            $desc[] =  $v['desc'];
        }
        if($data['score']>=16){
            $desc[] = '累计不超过16分';
        }
        $desc = array_filter($desc);
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 单病种病死率
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得8分；低于平均水平0-19%，得4分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlBslScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种病死率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_bsl');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_bsl','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '单病种病死率无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 8;
            $data['desc'] = '全省该专科平均水平为0，该专科单病种病死率也为0，得8分';
            return $data;
        }

        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 8;
            $data['desc'] = '单病种病死率低于全省该专科平均水平20%及以上，得8分';
        }elseif($percent>=0){
            $data['score'] = 4;
            $data['desc'] = '单病种病死率低于全省该专科平均水平0-19%，得4分';
        }
        return $data;
    }

    /**
     * 出院31天重返率
     * 与全省该专科平均水平比较：低于平均水平20%及以上，得8分；低于平均水平0-19%，得4分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlCflScore()
    {
        $data['score'] = 0;
        $data['desc'] = '出院31天重返率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_cfl');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_cfl','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '出院31天重返率无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 8;
            $data['desc'] = '全省该专科平均水平为0，该专科出院31天重返率也为0，得8分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 8;
            $data['desc'] = '出院31天重返率低于全省该专科平均水平20%及以上，得8分';
        }elseif($percent>=0){
            $data['score'] = 4;
            $data['desc'] = '出院31天重返率低于全省该专科平均水平0-19%，得4分';
        }
        return $data;
    }
}