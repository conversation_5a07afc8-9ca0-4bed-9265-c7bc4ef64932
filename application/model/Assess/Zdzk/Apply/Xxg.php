<?php
namespace App\model\Assess\Zdzk\Apply;
use Sofast\Core\sf;

class Xxg extends Common
{

    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得20分；≥20%-29%，得14分；≥10%-19%，得7分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = 'DRGs组数低于全省该专科平均水平10%，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得20分';
        }elseif($percent>=20){
            $data['score'] = 14;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得14分';
        }elseif($percent>=10){
            $data['score'] = 7;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得7分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）
     * 与全省该专科平均水平比较：≥30%，得20分；≥20%-29%，得14分；≥10%-19%，得7分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = 'CMI低于全省该专科平均水平10%，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = 'CMI≥全省该专科平均水平30%，得20分';
        }elseif($percent>=20){
            $data['score'] = 14;
            $data['desc'] = 'CMI≥全省该专科平均水平20%-29%，得14分';
        }elseif($percent>=10){
            $data['score'] = 7;
            $data['desc'] = 'CMI≥全省该专科平均水平10%-19%，得7分';
        }
        return $data;
    }

    /**
     * 四级手术占比
     *40%及以上得20分。小于40%的按照降序赋分：专科四级手术占比/40%*20。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=40){
            $data['score'] = 20;
            $data['desc'] = '四级手术占比≥40%，得20分';
        }else{
            $score = $percent/40*20;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比小于40%的按照降序赋分：{$percent}/40*20";
        }
        return $data;
    }

    /**
     * 微创手术占比
     * 25%及以上得20分。小于25%的按照降序赋分：专科四级手术占比/25%*20。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=25){
            $data['score'] = 20;
            $data['desc'] = '微创手术占比≥25%，得20分';
        }else{
            $score = $percent/25*20;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于25%的按照降序赋分：{$percent}/25*20";
        }
        return $data;
    }

    /**
     * 介入手术占比
     * 该专科申报单位排名：介入手术占比排名前25%（含25%），得20分；排名25%-50%（含50%），得14分；排名50%-75%（含75%）,得7分；其余不得分。。
     * 满分：20
     * @return array
     */
    function getAbilityJrssXxgScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前指定范围，不得分';
        $desc = [];
        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('ability');
        $indexCodes = [];
        $indexCodesStr = [];
        foreach ($configs['jrss_xxg'] as $k=>$v){
            $indexCodes[] = 'ability_jrss_xxg_'.$k;
            $indexCodesStr[] = "'".'ability_jrss_xxg_'.$k."'";
        }

        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $db = sf::getLib('db');
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `project_datas` where data != '无' and index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and project_id in (select project_id from projects where subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."' and statement IN (10,18,20,28,29,30)) GROUP BY project_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `project_datas` where data != '无' and index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and project_id in (select project_id from projects where subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."' and statement IN (10,18,20,28,29,30)) GROUP BY project_id having data > '{$avgData}' ORDER BY data desc) t";

        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));

        $rankStr = $rank.'/'.$count;

        if($rankPercent<=25){
            $data['score'] += 20;
            $desc[] = '介入手术占比排名前25%（'.$rankStr.'），得20分';
        }elseif($rankPercent<=50){
            $data['score'] = 14;
            $desc[] = '介入手术占比排名25%-50%（'.$rankStr.'），得14分';
        }elseif($rankPercent<=75){
            $data['score'] = 7;
            $desc[] = '介入手术占比排名50%-75%（'.$rankStr.'），得7分';
        }else{
            $data['score'] = 0;
            $desc[] = '介入手术占比排名靠后（'.$rankStr.'），不得分';
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 费用消耗指数
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得40分；低于平均水平20-29%，得30分；低于平均水平10-19%，得20分；低于平均水平0-9%，得10分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientFyxhScore()
    {
        $data['score'] = 0;
        $data['desc'] = '费用消耗指数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_fyxh');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_fyxh');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 40;
            $data['desc'] = '全省该专科平均水平为0，该专科费用消耗指数也为0，得40分';
            return $data;
        }
        if($avgData>$provinceAvgData || $provinceAvgData==0){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 40;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平30%以上，得40分';
        }elseif($percent>=20){
            $data['score'] = 30;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平20-29%，得30分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平10-19%，得20分';
        }elseif($percent>=0){
            $data['score'] = 10;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平0-9%，得10分';
        }
        return $data;
    }

    /**
     * 时间消耗指数
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得40分；≤20-29%，得30分；≤10-19%，得20分；≤0-9%，得10分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientSjxhScore()
    {
        $data['score'] = 0;
        $data['desc'] = '时间消耗指数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_sjxh');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_sjxh');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 40;
            $data['desc'] = '全省该专科平均水平为0，该专科时间消耗指数也为0，得40分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 40;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平30%以上，得40分';
        }elseif($percent>=20){
            $data['score'] = 30;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平20-29%，得30分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平10-19%，得20分';
        }elseif($percent>=0){
            $data['score'] = 10;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平0-9%，得10分';
        }
        return $data;
    }

    /**
     * 本专科RW≥2的病例占比（高优指标）
     * 与全省该专科平均水平比较：≥30%，得20分；≥20%-29%，得14分；≥10%-19%，得7分；其余情况不得分。
     * @return array
     */
    function getAbilityRwScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_rw');
        $avgData = $this->project->getAvgDataByIndexCode('ability_rw');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平30%，得20分';
        }elseif($percent>=20){
            $data['score'] = 14;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平20%-29%，得14分';
        }elseif($percent>=10){
            $data['score'] = 7;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平10%-19%，得7分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'RW≥2的病例占比≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }


}