<?php
namespace App\model\Assess\Zdzk\Apply;
class Enk extends Common
{
    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得60分；≥20%-29%，得40分；≥10%-19%，得20分；其余情况不得分
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = 'DRGs组数低于全省该专科平均水平10%，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 60;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得60分';
        }elseif($percent>=20){
            $data['score'] = 40;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得40分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得20分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）
     * 与全省该专科平均水平比较：≥30%，得60分；≥20%-29%，得40分；≥10%-19%，得20分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = 'CMI低于全省该专科平均水平10%，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 60;
            $data['desc'] = 'CMI≥全省该专科平均水平30%，得60分';
        }elseif($percent>=20){
            $data['score'] = 40;
            $data['desc'] = 'CMI≥全省该专科平均水平20%-29%，得40分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = 'CMI≥全省该专科平均水平10%-19%，得20分';
        }
        return $data;
    }

    /**
     * 中低风险组死亡率
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得20分；低于平均水平20-29%，得15分；低于平均水平10-19%，得10分；低于平均水平0-9%，得5分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyZdfxScore()
    {
        $data['score'] = 0;
        $data['desc'] = '中低风险组死亡率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_zdfx');
        $avgData = $this->project->getAvgDataByIndexCode('safety_zdfx');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 20;
            $data['desc'] = '全省该专科平均水平为0，该专科中低风险组死亡率也为0，得20分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平30%以上，得20分';
        }elseif($percent>=20){
            $data['score'] = 15;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平20-29%，得15分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平10-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 5;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平0-9%，得5分';
        }
        return $data;
    }

    /**
     * 本专业重点病种（单病种）医疗质量管理情况
     * 累计不超过20分
     * @return array
     */
    function getSafetyYlzlScore()
    {
        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('safety');
        $safetyData = [];
        foreach ($configs['zdbz'] as $k=>$config){
            if($config==0) continue;
            switch ($k){
                case 'cjfy':
                    //单病种次均费用
                    $safetyData['cjfy'] = $this->getSafetyYlzlCjfyScore();
                    break;
                case 'cfl':
                    //单病种出院31天重返率
                    $safetyData['cfl'] = $this->getSafetyYlzlCflScore();
                    break;
                case 'swl':
                    //患者死亡率
                    $safetyData['swl'] = $this->getSafetyYlzlSwlScore();
                    break;
                case 'bls':
                    //单病种例数
                    $safetyData['bls'] = $this->getSafetyYlzlBlsScore();
                    break;
                case 'zyr':
                    //单病种平均住院日
                    $safetyData['zyr'] = $this->getSafetyYlzlZyrScore();
                    break;
                case 'bsl':
                    //单病种病死率
                    $safetyData['bsl'] = $this->getSafetyYlzlBslScore();
                    break;
            }
        }
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $desc = [];
        foreach ($safetyData as $k=>$v){
            $data['score'] +=  $v['score'];
            $desc[] =  $v['desc'];
        }
        if($data['score']>=20){
            $desc[] = '累计不超过20分';
        }
        $desc = array_filter($desc);
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 单病种病死率
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得10分；低于平均水平0-19%，得8分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlBslScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种病死率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_bsl');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_bsl','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '单病种病死率无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 10;
            $data['desc'] = '全省该专科平均水平为0，该专科单病种病死率也为0，得10分';
            return $data;
        }

        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 10;
            $data['desc'] = '单病种病死率低于全省该专科平均水平20%及以上，得10分';
        }elseif($percent>=0){
            $data['score'] = 8;
            $data['desc'] = '单病种病死率低于全省该专科平均水平0-19%，得8分';
        }
        return $data;
    }

    /**
     * 出院31天重返率
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得10分；低于平均水平0-19%，得8分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlCflScore()
    {
        $data['score'] = 0;
        $data['desc'] = '出院31天重返率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_cfl');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_cfl','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '出院31天重返率无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 10;
            $data['desc'] = '全省该专科平均水平为0，该专科出院31天重返率也为0，得10分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 10;
            $data['desc'] = '出院31天重返率低于全省该专科平均水平20%及以上，得10分';
        }elseif($percent>=0){
            $data['score'] = 8;
            $data['desc'] = '出院31天重返率低于全省该专科平均水平0-19%，得8分';
        }
        return $data;
    }
}