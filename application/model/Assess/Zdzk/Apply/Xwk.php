<?php
namespace App\model\Assess\Zdzk\Apply;
class Xwk extends Common
{
    /**
     * 四级手术占比
     * 60%及以上得24分。小于60%的按照降序赋分：专科四级手术占比/60%*30。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=60){
            $data['score'] = 24;
            $data['desc'] = '四级手术占比≥40%，得24分';
        }else{
            $score = $percent/60*24;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比小于60%的按照降序赋分：{$percent}/60*24";
        }
        return $data;
    }

    /**
     * 微创手术占比
     * 50% 及以上得24分。小于50%的按照降序赋分：专科微创手术占比/50%*24。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=50){
            $data['score'] = 24;
            $data['desc'] = '微创手术占比≥50%，得24分';
        }else{
            $score = $percent/50*24;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于50%的按照降序赋分：{$percent}/50*24";
        }
        return $data;
    }
}