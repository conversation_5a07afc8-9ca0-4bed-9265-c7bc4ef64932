<?php
namespace App\model\Assess\Zdzk\Apply;
class Ebh extends Common
{

    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得30分；≥20%-29%，得20分；≥10%-19%，得10分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 30;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得30分';
        }elseif($percent>=20){
            $data['score'] = 20;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得20分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'DRGs组数≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）
     * 与全省该专科平均水平比较：≥30%，得30分；≥20%-29%，得20分；≥10%-19%，得10分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 30;
            $data['desc'] = 'CMI≥全省该专科平均水平30%，得30分';
        }elseif($percent>=20){
            $data['score'] = 20;
            $data['desc'] = 'CMI≥全省该专科平均水平20%-29%，得20分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = 'CMI≥全省该专科平均水平10%-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'CMI≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 四级手术占比
     * 15%及以上得30分。小于15%的按照降序赋分：专科四级手术占比/15%*30。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=15){
            $data['score'] = 30;
            $data['desc'] = '四级手术占比≥15%，得30分';
        }else{
            $score = $percent/15*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比小于15%的按照降序赋分：{$percent}/15*30";
        }
        return $data;
    }

    /**
     * 微创手术占比
     * 10% 及以上得30分。小于10%的按照降序赋分：专科微创手术占比/10%*30。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=10){
            $data['score'] = 30;
            $data['desc'] = '微创手术占比≥10%，得30分';
        }else{
            $score = $percent/10*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于10%的按照降序赋分：{$percent}/10*30";
        }
        return $data;
    }

    /**
     * 单病种病死率
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得2分；低于平均水平0-19%，得1分；高于平均水平，不得分。
     * 4.喉恶性肿瘤：C32.900（考核单病种病死率和单病种出院31天重返率）
     * @return array
     */
    function getSafetyYlzlBslScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种病死率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_bsl');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_bsl','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 2;
            $data['desc'] = '单病种病死率低于全省该专科平均水平20%及以上，得2分';
        }elseif($percent>=0){
            $data['score'] = 1;
            $data['desc'] = '单病种病死率低于全省该专科平均水平0-19%，得1分';
        }
        return $data;
    }

    /**
     * 单病种平均住院日
     * 与全省该专科平均水平比较：低于平均水平20%及以上，得8分；低于平均水平0-19%，得4分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlZyrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种平均住院日高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_zyr');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_zyr','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 8;
            $data['desc'] = '单病种平均住院日低于全省该专科平均水平20%及以上，得8分';
        }elseif($percent>=0){
            $data['score'] = 4;
            $data['desc'] = '单病种平均住院日低于全省该专科平均水平0-19%，得4分';
        }
        return $data;
    }


}