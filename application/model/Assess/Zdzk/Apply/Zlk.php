<?php
namespace App\model\Assess\Zdzk\Apply;
class ZLK extends Common
{

    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得30分；≥20%-29%，得20分；≥10%-19%，得10分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 30;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得30分';
        }elseif($percent>=20){
            $data['score'] = 20;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得20分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'DRGs组数≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）
     * 与全省该专科平均水平比较：≥30%，得30分；≥20%-29%，得20分；≥10%-19%，得10分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 30;
            $data['desc'] = 'CMI≥全省该专科平均水平30%，得30分';
        }elseif($percent>=20){
            $data['score'] = 20;
            $data['desc'] = 'CMI≥全省该专科平均水平20%-29%，得20分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = 'CMI≥全省该专科平均水平10%-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'CMI≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 四级手术占比
     * 40%及以上得30分。小于40%的按照降序赋分：专科四级手术占比/40%*30。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=40){
            $data['score'] = 30;
            $data['desc'] = '四级手术占比≥40%，得30分';
        }else{
            $score = $percent/40*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比小于40%的按照降序赋分：{$percent}/40*30";
        }
        return $data;
    }

    /**
     * 微创手术占比
     * 25% 及以上得30分。小于25%的按照降序赋分：专科微创手术占比/25%*30。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=25){
            $data['score'] = 30;
            $data['desc'] = '微创手术占比≥25%，得30分';
        }else{
            $score = $percent/25*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于25%的按照降序赋分：{$percent}/25*30";
        }
        return $data;
    }

    /**
     * 费用消耗指数
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得40分；低于平均水平20-29%，得30分；低于平均水平10-19%，得20分；低于平均水平0-9%，得10分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientFyxhScore()
    {
        $data['score'] = 0;
        $data['desc'] = '费用消耗指数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_fyxh');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_fyxh');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 40;
            $data['desc'] = '全省该专科平均水平为0，该专科费用消耗指数也为0，得40分';
            return $data;
        }
        if($avgData>$provinceAvgData || $provinceAvgData==0){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 40;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平30%以上，得40分';
        }elseif($percent>=20){
            $data['score'] = 30;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平20-29%，得30分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平10-19%，得20分';
        }elseif($percent>=0){
            $data['score'] = 10;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平0-9%，得10分';
        }
        return $data;
    }

    /**
     * 时间消耗指数
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得40分；低于平均水平20-29%，得30分；低于平均水平10-19%，得20分；低于平均水平0-9%，得10分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientSjxhScore()
    {
        $data['score'] = 0;
        $data['desc'] = '时间消耗指数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_sjxh');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_sjxh');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 40;
            $data['desc'] = '全省该专科平均水平为0，该专科时间消耗指数也为0，得40分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 40;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平30%以上，得40分';
        }elseif($percent>=20){
            $data['score'] = 30;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平20-29%，得30分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平10-19%，得20分';
        }elseif($percent>=0){
            $data['score'] = 10;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平0-9%，得10分';
        }
        return $data;
    }

    /**
     * 中低风险组死亡率
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得24分；低于平均水平20-29%，得18分；低于平均水平10-19%，得12分；低于平均水平0-9%，得6分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyZdfxScore()
    {
        $data['score'] = 0;
        $data['desc'] = '中低风险组死亡率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_zdfx');
        $avgData = $this->project->getAvgDataByIndexCode('safety_zdfx');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 24;
            $data['desc'] = '全省该专科平均水平为0，该专科中低风险组死亡率也为0，得24分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 24;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平30%以上，得24分';
        }elseif($percent>=20){
            $data['score'] = 18;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平20-29%，得18分';
        }elseif($percent>=10){
            $data['score'] = 12;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平10-19%，得12分';
        }elseif($percent>=0){
            $data['score'] = 6;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平0-9%，得6分';
        }
        return $data;
    }


    /**
     * 单病种平均住院日
     * 与全省该专科平均水平比较：低于平均水平20%及以上，得12分；低于平均水平0-19%，得6分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlZyrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种平均住院日高于全省该专科平均水平，不得分';

        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_zyr');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_zyr','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '单病种平均住院日无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 12;
            $data['desc'] = '全省该专科平均水平为0，该专科单病种平均住院日也为0，得12分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 12;
            $data['desc'] = '单病种平均住院日低于全省该专科平均水平20%及以上，得12分';
        }elseif($percent>=0){
            $data['score'] = 6;
            $data['desc'] = '单病种平均住院日低于全省该专科平均水平0-19%，得6分';
        }
        return $data;
    }

    /**
     * 次均费用
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得12分；低于平均水平0-19%，得6分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlCjfyScore()
    {
        $data['score'] = 0;
        $data['desc'] = '次均费用高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_cjfy');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_cjfy','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '次均费用无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 12;
            $data['desc'] = '全省该专科平均水平为0，该专科次均费用也为0，得12分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 12;
            $data['desc'] = '次均费用低于全省该专科平均水平20%及以上，得12分';
        }elseif($percent>=0){
            $data['score'] = 6;
            $data['desc'] = '次均费用低于全省该专科平均水平0-19%，得6分';
        }
        return $data;
    }


}