<?php
namespace App\model\Assess\Zdzk\Stage;
use Sofast\Core\Sf;

class Mzk extends Common
{

    /**
     * 四级手术麻醉占比
     * 该专科申报单位排名：四级手术占比排名前25%（含25%），得36分；排名25%-50%（含50%），得24分；排名50%-75%（含75%）,得12分；其余得0分。。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        $indexCode = 'ability_sjss';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and user_role = 2) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT project_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and user_role = 2) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 36;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得36分';
        }elseif($rankPercent<=50){
            $data['score'] = 24;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得24分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }


    /**
     * 时间消耗指数
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得16分；低于平均水平20-29%，得12分；低于平均水平10-19%，得8分；低于平均水平0-9%，得4分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientSjxhScore()
    {
        $data['score'] = 0;
        $data['desc'] = '时间消耗指数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_sjxh');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_sjxh');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 16;
            $data['desc'] = '全省该专科平均水平为0，该专科时间消耗指数也为0，得16分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 16;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平30%以上，得16分';
        }elseif($percent>=20){
            $data['score'] = 12;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平20-29%，得12分';
        }elseif($percent>=10){
            $data['score'] = 8;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平10-19%，得8分';
        }elseif($percent>=0){
            $data['score'] = 4;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平0-9%，得4分';
        }
        return $data;
    }

    /**
     * 梯队结构配置情况
     * 分值：30
     * 医师团队高级职称比例20%-40%、中级职称比例30-50%，各得15分。范围外不得分。
     * @return array
     */
    function getTalentTeamStructureScore()
    {
        $score = 0;
        $desc = [];
        $members = $this->project->memberStructures('apply',true);
        $ysCount = 0;
        $highCount = 0;
        $middleCount = 0;
        while($member = $members->getObject()){
            if($member->getPersonnelType()=='医师') $ysCount++;
            if($member->getPersonnelType()=='医师' && in_array($member->getTitleType(),['正高','副高'])) $highCount++;
            if($member->getPersonnelType()=='医师' && $member->getTitleType()=='中级') $middleCount++;
        }
        $highPercent = (float)sprintf('%.2f',$highCount/$ysCount*100);
        $middlePercent = (float)sprintf('%.2f',$middleCount/$ysCount*100);

        $desc[] = '医师总人数：'.$ysCount;
        if($highPercent>=20 && $highPercent<=40) {
            $score+=15;
            $desc[] = '高级职称医师人数：'.$highCount.'，高级职称医师比例：'.$highPercent.'%，得15分';
        }else{
            $desc[] = '高级职称医师人数：'.$highCount.'，高级职称医师比例：'.$highPercent.'%，不符合要求，不得分';
        }
        if($middlePercent>=30 && $middlePercent<=50) {
            $score+=15;
            $desc[] = '中级职称医师人数：'.$middleCount.'，中级职称医师比例：'.$middlePercent.'%，得15分';
        }else{
            $desc[] = '中级职称医师人数：'.$middleCount.'，中级职称医师比例：'.$middlePercent.'%，不符合要求，不得分';
        }
        return ['score'=>$score,'desc'=>implode('；',$desc)];
    }

    /**
     * 亚专科学科带头人及骨干发展情况
     * 获得国家级人才称号45分，省级人才称号30分，省卫健委或者省中药管理局人才称号15分。个人或多人获得多个级别人才称号时，按照就高不就低原则，只取其中一人获得的最高级别人才称号得分。
     * @return array
     */
    function getTalentSeniorScore()
    {
        $members = $this->project->memberSeniors('apply',true);
        $count['nation'] = 0;
        $count['province'] = 0;
        $count['swjw'] = 0;
        $count['szyj'] = 0;
        while($member = $members->getObject()){
            if($member->getHonorLevel()=='国家') $count['nation']++;
            if($member->getHonorLevel()=='省级') $count['province']++;
            if($member->getHonorLevel()=='省卫健委') $count['swjw']++;
            if($member->getHonorLevel()=='省中医局') $count['szyj']++;
        }
        $score = 0;
        $desc = '未获得人才称号，不得分';
        if($count['nation']){
            $score=45;
            $desc = '获得国家级人才称号，得45分';
        }elseif($count['province']){
            $score=30;
            $desc = '获得省级人才称号，得30分';
        }elseif($count['swjw'] || $count['szyj']){
            $score=15;
            $desc = '获得省卫健委或者省中药管理局人才称号，得15分';
        }
        return ['score'=>$score,'desc'=>$desc];
    }
}