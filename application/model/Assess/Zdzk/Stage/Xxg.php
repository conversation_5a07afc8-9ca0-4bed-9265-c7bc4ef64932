<?php
namespace App\model\Assess\Zdzk\Stage;
class Xxg extends Common
{

    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得20分；≥20%-29%，得14分；≥10%-19%，得7分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得20分';
        }elseif($percent>=20){
            $data['score'] = 14;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得14分';
        }elseif($percent>=10){
            $data['score'] = 7;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得7分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'DRGs组数≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }
    /**
     * 微创手术占比
     * 25%及以上得20分。小于25%的按照降序赋分：专科四级手术占比/25%*20。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=25){
            $data['score'] = 20;
            $data['desc'] = '微创手术占比≥25%，得20分';
        }else{
            $score = $percent/25*20;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于25%的按照降序赋分：{$percent}/25*20";
        }
        return $data;
    }

}