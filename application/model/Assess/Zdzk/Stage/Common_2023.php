<?php
namespace App\model\Assess\Zdzk\Stage;
use App\Model\RuleItems;
use Sofast\Core\Log;
use Sofast\Core\Sf;

class Common_2023 extends RuleItems
{
    public function getScoreByMark($mark)
    {
//        Log::write($this->project->getSubjectCode());
//        Log::write($this->project->getProjectId());
//        Log::write($mark);
        $data['score'] = 0;
        $data['desc'] = '';
        switch ($mark){
            case 'premise_local_input':
            case 'premise_company_input':
            case 'premise_security':
                $data['desc'] = '是(Y)';
                break;
            case 'strength_performance_level':
                $data = $this->getStrengthPerformanceLevelScore();
                break;
            case 'strength_performance_rank':
                $data = $this->getStrengthPerformanceRankScore();
                break;
            case 'strength_performance':
                $data = $this->getStrengthPerformanceScore();
                break;
            case 'strength_hygiene':
                $data = $this->getStrengthHygieneScore();
                break;
            case 'strength_hygiene2':
                $data = $this->getStrengthHygiene2Score();
                break;
            case 'layout_ztbj':
                $data = $this->getLayoutZtbj();
                break;
            case 'layout_eicu':
                $data = $this->getLayoutEicu();
                break;
            case 'prehospital_jzk':
                $data = $this->getPrehospitalJzk();
                break;
            case 'workload_jzk':
                $data = $this->getWorkloadJzk();
                break;
            case 'personnel_zzk_zgrd':
                $data = $this->getPersonnelZzkZgrd();
                break;
            case 'personnel_zzk_ysbl':
                $data = $this->getPersonnelZzkYsbl();
                break;
            case 'personnel_zzk_hsbl':
                $data = $this->getPersonnelZzkHsbl();
                break;
            case 'personnel_zzk_kszr':
                $data = $this->getPersonnelZzkKszr();
                break;
            case 'personnel_zzk_hsz':
                $data = $this->getPersonnelZzkHsz();
                break;
            case 'technique_jbjj':
                $data = $this->getTechniqueJbjj();
                break;
            case 'technique_wzjj':
                $data = $this->getTechniqueWzjj();
                break;
            case 'platform_xxxt':
                $data = $this->getPlatformXxxt();
                break;
            case 'scale_mzrs':
                $data = $this->getScaleMzrsScore();
                break;
            case 'scale_cyrs':
                $data = $this->getScaleCyrsScore();
                break;
            case 'efficient_zyr':
                $data = $this->getEfficientZyrScore();
                break;
            case 'ability_drgs':
                $data = $this->getAbilityDrgsScore();
                break;
            case 'ability_drgs_fk':
                $data = $this->getAbilityDrgsFkScore();
                break;
            case 'ability_drgs_ck':
                $data = $this->getAbilityDrgsCkScore();
                break;
            case 'ability_eicu_drgs':
                $data = $this->getAbilityEicuDrgs();
                break;
            case 'ability_cmi':
                $data = $this->getAbilityCmiScore();
                break;
            case 'ability_cmi_fk':
                $data = $this->getAbilityCmiFkScore();
                break;
            case 'ability_cmi_ck':
                $data = $this->getAbilityCmiCkScore();
                break;
            case 'ability_eicu_cmi':
                $data = $this->getAbilityEicuCmiScore();
                break;
            case 'ability_icu':
                $data = $this->getAbilityIcuScore();
                break;
            case 'safety_zzk_apache':
                $data = $this->getSafetyZzkApache();
                break;
            case 'safety_zzk_icucfl':
                $data = $this->getSafetyZzkIcucfl();
                break;
            case 'ability_xytx':
                $data = $this->getAbilityXytxScore();
                break;
            case 'ability_tjhl':
                $data = $this->getAbilityTjhlScore();
                break;
            case 'ability_rw':
                $data = $this->getAbilityRwScore();
                break;
            case 'ability_sjss':
                $data = $this->getAbilitySjssScore();
                break;
            case 'ability_sjss_fk':
                $data = $this->getAbilitySjssFkScore();
                break;
            case 'ability_sjss_ck':
                $data = $this->getAbilitySjssCkScore();
                break;
            case 'ability_swmz':
                $data = $this->getAbilitySwmzScore();
                break;
            case 'ability_rjmz':
                $data = $this->getAbilityRjmzScore();
                break;
            case 'efficient_fmmz':
                $data = $this->getEfficientFmmzScore();
                break;
            case 'efficient_mzqx':
                $data = $this->getEfficientMzqxScore();
                break;
            case 'efficient_ssqx':
                $data = $this->getEfficientSsqxScore();
                break;
            case 'efficient_ztmy':
                $data = $this->getEfficientZtmyScore();
                break;
            case 'safety_mzk_mz1':
                $data = $this->getSafetyMzkMz1Score();
                break;
            case 'safety_mzk_mz2':
                $data = $this->getSafetyMzkMz2Score();
                break;
            case 'safety_mzk_mz3':
                $data = $this->getSafetyMzkMz3Score();
                break;
            case 'safety_mzk_mz4':
                $data = $this->getSafetyMzkMz4Score();
                break;
            case 'safety_mzk_mz5':
                $data = $this->getSafetyMzkMz5Score();
                break;
            case 'safety_mzk_mz6':
                $data = $this->getSafetyMzkMz6Score();
                break;
            case 'safety_mzk_mz7':
                $data = $this->getSafetyMzkMz7Score();
                break;
            case 'safety_mzk_mz8':
                $data = $this->getSafetyMzkMz8Score();
                break;
            case 'ability_sjsl':
                $data = $this->getAbilitySjslScore();
                break;
            case 'ability_wcss':
                $data = $this->getAbilityWcssScore();
                break;
            case 'ability_wcss_fk':
                $data = $this->getAbilityWcssFkScore();
                break;
            case 'ability_wcss_ck':
                $data = $this->getAbilityWcssCkScore();
                break;
            case 'ability_njss':
                $data = $this->getAbilityNjssScore();
                break;
            case 'ability_cyrs':
                $data = $this->getAbilityCyrsScore();
                break;
            case 'ability_jrss_xhk':
                $data = $this->getAbilityJrssXhkScore();
                break;
            case 'ability_jrss_snk':
                $data = $this->getAbilityJrssSnkScore();
                break;
            case 'ability_jrss_xxg':
                $data = $this->getAbilityJrssXxgScore();
                break;
            case 'ability_wzbz':
                $data = $this->getAbilityWzbzScore();
                break;
            case 'ability_xess':
                $data = $this->getAbilityXessScore();
                break;
            case 'efficient_fyxh':
                $data = $this->getEfficientFyxhScore();
                break;
            case 'efficient_fyxh_fk':
                $data = $this->getEfficientFyxhFkScore();
                break;
            case 'efficient_fyxh_ck':
                $data = $this->getEfficientFyxhCkScore();
                break;
            case 'efficient_sjxh':
                $data = $this->getEfficientSjxhScore();
                break;
            case 'efficient_sjxh_fk':
                $data = $this->getEfficientSjxhFkScore();
                break;
            case 'efficient_sjxh_ck':
                $data = $this->getEfficientSjxhCkScore();
                break;
            case 'efficient_zqjr':
                $data = $this->getEfficientZqjrScore();
                break;
            case 'safety_ddfs':
                $data = $this->getSafetyDdfsScore();
                break;
            case 'safety_ylss':
                $data = $this->getSafetyYlssScore();
                break;
            case 'safety_fygr':
                $data = $this->getSafetyFygrScore();
                break;
            case 'safety_xlgr':
                $data = $this->getSafetyXlgrScore();
                break;
            case 'safety_nlgr':
                $data = $this->getSafetyNlgrScore();
                break;
            case 'safety_tsyl':
                $data = $this->getSafetyTsylScore();
                break;
            case 'safety_zdfx':
                $data = $this->getSafetyZdfxScore();
                break;
            case 'safety_zdfx_fk':
                $data = $this->getSafetyZdfxFkScore();
                break;
            case 'safety_zdfx_ck':
                $data = $this->getSafetyZdfxCkScore();
                break;
            case 'safety_ylzl':
                $data = $this->getSafetyYlzlScore();
                break;
            case 'safety_ylzl_swl':
                $data = $this->getSafetyYlzlSwlScore();
                break;
            case 'safety_ylzl_bls':
                $data = $this->getSafetyYlzlBlsScore();
                break;
            case 'safety_ylzl_zyr':
                $data = $this->getSafetyYlzlZyrScore();
                break;
            case 'safety_ylzl_cjfy':
                $data = $this->getSafetyYlzlCjfyScore();
                break;
            case 'safety_ylzl_cfl':
                $data = $this->getSafetyYlzlCflScore();
                break;
            case 'safety_ylzl_bsl':
                $data = $this->getSafetyYlzlBslScore();
                break;
            case 'safety_aqyy':
                $data = $this->getSafetyAqyyScore();
                break;
            case 'innovate_kyxm':
                $data = $this->getInnovateKyxmScore();
                break;
            case 'specialty_cxjc_zdxk':
                $data = $this->getSpecialtyCxjcZdxkScore();
                break;
            case 'innovate_zdxk':
                $data = $this->getInnovateZdxkScore();
                break;
            case 'innovate_syjs':
                $data = $this->getInnovateSyjsScore();
                break;
            case 'innovate_kycg_hjxx':
                $data = $this->getInnovateKycgHjxxScore();
                break;
            case 'innovate_kycg_fmzl':
                $data = $this->getInnovateKycgFmzlScore();
                break;
            case 'innovate_kycg':
                $data = $this->getInnovateKycgScore();
                break;
            case 'talent_person_structure':
                $data = $this->getTalentPersonStructureScore();
                break;
            case 'talent_team_structure':
                $data = $this->getTalentTeamStructureScore();
                break;
            case 'talent_senior':
                $data = $this->getTalentSeniorScore();
                break;
            case 'talent_yhb':
                $data = $this->getTalentYhbScore();
                break;
            case 'influence_fsnl_ychz':
                $data = $this->getInfluenceFsnlYchz();
                break;
            case 'influence_fsnl_swhz':
                $data = $this->getInfluenceFsnlSwhzScore();
                break;
            case 'influence_fsnl_swhz_fk':
                $data = $this->getInfluenceFsnlSwhzFkScore();
                break;
            case 'influence_fsnl_swhz_ck':
                $data = $this->getInfluenceFsnlSwhzCkScore();
                break;
            case 'influence_fsnl_zzsl':
                $data = $this->getInfluenceFsnlZzslScore();
                break;
            case 'influence_fsnl_zzsl_fk':
                $data = $this->getInfluenceFsnlZzslFkScore();
                break;
            case 'influence_fsnl_zzsl_ck':
                $data = $this->getInfluenceFsnlZzslCkScore();
                break;
            case 'influence_fsnl_pxsl':
                $data = $this->getInfluenceFsnlPxslScore();
                break;
            case 'influence_yxl_zlgf':
                $data = $this->getInfluenceYxlZlgfScore();
                break;
            case 'influence_yxl_zkzx':
                $data = $this->getInfluenceYxlZkzxScore();
                break;
            case 'influence_yxl_xszz':
                $data = $this->getInfluenceYxlXszzScore();
                break;
            case 'influence_yxl_jkcb':
                $data = $this->getInfluenceYxlJkcbScore();
                break;
            case 'plus_fudan_nation':
                $data = $this->getPlusFudanNationScore();
                break;
            case 'plus_fudan_southwest':
                $data = $this->getPlusFudanSouthwestScore();
                break;
            case 'plus_fudan':
                $data = $this->getPlusFudanScore();
                break;
            case 'plus_huaxi':
                $data = $this->getPlusHuaxiScore();
                break;
        }
        if($data['score']>$this->getWeight()) $data['score'] = $this->getWeight();
        return $data;
    }

    /**
     * 绩效考核 - 国家三级公立医院绩效考核等级
     * 分值：44
     * 国家三级公立医院绩效考核等级：综合医院考核等级为“A+”及以上的得44分，“A”级得20分，“A”级以下不得分。
     * @param $value
     * @return array
     */
    function getStrengthPerformanceLevelScore()
    {
        switch ($this->project->getDataByIndexCode('strength_performance_level')->getData()){
            case 'A+':
                return ['score'=>44,'desc'=>'国家三级公立医院绩效考核等级为“A+”及以上的得44分'];
            case 'A':
                return ['score'=>20,'desc'=>'国家三级公立医院绩效考核等级为“A”的得20分'];
            default:
                return ['score'=>0,'desc'=>'国家三级公立医院绩效考核等级为“A”级以下不得分'];
        }
    }

    /**
     * 绩效考核 - 四川省三级公立医院绩效考核排名
     * 分值：34
     * 四川省三级公立医院绩效考核排名：“A”（排名前1%-20%）为34分、“B”（排名前21%-75%）为10分、“C”（排名前76%-100%）及以下不得分。
     * @param $value
     * @return array
     */
    function getStrengthPerformanceRankScore()
    {
        switch ($this->project->getDataByIndexCode('strength_performance_rank')->getData()){
            case 'A':
                return ['score'=>34,'desc'=>'四川省三级公立医院绩效考核排名为“A”级得34分'];
            case 'B':
                return ['score'=>10,'desc'=>'四川省三级公立医院绩效考核排名为“B”级得10分'];
            default:
                return ['score'=>0,'desc'=>'四川省三级公立医院绩效考核排名为“C”级及以下不得分'];
        }
    }

    /**
     * 国家、省三级公立医院绩效考核等级、排名
     * 分值：34
     * 1.国家三级公立医院绩效考核等级：综合医院考核等级为“A+”及以上的得44分，“A”级得20分，“A”级以下不得分。
     * 2.四川省三级公立医院绩效考核排名：“A”（排名前1%-20%）为34分、“B”（排名前21%-75%）为10分、“C”（排名前76%-100%）及以下不得分。
     * 累计不超过78分。
     * @param $value
     * @return array
     */
    function getStrengthPerformanceScore()
    {
        $data['score'] = 0;
        $data['desc'] = '均不符合条件，不得分';
        $desc = [];
        $strengthData['level'] = $this->getStrengthPerformanceLevelScore();
        $strengthData['rank'] = $this->getStrengthPerformanceRankScore();
        $data['score'] = $strengthData['level']['score']+$strengthData['rank']['score'];
        $data['score'] = $data['score']>=78 ? 78 : $data['score'];
        $desc[] = $strengthData['level']['desc'];
        $desc[] = $strengthData['rank']['desc'];
        if($data['score']>=78){
            $desc[] = '累计不超过78分';
        }
        $desc = array_filter($desc);
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 承担属地新冠肺炎定点医疗机构等公共卫生职能
     * 为属地新冠肺炎定点医疗机构得26分，否则不得分。
     * @return array
     */
    function getStrengthHygieneScore()
    {
        $data['score'] = $this->project->getDataByIndexCode('strength_hygiene')->getData()=='是' ? 26 : 0;
        $data['desc'] = $this->project->getDataByIndexCode('strength_hygiene')->getData()=='是' ? '是属地新冠肺炎定点医疗机构，得26分。' : '不是属地新冠肺炎定点医疗机构，不得分。';
        return $data;
    }

    /**
     * 承担相关公共卫生职能
     * 1.对口支援、中国援外医疗队、公共卫生任务，每完成一项得6分。
     * 2.承担突发公共事件和重大事故灾害的紧急医疗救援任务以及配合突发公共卫生事件防控工作，得8分。
     * 累计不超过26分。
     * @return array
     */
    function getStrengthHygiene2Score()
    {
        $data['score'] = 0;
        $data['desc'] = '未承担相关公共卫生职能，不得分。';
        $arrs = [
            ['subject'=>'对口支援','code'=>'dkzy','score'=>6],
            ['subject'=>'中国援外医疗队','code'=>'ywyl','score'=>6],
            ['subject'=>'公共卫生任务','code'=>'ggws','score'=>6],
            ['subject'=>'承担突发公共事件和重大事故灾害的紧急医疗救援任务以及配合突发公共卫生事件防控工作','code'=>'zdsg','score'=>8],
        ];
        $finishCount = 0;
        foreach ($arrs as $k=>$arr){
            $count = $this->project->getAttachementCount('apply_strength_hygiene2_'.$arr['code']);
            if($count>0){
                $data['score']+=$arr['score'];
                $finishCount++;
            }
        }
        if($finishCount>0) $data['desc'] = '完成'.$finishCount.'项，得'.$data['score'].'分';

        return $data;
    }

    /**
     * 急诊总体布局
     * 急诊留观室床位＜20张扣5分，急诊抢救室床位＜10张扣5分，每张床位平均面积低于12平方米扣5分，抢救复苏单元＜2张扣5分，无隔离抢救室扣5分，无隔离观察室（病房）扣5分，扣完为止。
     * 满分：20
     * @return array
     */
    function getLayoutZtbj()
    {
        $data['score'] = 20;
        $data['desc'] = '所有条件均满足，不扣分';
        $desc = [];
        if($this->project->getDataByIndexCode('layout_ztbj_1')->getData()<20){
            $data['score']-=5;
            $desc[] = '急诊留观室床位＜20张，扣5分';
        }
        if($this->project->getDataByIndexCode('layout_ztbj_2')->getData()<10){
            $data['score']-=5;
            $desc[] = '急诊抢救室床位＜10张，扣5分';
        }
        if($this->project->getDataByIndexCode('layout_ztbj_3')->getData()<12){
            $data['score']-=5;
            $desc[] = '每张床位平均面积低于12m²，扣5分';
        }
        if($this->project->getDataByIndexCode('layout_ztbj_4')->getData()<2){
            $data['score']-=5;
            $desc[] = '抢救复苏单元床位数＜2张，扣5分';
        }
        if($this->project->getDataByIndexCode('layout_ztbj_5')->getData()!='是'){
            $data['score']-=5;
            $desc[] = '无隔离抢救室，扣5分';
        }
        if($this->project->getDataByIndexCode('layout_ztbj_6')->getData()!='是'){
            $data['score']-=5;
            $desc[] = '无隔离观察室（病房），扣5分';
        }

        if($data['score']<0) $data['score']=0;
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * EICU建设
     * 1、EICU床不少于16张；2.开放式病床每张床的占地面积为15-18m2;3.最少配备1个单间病房,面积为18-25m2；4.基本辅助用房包括医师办公室、主任办公室、工作人员休息室、中央工作站、治疗室、配药室、仪器室、更衣室、清洁室、污废物处理室、值班室、盥洗室等。
     * 每项不满足扣10分，扣完为止
     * 满分：20
     * @return array
     */
    function getLayoutEicu()
    {
        $data['score'] = 20;
        $data['desc'] = '所有条件均满足，不扣分';
        $desc = [];
        if($this->project->getDataByIndexCode('layout_eicu_1')->getData()<16){
            $data['score']-=10;
            $desc[] = 'EICU床少于16张，扣10分';
        }
        if($this->project->getDataByIndexCode('layout_eicu_2')->getData()<15){
            $data['score']-=10;
            $desc[] = '开放式病床每张床的占地面积为15-18m²，不满足该项，扣10分';
        }
        if($this->project->getDataByIndexCode('layout_eicu_3')->getData()<1 || $this->project->getDataByIndexCode('layout_eicu_4')->getData()<18){
            $data['score']-=10;
            $desc[] = '最少配备1个单间病房,面积为18-25m²，不满足该项，扣10分';
        }
        if($this->project->getDataByIndexCode('layout_eicu_5')->getData()!='是'){
            $data['score']-=10;
            $desc[] = '未配备基本辅助用房，扣10分';
        }
        if($data['score']<0) $data['score']=0;
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 院前急救质量
     * 具有与“120”中心的合作协议4分，急诊科接到调度中心指令3分钟出车率达到95%得3分，＜95%不得分，急救反应时间达标3分，不达标不得分。
     * 急救反应时间≤15分钟（城区）、25分钟（郊区）
     * 满分：10
     * @return array
     */
    function getPrehospitalJzk()
    {
        $data['score'] = 0;
        $data['desc'] = '均不符合，不得分。';
        $desc = [];
        if($this->project->getDataByIndexCode('prehospital_jzk_1')->getData()=='是'){
            $data['score']+=4;
            $desc[] = '具有与“120”中心的合作协议，得4分';
        }
        if($this->project->getDataByIndexCode('prehospital_jzk_2')->getData()>=95){
            $data['score']+=3;
            $desc[] = '急诊科接到调度中心指令3分钟出车率达到95%，得3分';
        }
        if($this->project->getDataByIndexCode('prehospital_jzk_3')->getData()<=15 && $this->project->getDataByIndexCode('prehospital_jzk_4')->getData()<=25){
            $data['score']+=3;
            $desc[] = '急救反应时间≤15分钟（城区）、25分钟（郊区），得3分';
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 急诊工作量与质量
     * 1.评估前3年，急诊年均人次（排除会诊重复挂号和儿科急诊人数）。每年8万人次得5分，每增加1万加1分，直至封顶分（10分），低于8万人次不得分；
     * 2.评估前3年，每年急诊I、II级患者占比，大于等于5%，得5分，每增加1%加1分，直至封顶分（10分），每降低1%减0.25分，除外儿科急诊抢救人数；
     * 3.评估前3年，抢救成功率≥92%，得10分；≥85%，得5分，<85%不得分。
     * 满分：30
     * @return array
     */
    function getWorkloadJzk()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $desc = [];
        $avgData = $this->project->getAvgDataByIndexCode('workload_jzk_jzrs');
        if((string)$avgData=='无'){
            $desc[] = '急诊年均人次无数据，不得分';
        }else{
            if($avgData>=130000){
                $data['score']+=10;
                $desc[] = '急诊年均人次达13万人以上，得10分';
            }elseif($avgData>=120000){
                $data['score']+=9;
                $desc[] = '急诊年均人次达12万人，得9分';
            }elseif($avgData>=110000){
                $data['score']+=8;
                $desc[] = '急诊年均人次达11万人，得8分';
            }elseif($avgData>=100000){
                $data['score']+=7;
                $desc[] = '急诊年均人次达10万人，得7分';
            }elseif($avgData>=90000){
                $data['score']+=6;
                $desc[] = '急诊年均人次达9万人，得6分';
            }elseif($avgData>=80000){
                $data['score']+=5;
                $desc[] = '急诊年均人次达8万人，得5分';
            }else{
                $desc[] = '急诊年均人次低于8万人次不得分';
            }
        }
        $avgDataYj = $this->project->getAvgDataByIndexCode('specialty_gzzl_yjhz');
        $avgDataEj = $this->project->getAvgDataByIndexCode('specialty_gzzl_ejhz');
        if((string)$avgDataYj=='无' && (string)$avgDataEj=='无') {
            $desc[] = '急诊I、II级患者占比无数据，不得分';
        }else{
            if($avgDataYj>=10 && $avgDataEj>=10){
                $data['score']+=10;
                $desc[] = '急诊I、II级患者占比，大于等于10%，得10分';
            }elseif($avgDataYj>=9 && $avgDataEj>=9){
                $data['score']+=9;
                $desc[] = '每年急诊I、II级患者占比，大于等于9%，得9分';
            }elseif($avgDataYj>=8 && $avgDataEj>=8){
                $data['score']+=8;
                $desc[] = '每年急诊I、II级患者占比，大于等于8%，得8分';
            }elseif($avgDataYj>=7 && $avgDataEj>=7){
                $data['score']+=7;
                $desc[] = '每年急诊I、II级患者占比，大于等于7%，得7分';
            }elseif($avgDataYj>=6 && $avgDataEj>=6){
                $data['score']+=6;
                $desc[] = '每年急诊I、II级患者占比，大于等于6%，得6分';
            }elseif($avgDataYj>=5 && $avgDataEj>=5){
                $data['score']+=5;
                $desc[] = '每年急诊I、II级患者占比，大于等于5%，得5分';
            }else{
                $desc[] = '每年急诊I、II级患者占比，小于5%，不得分';
            }
        }

        $avgDataQj = $this->project->getAvgDataByIndexCode('specialty_gzzl_qjcgl');
        if((string)$avgDataQj=='无') {
            $desc[] = '抢救成功率无数据，不得分';
        }else{
            if($avgDataQj>=92){
                $data['score']+=10;
                $desc[] = '抢救成功率≥92%，得10分';
            }elseif($avgDataQj>=85){
                $data['score']+=5;
                $desc[] = '抢救成功率≥85%，得5分';
            }else{
                $desc[] = '抢救成功率<85%，不得分';
            }
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }


    /**
     * 重症资格认定医师比例
     * 1.有一定比例的医师必须持有重症医学医师执业证书,是执业注册范围为重症医学科的执业医师（应占比例70%)。
     * 2.有一定比例的医师必须持有中华医学会“重症医学专科资质培训合格证”或者从事重症医学科工作两年（应占比例70%）。
     * 以上两条均达标得24分，不达标得0分。
     * 满分：24
     * @return array
     */
    function getPersonnelZzkZgrd()
    {
        $data['score'] = 0;
        $data['desc'] = '不达标得0分';
        $desc = [];
        $percent1 = (float)$this->project->getDataByIndexCode('personnel_zzk_zgrd1')->getData();
        $percent2 = (float)$this->project->getDataByIndexCode('personnel_zzk_zgrd2')->getData();
        if($percent1>=70){
            $desc[] = '持有重症医学医师执业证书，且执业注册范围为重症医学科的执业医师人数占比≥70%，只达标一条，不得分';
        }
        if($percent2>=70){
            $desc[] = '持有中华医学会“重症医学专科资质培训合格证”或者从事重症医学科工作两年及以上人数占比≥70%，只达标一条，不得分';
        }
        if($percent1>=70 && $percent2>=70){
            $data['score'] = 24;
            $desc = [];
            $desc[] = '重症资格认定医师比例两条均达标，得24分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 重症医学科医床比
     * 重症医学科医师人数与重症医学科开放床位数比应为0.8:1以上。比例达标得24分，在达标的基础上比例每下降10%减6分。
     * 满分：24
     * @return array
     */
    function getPersonnelZzkYsbl()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $desc = [];
        $percent = (float)$this->project->getAvgDataByIndexCode('personnel_zzk_ysbl');
        if($percent>=0.8){
            $data['score']=24;
            $desc[] = '重症医学科医师人数与重症医学科开放床位数比为'.$percent.'，得24分';
        }elseif($percent>=0.7){
            $data['score']=18;
            $desc[] = '重症医学科医师人数与重症医学科开放床位数比为'.$percent.'，得18分';
        }elseif($percent>=0.6){
            $data['score']=12;
            $desc[] = '重症医学科医师人数与重症医学科开放床位数比为'.$percent.'，得12分';
        }elseif($percent>=0.5){
            $data['score']=6;
            $desc[] = '重症医学科医师人数与重症医学科开放床位数比为'.$percent.'，得6分';
        }else{
            $desc[] = '重症医学科医师人数与重症医学科开放床位数比为0.5以下，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 重症医学科护床比
     * 重症医学科医师人数与重症医学科开放床位数比应为0.8:1以上。比例达标得24分，在达标的基础上比例每下降10%减6分。
     * 满分：24
     * @return array
     */
    function getPersonnelZzkHsbl()
    {
        $data['score'] = 0;
        $data['desc'] = '均不符合，不得分';
        $desc = [];
        $percent = (float)$this->project->getAvgDataByIndexCode('personnel_zzk_hsbl');
        if($percent>=3){
            $data['score']=24;
            $desc[] = '重症医学科护士人数与床位数之比为'.$percent.'，得24分';
        }elseif($percent>=2.9){
            $data['score']=18;
            $desc[] = '重症医学科护士人数与床位数之比为'.$percent.'，得18分';
        }elseif($percent>=2.8){
            $data['score']=12;
            $desc[] = '重症医学科护士人数与床位数之比为'.$percent.'，得12分';
        }elseif($percent>=2.7){
            $data['score']=6;
            $desc[] = '重症医学科护士人数与床位数之比为'.$percent.'，得6分';
        }else{
            $desc[] = '重症医学科护士人数与床位数之比为2.7以下，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 重症科主任要求
     * 三级医院重症医学科主任必须是副高职称以上，综合ICU科主任应持有重症医学医师执业证书。达标得24分，不达标得0分。
     * 满分：24
     * @return array
     */
    function getPersonnelZzkKszr()
    {
        $data['score'] = 0;
        $data['desc'] = '不达标得0分';
        $desc = [];
        $title = $this->project->getDataByIndexCode('personnel_zzk_kzr_zc')->getData();
        if(in_array($title,['副高','正高'])){
            $desc[] = '职称为副高职称以上，只达标一条，不得分';
        }
        $zs = $this->project->getDataByIndexCode('personnel_zzk_kzr_zgzs')->getData();
        if($zs=='执业医师资格证书'){
            $desc[] = '持有执业医师资格证书，只达标一条，不得分';
        }

        if(in_array($title,['副高','正高']) && $zs=='执业医师资格证书'){
            $data['score'] = 24;
            $desc = [];
            $desc[] = '职称与证书均达标，得24分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 重症护士长要求
     * ICU护士长应具有中级以上专业技术职称。在重症监护领域工作3年以上，具备一定管理设备设施能力，符合医院功能任务需要。有ICU专业护士资质证书或上级医院综合ICU进修学习半年以上。达标得24分，不达标得0分。
     * 满分：24
     * @return array
     */
    function getPersonnelZzkHsz()
    {
        $data['score'] = 0;
        $data['desc'] = '不达标得0分';
        $desc = [];
        $title = $this->project->getDataByIndexCode('personnel_zzk_hsz_zc')->getData();
        if(!in_array($title,['中级','副高','正高'])){
            $desc[] = '职称不达标，不得分';
        }
        $workYear = $this->project->getDataByIndexCode('personnel_zzk_hsz_gznx')->getData();
        if($workYear<3){
            $desc[] = '重症监护领域工作年限不达标，不得分';
        }
        $workMonth = $this->project->getDataByIndexCode('personnel_zzk_hsz_jxsc')->getData();
        $zs = $this->project->getDataByIndexCode('personnel_zzk_hsz_zgzs')->getData();
        if($workMonth<6 && $zs!='重症医学科专科护士证书'){
            $desc[] = '资质证书和ICU进修学习时长均不达标，不得分';
        }

        if(in_array($title,['中级','副高','正高']) && $workYear>=3 && ($zs=='重症医学科专科护士证书' || $workMonth>=6)){
            $data['score'] = 24;
            $desc = [];
            $desc[] = '达标得24分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 基本急救及床旁检测技术
     * 评估近3年，急诊科所有区域（包括院前急救、急诊内、外科、急诊抢救室、急诊病房与EICU）开展动静脉穿刺置管术150例、胸腔闭式引流术30例、腰椎穿刺引流术10例，腹腔穿刺引流术30例，清创缝合术600例、气管插管术300例、心肺复苏术100例、呼吸机（有创、无创）使用300例、POCT（5000例次），一项例数不够扣2分，不能开展扣4分，直至扣完。
     *满分：36
     */
    function getTechniqueJbjj()
    {
        $data['score'] = 36;
        $data['desc'] = '所有条件均符合，不扣分';
        $desc = [];
        if($this->project->getSumDataByIndexCode('technique_jbjj_1')==0){
            $data['score']-=4;
            $desc[] = '不能开展动静脉穿刺置管术，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_1')<150){
            $data['score']-=2;
            $desc[] = '开展动静脉穿刺置管术不足150例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_2')==0){
            $data['score']-=4;
            $desc[] = '不能开展胸腔闭式引流术，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_2')<30){
            $data['score']-=2;
            $desc[] = '开展胸腔闭式引流术不足30例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_3')==0){
            $data['score']-=4;
            $desc[] = '不能开展腰椎穿刺引流术，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_3')<10){
            $data['score']-=2;
            $desc[] = '开展腰椎穿刺引流术不足10例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_4')==0){
            $data['score']-=4;
            $desc[] = '不能开展腹腔穿刺引流术，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_4')<30){
            $data['score']-=2;
            $desc[] = '开展腹腔穿刺引流术不足30例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_5')==0){
            $data['score']-=4;
            $desc[] = '不能开展清创缝合术，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_5')<600){
            $data['score']-=2;
            $desc[] = '开展清创缝合术不足600例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_6')==0){
            $data['score']-=4;
            $desc[] = '不能开展气管插管术，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_6')<300){
            $data['score']-=2;
            $desc[] = '开展气管插管术不足300例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_7')==0){
            $data['score']-=4;
            $desc[] = '不能开展心肺复苏术，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_7')<100){
            $data['score']-=2;
            $desc[] = '开展心肺复苏术不足100例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_8')==0){
            $data['score']-=4;
            $desc[] = '不能使用呼吸机（有创、无创），扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_8')<300){
            $data['score']-=2;
            $desc[] = '使用呼吸机（有创、无创）不足300例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_jbjj_9')==0){
            $data['score']-=4;
            $desc[] = '不能开展POCT，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_jbjj_9')<5000){
            $data['score']-=2;
            $desc[] = '开展POCT不足5000例，扣2分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 危重病救治技术
     * 评估近3年，急诊科所有区域（包括院前急救、急诊内外科、急诊抢救室、急诊病房与EICU）开展有创血流动力学监测（Swan—Ganz导管或/和PICCO）技术12例、气管切开术60例、床旁超声150例、CRRT技术90例、亚低温治疗技术10例、纤维支气管镜吸痰灌洗技术90例，少一项扣6分，每项技术例数不足扣3分；ECMO 3例、主动脉内球囊反搏（IABP）3例、主动脉球囊阻断（或动脉介入手术）3例，少一项扣4分，每项技术例数不足扣2分。
     *满分：48
     */
    function getTechniqueWzjj()
    {
        $data['score'] = 48;
        $data['desc'] = '所有条件均符合，不扣分';
        $desc = [];
        if($this->project->getSumDataByIndexCode('technique_wzjj_1')==0){
            $data['score']-=6;
            $desc[] = '未开展有创血流动力学监测（Swan—Ganz导管或/和PICCO）技术，扣6分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_1')<12){
            $data['score']-=3;
            $desc[] = '开展有创血流动力学监测（Swan—Ganz导管或/和PICCO）技术不足12例，扣3分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_2')==0){
            $data['score']-=6;
            $desc[] = '未开展气管切开术，扣6分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_2')<60){
            $data['score']-=3;
            $desc[] = '开展胸腔闭式引流术不足60例，扣3分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_3')==0){
            $data['score']-=6;
            $desc[] = '未开展床旁超声，扣6分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_3')<150){
            $data['score']-=3;
            $desc[] = '开展床旁超声不足150例，扣3分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_4')==0){
            $data['score']-=6;
            $desc[] = '未开展CRRT技术，扣6分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_4')<90){
            $data['score']-=3;
            $desc[] = '开展CRRT技术不足90例，扣3分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_5')==0){
            $data['score']-=6;
            $desc[] = '未开展亚低温治疗技术，扣6分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_5')<10){
            $data['score']-=3;
            $desc[] = '开展亚低温治疗技术不足10例，扣3分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_6')==0){
            $data['score']-=6;
            $desc[] = '未开展纤维支气管镜吸痰灌洗技术，扣6分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_6')<90){
            $data['score']-=3;
            $desc[] = '开展纤维支气管镜吸痰灌洗技术不足90例，扣3分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_7')==0){
            $data['score']-=4;
            $desc[] = '未开展ECMO，扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_7')<3){
            $data['score']-=2;
            $desc[] = '开展ECMO不足3例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_8')==0){
            $data['score']-=4;
            $desc[] = '未开展主动脉内球囊反搏（IABP），扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_8')<3){
            $data['score']-=2;
            $desc[] = '开展主动脉内球囊反搏（IABP）不足3例，扣2分';
        }
        if($this->project->getSumDataByIndexCode('technique_wzjj_9')==0){
            $data['score']-=4;
            $desc[] = '未开展主动脉球囊阻断（或动脉介入手术），扣4分';
        }elseif($this->project->getSumDataByIndexCode('technique_wzjj_9')<3){
            $data['score']-=2;
            $desc[] = '开展主动脉球囊阻断（或动脉介入手术）不足3例，扣2分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 急诊信息系统建设
     * 使用电子化的院前急救、院内急诊、急救抢救室、EICU信息系统（8分）；
    院前急救、院内急诊+急诊抢救室、EICU与院内各信息系统互联互通（8分）；
    依托信息化手段开展相关数据和质量控制指标的采集、分析、上报工作（8分）
     *满分：24
     */
    function getPlatformXxxt()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $desc = [];
        if($this->project->getDataByIndexCode('platform_xxxt_1')=='是'){
            $data['score']+=8;
            $desc[] = '使用电子化的院前急救、院内急诊、急救抢救室、EICU信息系统，得8分';
        }
        if($this->project->getDataByIndexCode('platform_xxxt_2')=='是'){
            $data['score']+=8;
            $desc[] = '院前急救、院内急诊+急诊抢救室、EICU与院内各信息系统互联互通，得8分';
        }
        if($this->project->getDataByIndexCode('platform_xxxt_3')=='是'){
            $data['score']+=8;
            $desc[] = '依依托信息化手段开展相关数据和质量控制指标的采集、分析、上报工作，得8分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 年门诊人数
     * 按照申报专科排序分段，排名前1-3名，得40分；排名前4-6名，得20分；之后排名不得分。
     * @return array
     */
    function getScaleMzrsScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前6，不得分';
        $desc = [];

        $indexCode = 'scale_mzrs';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        if($rank<=3){
            $data['score'] = 40;
            $desc[] = '排名前1-3名（'.$rank.'/'.$count.'），得40分';
        }elseif($rank<=6){
            $data['score'] = 24;
            $desc[] = '排名排名前4-6名（'.$rank.'/'.$count.'），得20分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前6，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 年平均出院人数
     * 按照申报专科排序分段，排名前1-3名，得40分；排名前4-6名，得20分；之后排名不得分。
     * @return array
     */
    function getScaleCyrsScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前6，不得分';
        $desc = [];

        $indexCode = 'scale_cyrs';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        if($rank<=3){
            $data['score'] = 40;
            $desc[] = '排名前1-3名（'.$rank.'/'.$count.'），得40分';
        }elseif($rank<=6){
            $data['score'] = 24;
            $desc[] = '排名排名前4-6名（'.$rank.'/'.$count.'），得20分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前6，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 平均住院日
     * 按照申报专科排序分段，排名前1-3名，得120分；排名前4-6名，得60分；之后排名不得分。
     * @return array
     */
    function getEfficientZyrScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前6，不得分';
        $desc = [];

        $indexCode = 'efficient_zyr';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data < '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        if($rank<=3){
            $data['score'] = 120;
            $desc[] = '排名前1-3名（'.$rank.'/'.$count.'），得120分';
        }elseif($rank<=6){
            $data['score'] = 60;
            $desc[] = '排名排名前4-6名（'.$rank.'/'.$count.'），得60分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前6，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }
    /**
     * DRGs组数
     * 与全省该专科平均水平比较：≥30%，得30分；≥20%-29%，得20分；≥10%-19%，得10分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 30;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平30%，得30分';
        }elseif($percent>=20){
            $data['score'] = 20;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平20%-29%，得20分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = 'DRGs组数≥全省该专科平均水平10%-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'DRGs组数≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * DRGs组数（妇科）
     * 与全省该专科平均水平比较：≥30%，得15分；≥20%-29%，得10分；≥10%-19%，得5分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs_fk');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 15;
            $data['desc'] = 'DRGs组数（妇科）≥全省该专科平均水平30%，得15分';
        }elseif($percent>=20){
            $data['score'] = 10;
            $data['desc'] = 'DRGs组数（妇科）≥全省该专科平均水平20%-29%，得10分';
        }elseif($percent>=10){
            $data['score'] = 5;
            $data['desc'] = 'DRGs组数（妇科）≥全省该专科平均水平10%-19%，得5分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'DRGs组数（妇科）≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * DRGs组数（产科）
     * 与全省该专科平均水平比较：≥30%，得15分；≥20%-29%，得10分；≥10%-19%，得5分；其余情况不得分。
     * @return array
     */
    function getAbilityDrgsCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_drgs_ck');
        $avgData = $this->project->getAvgDataByIndexCode('ability_drgs_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 15;
            $data['desc'] = 'DRGs组数（产科）≥全省该专科平均水平30%，得15分';
        }elseif($percent>=20){
            $data['score'] = 10;
            $data['desc'] = 'DRGs组数（产科）≥全省该专科平均水平20%-29%，得10分';
        }elseif($percent>=10){
            $data['score'] = 5;
            $data['desc'] = 'DRGs组数（产科）≥全省该专科平均水平10%-19%，得5分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'DRGs组数（产科）≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * DRGs组数（EICU）
     * 不能提供EICU DRGs清单得0分，DRGs组数＜50得2分，DRGs组数50-79得3分，DRGs组数80-99得4分，DRGs组数≥100得5分
     * @return array
     */
    function getAbilityEicuDrgs()
    {
        $data['score'] = 0;
        $data['desc'] = '不能提供EICU DRGs清单得0分';
        $desc = [];
        if($this->project->getAvgDataByIndexCode('ability_eicu_drgs')>=100){
            $data['score']+=5;
            $desc[] = 'DRGs组数≥100，得5分';
        }elseif($this->project->getAvgDataByIndexCode('ability_eicu_drgs')>=80){
            $data['score']+=4;
            $desc[] = 'DRGs组数80-99，得4分';
        }elseif($this->project->getAvgDataByIndexCode('ability_eicu_drgs')>=50){
            $data['score']+=3;
            $desc[] = 'DRGs组数50-79，得3分';
        }elseif($this->project->getAvgDataByIndexCode('ability_eicu_drgs')<50){
            $data['score']+=2;
            $desc[] = 'DRGs组数＜50得2分';
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 病例组合指数（CMI）
     * 与全省该专科平均水平比较：≥30%，得30分；≥20%-29%，得20分；≥10%-19%，得10分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 30;
            $data['desc'] = 'CMI≥全省该专科平均水平30%，得30分';
        }elseif($percent>=20){
            $data['score'] = 20;
            $data['desc'] = 'CMI≥全省该专科平均水平20%-29%，得20分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = 'CMI≥全省该专科平均水平10%-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'CMI≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）（妇科）
     * 与全省该专科平均水平比较：≥30%，得15分；≥20%-29%，得10分；≥10%-19%，得5分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi_fk');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 15;
            $data['desc'] = 'CMI（妇科）≥全省该专科平均水平30%，得15分';
        }elseif($percent>=20){
            $data['score'] = 10;
            $data['desc'] = 'CMI（妇科）≥全省该专科平均水平20%-29%，得10分';
        }elseif($percent>=10){
            $data['score'] = 5;
            $data['desc'] = 'CMI（妇科）≥全省该专科平均水平10%-19%，得5分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'CMI（妇科）≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）（产科）
     * 与全省该专科平均水平比较：≥30%，得15分；≥20%-29%，得10分；≥10%-19%，得5分；其余情况不得分。
     * @return array
     */
    function getAbilityCmiCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_cmi_ck');
        $avgData = $this->project->getAvgDataByIndexCode('ability_cmi_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 15;
            $data['desc'] = 'CMI（产科）≥全省该专科平均水平30%，得15分';
        }elseif($percent>=20){
            $data['score'] = 10;
            $data['desc'] = 'CMI（产科）≥全省该专科平均水平20%-29%，得10分';
        }elseif($percent>=10){
            $data['score'] = 5;
            $data['desc'] = 'CMI（产科）≥全省该专科平均水平10%-19%，得5分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'CMI（产科）≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 病例组合指数（CMI）（EICU）
     * 不能提供EICU CMI清单得0分，CMI＜2得2分，CMI2-2.49得3分，CMI2.5-2.99得4分，CMI≥3得5分
     * @return array
     */
    function getAbilityEicuCmiScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不能提供EICU CMI清单，得0分';
        $desc = [];
        $avgData = $this->project->getAvgDataByIndexCode('ability_eicu_cmi');
        if($avgData>=3){
            $data['score']+=5;
            $desc[] = 'CMI≥3，得5分';
        }elseif($avgData>=2.5){
            $data['score']+=4;
            $desc[] = 'CMI2.5-2.99，得4分';
        }elseif($avgData>=2){
            $data['score']+=3;
            $desc[] = 'CMI2-2.49，得3分';
        }elseif($avgData<2){
            $data['score']+=2;
            $desc[] = 'CMI＜2得2分';
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * ICU患者收治率
     * 该专科全省排名：ICU患者收治率排名前25%（含25%），得40分；排名25%-50%（含50%），得30分；排名50%-75%（含75%）,得20分；其余得10分。
     * @return array
     */
    function getAbilityIcuScore()
    {
        $data['score'] = 10;
        $data['desc'] = 'ICU患者收治率排名未进前75%，得10分';
        $db = sf::getLib('db');
        $indexCode = 'ability_icu';
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        $rankStr = $rank.'/'.$count;
//        $html = Button::setUrl(site_url('index/rank/icu/id/'.$this->project->getProjectId()))->window($rankStr);
        if($rankPercent<=25){
            $data['score']=40;
            $desc[] = 'ICU患者收治率排名前25%（'.$rankStr.'），得40分';
        }elseif($rankPercent<=50){
            $data['score']=30;
            $desc[] = 'ICU患者收治率排名25%-50%（'.$rankStr.'），得30分';
        }elseif($rankPercent<=75){
            $data['score']=20;
            $desc[] = 'ICU患者收治率排名50%-75%（'.$rankStr.'），得20分';
        }else{
            $data['score']=0;
            $desc[] = 'ICU患者收治率排名靠后（'.$rankStr.'），不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 血液透析患者管理
     *  @return array
     */
    function getAbilityXytxScore()
    {
        //血液透析患者管理(检测率)
        $item['jcl'] = $this->getAbilityXytxJclScore();
        //血液透析患者管理(达标率)
        $item['dbl'] = $this->getAbilityXytxDblScore();
        $data['score'] = $item['jcl']['score'] + $item['dbl']['score'];
        if($data['score']>60){
            $data['score'] = 60;
        }
        $desc[] = $item['jcl']['desc'];
        $desc[] = $item['dbl']['desc'];
        $data['desc'] = implode(';',$desc);
        return $data;
    }

    /**
     * 血液透析患者管理(检测率)
     * 在申报单位中排名：
     * 1.血红蛋白检测率、尿素清除率(URR)检测率、尿素清除指数(Kt/V)检测率、血磷检测率、血钙检测率、PTH检测率，
     * 任意一项指标排名前25%（含25%），得7分；排名25%-50%（含50%），得4分；排名50%-75%（含75%）,得2分；其余得0分。本项累计得分不超过42分。
     * @return array
     */
    function getAbilityXytxJclScore()
    {
        $data['score'] = 0;
        $data['desc'] = '所有指标排名未进前75%，不得分';
        $db = sf::getLib('db');
        $codes = ['xhdb'=>'血红蛋白检测率','urr'=>'尿素清除率(URR)检测率','ktv'=>'尿素清除指数(Kt/V)检测率','xl'=>'血磷检测率','xg'=>'血钙检测率','pth'=>'PTH检测率'];
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        foreach ($codes as $code=>$text){
            $mark = 'ability_xytx_jcl_'.$code;
            $percent = $this->project->getDataByIndexCode($mark,$this->project->getEndYear())->getData();

            if((string)$percent=='无'){
                $desc[] = $text.'无数据，不得分';
                continue;
            }

            $sql = "SELECT count(*) c FROM `stage_datas` where index_code = '{$mark}' and index_year = '".$this->project->getEndYear()."' and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) ORDER BY data desc";
            $count = $db->result_first($sql);        //血红蛋白检测率总数
            if($count==0) continue;
            $sql = "SELECT count(*) c FROM `stage_datas` where index_code = '{$mark}' and index_year = '".$this->project->getEndYear()."' and data > '{$percent}' and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) ORDER BY data desc";
            $rank = (int)$db->result_first($sql)+1;        //排名
            $rankPercent = sprintf('%.2f',($rank/$count*100));
            if($rankPercent<=25){
                $data['score']+=7;
                $desc[] = $text.'排名前25%（'.$rank.'/'.$count.'），得7分';
                continue;
            }elseif($rankPercent<=50){
                $data['score']+=4;
                $desc[] = $text.'排名25%-50%（'.$rank.'/'.$count.'），得4分';
                continue;
            }elseif($rankPercent<=75){
                $data['score']+=2;
                $desc[] = $text.'排名50%-75%（'.$rank.'/'.$count.'），得2分';
            }
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 血液透析患者管理(达标率)
     * 在申报单位中排名：
     * 血红蛋白达标率、尿素清除率(URR)达标率、尿素清除指数(Kt/V)达标率、血磷达标率、血钙达标率、PTH达标率，
     * 任意一项指标排名前25%（含25%），得3分；排名25%-50%（含50%），得2分；排名50%-75%（含75%）,得1分；其余得0分。
     * 本项累计得分不超过18分。
     * @return array
     */
    function getAbilityXytxDblScore()
    {
        $data['score'] = 0;
        $data['desc'] = '所有指标排名未进前75%，不得分';
        $db = sf::getLib('db');
        $codes = ['xhdb'=>'血红蛋白达标率','urr'=>'尿素清除率(URR)达标率','ktv'=>'尿素清除指数(Kt/V)达标率','xl'=>'血磷达标率','xg'=>'血钙达标率','pth'=>'PTH达标率'];
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        foreach ($codes as $code=>$text){
            $mark = 'ability_xytx_dbl_'.$code;
            $percent = $this->project->getDataByIndexCode($mark,$this->project->getEndYear())->getData();
            if((string)$percent=='无'){
                $desc[] = $text.'无数据，不得分';
                continue;
            }
            $sql = "SELECT count(*) c FROM `stage_datas` where index_code = '{$mark}' and index_year = '".$this->project->getEndYear()."' and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) ORDER BY data desc";
            $count = $db->result_first($sql);        //达标率总数
            if($count==0) continue;
            $sql = "SELECT count(*) c FROM `stage_datas` where index_code = '{$mark}' and index_year = '".$this->project->getEndYear()."' and data > '{$percent}' and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) ORDER BY data desc";
            $rank = (int)$db->result_first($sql)+1;        //排名
            $rankPercent = sprintf('%.2f',($rank/$count*100));
            if($rankPercent<=25){
                $data['score']+=3;
                $desc[] = $text.'排名前25%（'.$rank.'/'.$count.'），得3分';
                continue;
            }elseif($rankPercent<=50){
                $data['score']+=2;
                $desc[] = $text.'排名25%-50%（'.$rank.'/'.$count.'），得2分';
                continue;
            }elseif($rankPercent<=75){
                $data['score']+=1;
                $desc[] = $text.'排名50%-75%（'.$rank.'/'.$count.'），得1分';
            }
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 特级护理占比
     * 该专科申报单位排名：特级护理占比排名前25%（含25%），得60分；排名25%-50%（含50%），得40分；排名50%-75%（含75%）,得20分；其余得0分。。
     * @return array
     */
    function getAbilityTjhlScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        $indexCode = 'ability_tjhl';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 60;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得60分';
        }elseif($rankPercent<=50){
            $data['score'] = 40;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得40分';
        }elseif($rankPercent<=75){
            $data['score'] = 20;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得20分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 本专科RW≥2的病例占比（高优指标）
     * 与全省该专科平均水平比较：≥30%，得24分；≥20%-29%，得16分；≥10%-19%，得8分；其余情况不得分。
     * @return array
     */
    function getAbilityRwScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('ability_rw');
        $avgData = $this->project->getAvgDataByIndexCode('ability_rw');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 24;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平30%，得24分';
        }elseif($percent>=20){
            $data['score'] = 16;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平20%-29%，得16分';
        }elseif($percent>=10){
            $data['score'] = 8;
            $data['desc'] = 'RW≥2的病例占比≥全省该专科平均水平10%-19%，得8分';
        }elseif($percent>=0){
            $data['score'] = 0;
            $data['desc'] = 'RW≥2的病例占比≤全省该专科平均水平10%，不得分';
        }
        return $data;
    }

    /**
     * 急性生理与慢性健康评分（APACHEⅡ评分）≥15分患者收治率（入ICU24小时内）
     * 该专科全省排名：APACHEⅡ评分≥15分患者收治率排名前25%（含25%），得40分；排名25%-50%（含50%），得30分；排名50%-75%（含75%）,得20分；其余得10分。
     * @return array
     */
    function getSafetyZzkApache()
    {
        $data['score'] = 10;
        $data['desc'] = '收治率排名未进入前75%，得10分';
        $db = sf::getLib('db');
        $mark = 'safety_zzk_apache';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($mark);
        $subjectCode = $this->project->getSubjectCode();
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$mark}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";



        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$mark}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        if($count>0){

            $rankPercent = sprintf('%.2f',($rank/$count*100));
            if($rankPercent<=25){
                $data['score']=40;
                $desc[] = '收治率排名前25%（'.$rank.'/'.$count.'），得40分';
            }elseif($rankPercent<=50){
                $data['score']=30;
                $desc[] = '收治率排名25-50%（'.$rank.'/'.$count.'），得30分';
            }elseif($rankPercent<=75){
                $data['score']=20;
                $desc[] = '收治率排名50%-75%（'.$rank.'/'.$count.'），得20分';
            }else{
                $data['score']=10;
                $desc[] = '收治率排名未进入前75%（'.$rank.'/'.$count.'），得10分';
            }
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 转出ICU后48h内重返率
     * 该专科全省排名：转出ICU后48h内重返ICU率排名后75%（含75%），得40分；排名50%-75%（含50%），得30分；排名25%-50%（含25%）,得20分；其余得10分。
     * @return array
     */
    function getSafetyZzkIcucfl()
    {
        $data['score'] = 10;
        $data['desc'] = '转出ICU后48h内重返率排名进入前25%，得10分';
        $db = sf::getLib('db');
        $mark = 'safety_zzk_icucfl';

        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($mark);
        $subjectCode = $this->project->getSubjectCode();
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$mark}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$mark}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名

        if($count>0){

            $rankPercent = sprintf('%.2f',($rank/$count*100));
            if($rankPercent>=75){
                $data['score']=40;
                $desc[] = '转出ICU后48h内重返率排名后75%（'.$rank.'/'.$count.'），得40分';
            }elseif($rankPercent>=50){
                $data['score']=30;
                $desc[] = '转出ICU后48h内重返率排名50%-75%（'.$rank.'/'.$count.'），得30分';
            }elseif($rankPercent>=25){
                $data['score']=20;
                $desc[] = '转出ICU后48h内重返率排名25%-50%（'.$rank.'/'.$count.'），得20分';
            }else{
                $data['score']=10;
                $desc[] = '转出ICU后48h内重返率排名前25%（'.$rank.'/'.$count.'），得10分';
            }
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }


    /**
     * 四级手术占比
     * 40%及以上得30分。小于40%的按照降序赋分：专科四级手术占比/40%*30。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=40){
            $data['score'] = 30;
            $data['desc'] = '四级手术占比≥40%，得30分';
        }else{
            $score = $percent/40*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比小于40%的按照降序赋分：{$percent}/40*30";
        }
        return $data;
    }


    /**
     * 四级手术占比（妇科）
     * 40%及以上得15分。小于40%的按照降序赋分：专科四级手术占比/40%*15。
     * @return array
     */
    function getAbilitySjssFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=40){
            $data['score'] = 15;
            $data['desc'] = '四级手术占比（妇科）≥40%，得15分';
        }else{
            $score = $percent/40*15;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比（妇科）小于40%的按照降序赋分：{$percent}/40*15";
        }
        return $data;
    }
    /**
     * 四级手术占比（产科）
     * 40%及以上得15分。小于40%的按照降序赋分：专科四级手术占比/40%*15。
     * @return array
     */
    function getAbilitySjssCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=40){
            $data['score'] = 15;
            $data['desc'] = '四级手术占比（产科）≥40%，得15分';
        }else{
            $score = $percent/40*15;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比（产科）小于40%的按照降序赋分：{$percent}/40*15";
        }
        return $data;
    }

    /**
     * 手术室外麻醉占比
     * 该专科申报单位排名：四级手术占比排名前25%（含25%），得36分；排名25%-50%（含50%），得24分；排名50%-75%（含75%）,得12分；其余得0分。。
     * @return array
     */
    function getAbilitySwmzScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        $indexCode = 'ability_swmz';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 36;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得36分';
        }elseif($rankPercent<=50){
            $data['score'] = 24;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得24分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 麻醉医师人均年麻醉例次数
     * 500-800例次，得满分；>800或<500，不得分。
     * @return array
     */
    function getAbilityRjmzScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_rjmz');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=500 && $percent<800){
            $data['score'] = 48;
            $data['desc'] = '500-800例次，得48分';
        }elseif($percent>800){
            $data['score'] = 0;
            $data['desc'] = "大于800例次，不得分";
        }elseif($percent<500){
            $data['score'] = 0;
            $data['desc'] = "小于500例次，不得分";
        }
        return $data;
    }

    /**
     * 阴道分娩椎管内麻醉使用率（无产科医院不考核）
     * 该专科申报单位排名：阴道分娩椎管内麻醉使用率排名前25%（含25%），得16分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得4分；其余不得分。
     * @return array
     */
    function getEfficientFmmzScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        $indexCode = 'efficient_fmmz';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 16;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得16分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 4;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得4分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 入室后手术麻醉取消率
     * 该专科申报单位排名：入室后手术麻醉取消率排名前25%（含25%），得4分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得12分；其余得16分。
     * @return array
     */
    function getEfficientMzqxScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 16;
        $data['desc'] = '排名未进前75%，得16分';
        $desc = [];

        $indexCode = 'efficient_mzqx';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 4;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }else{
            $data['score'] = 16;
            $desc[] = '排名未进前75%，得16分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 麻醉开始后手术取消率
     * 该专科申报单位排名：入室后手术麻醉取消率排名前25%（含25%），得4分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得12分；其余得16分。
     * @return array
     */
    function getEfficientSsqxScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 16;
        $data['desc'] = '排名未进前75%，得16分';
        $desc = [];

        $indexCode = 'efficient_ssqx';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 4;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }else{
            $data['score'] = 16;
            $desc[] = '排名未进前75%，得16分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 术后镇痛满意率
     * 该专科申报单位排名：术后镇痛满意率排名前25%（含25%），得16分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得4分；其余不得分。
     * @return array
     */
    function getEfficientZtmyScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        $indexCode = 'efficient_ztmy';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 16;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得16分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 4;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得4分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 术中自体血输注率
     * 该专科申报单位排名：术中自体血输注率排名前25%（含25%），得16分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得4分；其余不得分。
     * @return array
     */
    function getSafetyMzkMz1Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        $indexCode = 'safety_mzk_mz1';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 16;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得16分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 4;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得4分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 术中心脏骤停率
     * 该专科申报单位排名：术中心脏骤停率排名前25%（含25%），得4分；排名25%-50%（含50%），得8分；排名50%-75%（含75%）,得12分；其余得16分。
     * @return array
     */
    function getSafetyMzkMz2Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 16;
        $data['desc'] = '排名未进前75%，得16分';
        $desc = [];

        $indexCode = 'safety_mzk_mz2';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 4;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }else{
            $data['score'] = 16;
            $desc[] = '排名未进前75%，得16分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 麻醉后24小时内患者死亡率
     * 该专科申报单位排名：麻醉后24小时内患者死亡率排名前25%（含25%），得2分；排名25%-50%（含50%），得4分；排名50%-75%（含75%）,得6分；其余得8分。
     * @return array
     */
    function getSafetyMzkMz3Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 8;
        $data['desc'] = '排名未进前75%，得8分';
        $desc = [];

        $indexCode = 'safety_mzk_mz3';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 2;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得2分';
        }elseif($rankPercent<=50){
            $data['score'] = 4;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 8;
            $desc[] = '排名未进前75%，得8分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 区域阻滞麻醉后严重神经并发症发生率
     * 该专科申报单位排名：区域阻滞麻醉后严重神经并发症发生率排名前25%（含25%），得2分；排名25%-50%（含50%），得4分；排名50%-75%（含75%）,得6分；其余得8分。
     * @return array
     */
    function getSafetyMzkMz4Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 8;
        $data['desc'] = '排名未进前75%，得8分';
        $desc = [];

        $indexCode = 'safety_mzk_mz4';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 2;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得2分';
        }elseif($rankPercent<=50){
            $data['score'] = 4;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 8;
            $desc[] = '排名未进前75%，得8分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 择期手术麻醉前访视率
     * 该专科申报单位排名：择期手术麻醉前访视率排名前25%（含25%），得8分；排名25%-50%（含50%），得4分；排名50%-75%（含75%）,得2分；其余不得分。
     * @return array
     */
    function getSafetyMzkMz5Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        $indexCode = 'safety_mzk_mz5';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 8;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得8分';
        }elseif($rankPercent<=50){
            $data['score'] = 4;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=75){
            $data['score'] = 2;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得2分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 麻醉期间严重反流误吸发生率
     * 该专科申报单位排名：麻醉期间严重反流误吸发生率排名前25%（含25%），得2分；排名25%-50%（含50%），得4分；排名50%-75%（含75%）,得6分；其余得8分。
     * @return array
     */
    function getSafetyMzkMz6Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 8;
        $data['desc'] = '排名未进前75%，得8分';
        $desc = [];

        $indexCode = 'safety_mzk_mz6';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 2;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得2分';
        }elseif($rankPercent<=50){
            $data['score'] = 4;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 8;
            $desc[] = '排名未进前75%，得8分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 计划外建立人工气道发生率
     * 该专科申报单位排名：计划外建立人工气道发生率排名前25%（含25%），得2分；排名25%-50%（含50%），得4分；排名50%-75%（含75%）,得6分；其余得8分。
     * @return array
     */
    function getSafetyMzkMz7Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 8;
        $data['desc'] = '排名未进前75%，得8分';
        $desc = [];

        $indexCode = 'safety_mzk_mz7';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 2;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得2分';
        }elseif($rankPercent<=50){
            $data['score'] = 4;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 8;
            $desc[] = '排名未进前75%，得8分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 非计划二次气管插管率
     * 该专科申报单位排名：非计划二次气管插管率排名前25%（含25%），得2分；排名25%-50%（含50%），得4分；排名50%-75%（含75%）,得6分；其余得8分。
     * @return array
     */
    function getSafetyMzkMz8Score()
    {
        $db = sf::getLib('db');
        $data['score'] = 8;
        $data['desc'] = '排名未进前75%，得8分';
        $desc = [];

        $indexCode = 'safety_mzk_mz8';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['score'] = 0;
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 2;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得2分';
        }elseif($rankPercent<=50){
            $data['score'] = 4;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得4分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 8;
            $desc[] = '排名未进前75%，得8分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 四级手术数量
     * 1.支气管镜四级手术总例数，在该专科申报单位排名前25%（含25%），得24分；排名25%-50%（含50%），得16分；排名50%-75%（含75%）,得8分；其余得0分。
     * 2.血管介入四级手术总例数，在该专科申报单位排名前25%（含25%），得24分；排名25%-50%（含50%），得16分；排名50%-75%（含75%）,得8分；其余得0分。
     * @return array
     */
    function getAbilitySjslScore()
    {
        $db = sf::getLib('db');
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];

        //支气管镜四级手术总例数
        $indexCode = 'ability_sjsl_zqgj';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);

        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 24;
            $desc[] = '支气管镜四级手术总例数排名前25%（'.$rank.'/'.$count.'），得24分';
        }elseif($rankPercent<=50){
            $data['score'] = 16;
            $desc[] = '支气管镜四级手术总例数排名26%-50%（'.$rank.'/'.$count.'），得16分';
        }elseif($rankPercent<=75){
            $data['score'] = 8;
            $desc[] = '支气管镜四级手术总例数排名51%-75%（'.$rank.'/'.$count.'），得8分';
        }else{
            $data['score'] = 0;
            $desc[] = '支气管镜四级手术总例数排名未进前75%，不得分';
        }

        //血管介入四级手术总例数
        $indexCode = 'ability_sjsl_xgjr';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 24;
            $desc[]= '血管介入四级手术总例数排名前25%（'.$rank.'/'.$count.'），得24分';
        }elseif($rankPercent<=50){
            $data['score'] = 16;
            $desc[] = '血管介入四级手术总例数排名26%-50%（'.$rank.'/'.$count.'），得16分';
        }elseif($rankPercent<=75){
            $data['score'] = 8;
            $desc[] = '血管介入四级手术总例数排名51%-75%（'.$rank.'/'.$count.'），得8分';
        }else{
            $data['score'] = 0;
            $desc[] = '血管介入四级手术总例数排名未进前75%，不得分';
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 微创手术占比
     * 25%及以上得30分。小于25%的按照降序赋分：专科四级手术占比/25%*30。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=25){
            $data['score'] = 30;
            $data['desc'] = '微创手术占比≥25%，得30分';
        }else{
            $score = $percent/25*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于25%的按照降序赋分：{$percent}/25*30";
        }
        return $data;
    }

    /**
     * 微创手术占比（妇科）
     * 25% 及以上得15分。小于25%的按照降序赋分：专科微创手术占比/25%*15。
     * @return array
     */
    function getAbilityWcssFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=25){
            $data['score'] = 15;
            $data['desc'] = '微创手术占比（妇科）≥25%，得15分';
        }else{
            $score = $percent/25*15;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比（妇科）小于25%的按照降序赋分：{$percent}/25*15";
        }
        return $data;
    }

    /**
     * 微创手术占比（产科）
     * 25% 及以上得15分。小于25%的按照降序赋分：专科微创手术占比/25%*15。
     * @return array
     */
    function getAbilityWcssCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=25){
            $data['score'] = 15;
            $data['desc'] = '微创手术占比（产科）≥25%，得15分';
        }else{
            $score = $percent/25*15;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比（产科）小于25%的按照降序赋分：{$percent}/25*15";
        }
        return $data;
    }

    /**
     * 四级内镜手术占比
     * 该专科申报单位排名：四级内镜手术占比排名前30%，得36分；排名31%-50%，得24分；排名51%-75%,得12分；其余得0分。
     * @return array
     */
    function getAbilityNjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';

        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('specialty');
        $indexCodes = [];
        $indexCodesStr = [];
        foreach ($configs['njss'] as $k=>$v){
            foreach ($configs[$k] as $_k=>$_v){
                $indexCodes[] = 'ability_njss_'.$k.'_'.$_k;
                $indexCodesStr[] = "'".'ability_njss_'.$k.'_'.$_k."'";
            }
        }
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $db = sf::getLib('db');

        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";

        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=30){
            $data['score'] = 36;
            $data['desc'] = '排名前30%（'.$rank.'/'.$count.'），得36分';
        }elseif($rankPercent<=50){
            $data['score'] = 24;
            $data['desc'] = '排名31%-50%（'.$rank.'/'.$count.'），得24分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $data['desc'] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }
        return $data;
    }

    /**
     * 危重症病种比例
     * 该专科申报单位排名：危重症比例值排名前25%（含25%），得24分；排名25%-50%（含50%），得18分；排名50%-75%（含75%）,得12分；其余得0分。
     * @return array
     */
    function getAbilityWzbzScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';

        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('ability');
        $indexCodes = [];
        $indexCodesStr = [];
        foreach ($configs['wzbz'] as $k=>$v){
            $indexCodes[] = 'ability_wzbz_'.$k;
            $indexCodesStr[] = "'".'ability_wzbz_'.$k."'";
        }
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }

        $db = sf::getLib('db');

        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数
        if($count==0){
            $data['score'] = 0;
            $data['desc'] = '系统出错：分母为0';
            return $data;
        }

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));

        if($rankPercent<=25){
            $data['score'] = 36;
            $data['desc'] = '排名前25%（'.$rank.'/'.$count.'），得24分';
        }elseif($rankPercent<=50){
            $data['score'] = 24;
            $data['desc'] = '排名26%-50%（'.$rank.'/'.$count.'），得18分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $data['desc'] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }
        return $data;
    }


    /**
     * 低龄儿手术占比
     * 该专科申报单位排名：
     * 新生儿手术占比排名前25%（含25%），得12分；排名25%-50%（含50%），得9分；排名50%-75%（含75%）,得6分；其余得3分。
     * 婴儿手术占比排名前25%（含25%），得12分；排名25%-50%（含50%），得9分；排名50%-75%（含75%）,得6分；其余得3分。
     * @return array
     */
    function getAbilityXessScore()
    {
        //新生儿手术占比
        $item['xsr'] = $this->getAbilityXessXsrScore();
        //婴儿手术占比
        $item['yr'] = $this->getAbilityXessYrScore();
        $data['score'] = $item['xsr']['score'] + $item['yr']['score'];
        if($data['score']>24){
            $data['score'] = 24;
        }
        $desc[] = $item['xsr']['desc'];
        $desc[] = $item['yr']['desc'];
        $data['desc'] = implode(';',$desc);
        return $data;
    }

    /**
     * 新生儿手术占比排名前25%（含25%），得12分；排名25%-50%（含50%），得9分；排名50%-75%（含75%）,得6分；其余得3分。
     * @return array
     */
    function getAbilityXessXsrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];
        $indexCode = 'ability_xsss';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();
        $db = sf::getLib('db');
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 12;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得12分';
        }elseif($rankPercent<=50){
            $data['score'] = 9;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得9分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 婴儿手术占比排名前25%（含25%），得12分；排名25%-50%（含50%），得9分；排名50%-75%（含75%）,得6分；其余得3分。
     * @return array
     */
    function getAbilityXessYrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';
        $desc = [];
        $indexCode = 'ability_yess';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $subjectCode = $this->project->getSubjectCode();
        $db = sf::getLib('db');
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=25){
            $data['score'] = 12;
            $desc[] = '排名前25%（'.$rank.'/'.$count.'），得12分';
        }elseif($rankPercent<=50){
            $data['score'] = 9;
            $desc[] = '排名26%-50%（'.$rank.'/'.$count.'），得9分';
        }elseif($rankPercent<=75){
            $data['score'] = 6;
            $desc[] = '排名51%-75%（'.$rank.'/'.$count.'），得6分';
        }else{
            $data['score'] = 0;
            $desc[] = '排名未进前75%，不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 四级消化介入(血管介入）手术占比
     * 该专科申报单位排名：四级消化介入(血管介入）手术占比排名前30%，得36分；排名31%-50%，得24分；排名51%-75%,得12分；其余得0分。
     * @return array
     */
    function getAbilityJrssXhkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前75%，不得分';

        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('specialty');
        $indexCodes = [];
        $indexCodesStr = [];
        foreach ($configs['jrss_xhk'] as $k=>$v){
            foreach ($configs[$k] as $_k=>$_v){
                $indexCodes[] = 'ability_jrss_'.$k.'_'.$_k;
                $indexCodesStr[] = "'".'ability_jrss_'.$k.'_'.$_k."'";
            }
        }
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $db = sf::getLib('db');

        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";


        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        if($rankPercent<=30){
            $data['score'] = 36;
            $data['desc'] = '排名前30%（'.$rank.'/'.$count.'），得36分';
        }elseif($rankPercent<=50){
            $data['score'] = 24;
            $data['desc'] = '排名31%-50%（'.$rank.'/'.$count.'），得24分';
        }elseif($rankPercent<=75){
            $data['score'] = 12;
            $data['desc'] = '排名51%-75%（'.$rank.'/'.$count.'），得12分';
        }
        return $data;
    }

    /**
     * 介入手术占比
     * 1.该专科申报单位排名，血管造影手术占比排名前30%，得24分，排名31%-50%,得18分，排名51%-75%，得12分，其余不得分。
     * 2.该专科申报单位排名，支架置入术占比排名前30%，得12分，排名31%-50%，得8分，其余不得分。
     * 满分：36
     * @return array
     */
    function getAbilityJrssSnkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前指定范围，不得分';
        $desc = [];

        //脑血管造影手术占比
        $indexCode = 'ability_jrss_nxgzy';
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $db = sf::getLib('db');

        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";


        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";


        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        $rankStr = $rank.'/'.$count;
//        $html = Button::setUrl(site_url('index/rank/ranking/index_code/specialty_fwnl_jrss_nxgzy/id/'.$this->project->getProjectId()))->window($rankStr);

        if($rankPercent<=30){
            $data['score'] += 24;
            $desc[] = '血管造影手术占比排名前30%（'.$rankStr.'），得24分';
        }elseif($rankPercent<=50){
            $data['score'] += 18;
            $desc[] = '血管造影手术占比排名31%-50%（'.$rankStr.'），得18分';
        }elseif($rankPercent<=75){
            $data['score'] += 12;
            $desc[] = '血管造影手术占比排名51%-75%（'.$rankStr.'），得12分';
        }else{
            $data['score'] += 0;
            $desc[] = '血管造影手术占比排名靠后（'.$rankStr.'），不得分';
        }

        //支架置入术占比
        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('ability');
        $indexCodes = [];
        $indexCodesStr = [];
        foreach ($configs['jrss_snk'] as $k=>$v){
            foreach ($configs[$k] as $_k=>$_v){
                $indexCodes[] = 'ability_jrss_snk_'.$k.'_'.$_k;
                $indexCodesStr[] = "'".'ability_jrss_snk_'.$k.'_'.$_k."'";
            }
        }

        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);

        $db = sf::getLib('db');
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";

        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));

        $rankStr = $rank.'/'.$count;
//        $html = Button::setUrl(site_url('index/rank/zjzr/id/'.$this->project->getProjectId()))->window($rankStr);

        if($rankPercent<=30){
            $data['score'] += 12;
            $desc[] = '支架置入术占比排名前30%（'.$rankStr.'），得12分';
        }elseif($rankPercent<=50){
            $data['score'] = 8;
            $desc[] = '支架置入术占比排名31%-50%（'.$rankStr.'），得8分';
        }else{
            $data['score'] = 0;
            $desc[] = '支架置入术占比排名靠后（'.$rankStr.'），不得分';
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 介入手术占比
     * 该专科申报单位排名：介入手术占比排名前25%（含25%），得10分；排名25%-50%（含50%），得6分；排名50%-75%（含75%）,得3分；其余不得分。。
     * 满分：10
     * @return array
     */
    function getAbilityJrssXxgScore()
    {
        $data['score'] = 0;
        $data['desc'] = '排名未进前指定范围，不得分';
        $desc = [];
        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('ability');
        $indexCodes = [];
        $indexCodesStr = [];
        foreach ($configs['jrss_xxg'] as $k=>$v){
            $indexCodes[] = 'ability_jrss_xxg_'.$k;
            $indexCodesStr[] = "'".'ability_jrss_xxg_'.$k."'";
        }

        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $db = sf::getLib('db');
        $subjectCode = $this->project->getSubjectCode();

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";

        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code IN (".implode(',',$indexCodesStr).") and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";

        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));

        $rankStr = $rank.'/'.$count;

        if($rankPercent<=25){
            $data['score'] += 10;
            $desc[] = '介入手术占比排名前25%（'.$rankStr.'），得10分';
        }elseif($rankPercent<=50){
            $data['score'] = 6;
            $desc[] = '介入手术占比排名25%-50%（'.$rankStr.'），得6分';
        }elseif($rankPercent<=75){
            $data['score'] = 3;
            $desc[] = '介入手术占比排名50%-75%（'.$rankStr.'），得3分';
        }else{
            $data['score'] = 0;
            $desc[] = '介入手术占比排名靠后（'.$rankStr.'），不得分';
        }
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 平均年出院人数
     * 平均年出院人数≥4000人次得20分，3000-3999人次得15分，2000-2999人次得10分，低于2000人次不得分。
     * @return array
     */
    function getAbilityCyrsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '平均年出院人数低于2000人次不得分';
        $avgData = $this->project->getAvgDataByIndexCode('ability_cyrs');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($avgData>=4000){
            $data['score'] = 20;
            $data['desc'] = '平均年出院人数≥4000人次，得20分';
        }elseif($avgData>=3000){
            $data['score'] = 15;
            $data['desc'] = '平均年出院人数3000-3999人次，得15分';
        }elseif($avgData>=2000){
            $data['score'] = 10;
            $data['desc'] = '平均年出院人数2000-2999人次，得10分';
        }
        return $data;
    }

    /**
     * 费用消耗指数
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得40分；低于平均水平20-29%，得30分；低于平均水平10-19%，得20分；低于平均水平0-9%，得10分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientFyxhScore()
    {
        $data['score'] = 0;
        $data['desc'] = '费用消耗指数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_fyxh');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_fyxh');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 40;
            $data['desc'] = '全省该专科平均水平为0，该专科费用消耗指数也为0，得40分';
            return $data;
        }
        if($avgData>$provinceAvgData || $provinceAvgData==0){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 40;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平30%以上，得40分';
        }elseif($percent>=20){
            $data['score'] = 30;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平20-29%，得30分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平10-19%，得20分';
        }elseif($percent>=0){
            $data['score'] = 10;
            $data['desc'] = '费用消耗指数低于全省该专科平均水平0-9%，得10分';
        }
        return $data;
    }

    /**
     * 费用消耗指数（妇科）
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得20分；低于平均水平20-29%，得15分；低于平均水平10-19%，得10分；低于平均水平0-9%，得5分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientFyxhFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '费用消耗指数（妇科）高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_fyxh_fk');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_fyxh_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 20;
            $data['desc'] = '全省该专科平均水平为0，该专科费用消耗指数（妇科）也为0，得20分';
            return $data;
        }
        if($avgData>$provinceAvgData || $provinceAvgData==0){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = '费用消耗指数（妇科）低于全省该专科平均水平30%以上，得20分';
        }elseif($percent>=20){
            $data['score'] = 15;
            $data['desc'] = '费用消耗指数（妇科）低于全省该专科平均水平20-29%，得15分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = '费用消耗指数（妇科）低于全省该专科平均水平10-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 5;
            $data['desc'] = '费用消耗指数（妇科）低于全省该专科平均水平0-9%，得5分';
        }
        return $data;
    }

    /**
     * 费用消耗指数（产科）
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得20分；低于平均水平20-29%，得15分；低于平均水平10-19%，得10分；低于平均水平0-9%，得5分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientFyxhCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '费用消耗指数（产科）高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_fyxh_ck');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_fyxh_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 20;
            $data['desc'] = '全省该专科平均水平为0，该专科费用消耗指数（产科）也为0，得20分';
            return $data;
        }
        if($avgData>$provinceAvgData || $provinceAvgData==0){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = '费用消耗指数（产科）低于全省该专科平均水平30%以上，得20分';
        }elseif($percent>=20){
            $data['score'] = 15;
            $data['desc'] = '费用消耗指数（产科）低于全省该专科平均水平20-29%，得15分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = '费用消耗指数（产科）低于全省该专科平均水平10-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 5;
            $data['desc'] = '费用消耗指数（产科）低于全省该专科平均水平0-9%，得5分';
        }
        return $data;
    }

    /**
     * 时间消耗指数
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得40分；≤20-29%，得30分；≤10-19%，得20分；≤0-9%，得10分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientSjxhScore()
    {
        $data['score'] = 0;
        $data['desc'] = '时间消耗指数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_sjxh');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_sjxh');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 40;
            $data['desc'] = '全省该专科平均水平为0，该专科时间消耗指数也为0，得40分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 40;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平30%以上，得40分';
        }elseif($percent>=20){
            $data['score'] = 30;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平20-29%，得30分';
        }elseif($percent>=10){
            $data['score'] = 20;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平10-19%，得20分';
        }elseif($percent>=0){
            $data['score'] = 10;
            $data['desc'] = '时间消耗指数低于全省该专科平均水平0-9%，得10分';
        }
        return $data;
    }

    /**
     * 时间消耗指数（妇科）
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得20分；≤20-29%，得15分；≤10-19%，得10分；≤0-9%，得5分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientSjxhFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '时间消耗指数（妇科）高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_sjxh_fk');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_sjxh_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 20;
            $data['desc'] = '全省该专科平均水平为0，该专科时间消耗指数（妇科）也为0，得20分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = '时间消耗指数（妇科）低于全省该专科平均水平30%以上，得20分';
        }elseif($percent>=20){
            $data['score'] = 15;
            $data['desc'] = '时间消耗指数（妇科）低于全省该专科平均水平20-29%，得15分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = '时间消耗指数（妇科）低于全省该专科平均水平10-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 5;
            $data['desc'] = '时间消耗指数（妇科）低于全省该专科平均水平0-9%，得5分';
        }
        return $data;
    }

    /**
     * 时间消耗指数（产科）
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得20分；≤20-29%，得15分；≤10-19%，得10分；≤0-9%，得5分；高于平均水平，不得分。
     * @return array
     */
    function getEfficientSjxhCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '时间消耗指数（产科）高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('efficient_sjxh_ck');
        $avgData = $this->project->getAvgDataByIndexCode('efficient_sjxh_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 20;
            $data['desc'] = '全省该专科平均水平为0，该专科时间消耗指数（产科）也为0，得20分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 20;
            $data['desc'] = '时间消耗指数（产科）低于全省该专科平均水平30%以上，得20分';
        }elseif($percent>=20){
            $data['score'] = 15;
            $data['desc'] = '时间消耗指数（产科）低于全省该专科平均水平20-29%，得15分';
        }elseif($percent>=10){
            $data['score'] = 10;
            $data['desc'] = '时间消耗指数（产科）低于全省该专科平均水平10-19%，得10分';
        }elseif($percent>=0){
            $data['score'] = 5;
            $data['desc'] = '时间消耗指数（产科）低于全省该专科平均水平0-9%，得5分';
        }
        return $data;
    }

    /**
     * 早期介入（包含医疗、治疗、护理）
     * 1.神经康复早期介入时间（从入院到介入时间）：48小时以内6分，72小时以内4分，120小时以内2分，>120小时0分；早期介入（例数/3年）：神经康复2400例-2000例/3年得4分，2000例-1500例/3年得3分，1500-1000例/3年得2分，<1000例/3年得1分；
     * 2.骨科康复早期介入时间（从手术到介入时间）：骨科手术48小时以内6分，72小时以内4分，120小时以内2分，>120小时0分；早期介入（例数/3年）：骨科康复1800-1500例/3年得4分，1500例-1200例/3年得3分，1200-800例/3年2分，<800例/3年得1分，；
     * 3.重症康复早期介入时间（从入院到介入时间）：48小时以内4分，72小时以内2分，120小时以内1分，>120小时0分；早期介入（例数/3年）：重症医学300-200例/3年得4分，200例-100例/3年得2分，<100/3年得1分。
     * 累计最高不超过28分。
     * @return array
     */
    function getEfficientZqjrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '均不符合条件，不得分';
        $desc = [];
        //1.神经康复
        //1.1早期介入时间
        $sjAvgData['ncz'] = $this->project->getAvgDataByIndexCode('efficient_zqjr_ncz');
        $sjAvgData['ncx'] = $this->project->getAvgDataByIndexCode('efficient_zqjr_ncx');
        $sjAvgCount = 0;
        $sjAvgSum = 0;
        $haveData = false;
        foreach ($sjAvgData as $sjData){
            if((string)$sjData!='无'){
                $sjAvgCount++;
                $sjAvgSum+=$sjData;
                $haveData = true;
            }
        }
        if($haveData===false) $avgData = '无';
        else $avgData = $sjAvgCount > 1 ? $sjAvgSum / $sjAvgCount : $sjAvgSum;

        if($avgData=='无'){
            $desc[] = '神经康复早期介入时间无数据，不得分';
        }else{
            if($avgData<48){
                $data['score'] += 6;
                $desc[] = '神经康复早期介入时间（从入院到介入时间）：48小时以内得6分';
            }elseif($avgData<72){
                $data['score'] += 4;
                $desc[] = '神经康复早期介入时间（从入院到介入时间）：72小时以内得4分';
            }elseif($avgData<120){
                $data['score'] += 2;
                $desc[] = '神经康复早期介入时间（从入院到介入时间）：120小时以内得2分';
            }else{
                $desc[] = '神经康复早期介入时间（从入院到介入时间）：大于120小时得0分';
            }
        }

        //1.2 早期介入（例数/3年）
        $avgData = $this->project->getAvgDataByIndexCode('efficient_zqjr_sjk');
        if((string)$avgData=='无'){
            $desc[] = '神经康复早期介入例数无数据，不得分';
        }else{
            if($avgData>2400){
                $desc[] = '神经康复大于2400例，得0分';
            }elseif($avgData>=2000){
                $data['score'] += 4;
                $desc[] = '神经康复2400例-2000例，得4分';
            }elseif($avgData>=1500){
                $data['score'] += 3;
                $desc[] = '神经康复2000例-1500例，得3分';
            }elseif($avgData>=1000){
                $data['score'] += 2;
                $desc[] = '神经康复1500-1000例，得2分';
            }elseif($avgData<1000){
                $data['score'] += 1;
                $desc[] = '神经康复<1000例，得1分';
            }
        }

        //2.骨科康复
        //2.1 早期介入时间
        $gkAvgData['kzh'] = $this->project->getAvgDataByIndexCode('efficient_zqjr_kzh');
        $gkAvgData['xzh'] = $this->project->getAvgDataByIndexCode('efficient_zqjr_xzh');
        $gkAvgData['gkss'] = $this->project->getAvgDataByIndexCode('efficient_zqjr_gkss');
        $gkAvgCount = 0;
        $gkAvgSum = 0;
        $haveData = false;
        foreach ($gkAvgData as $gkData){
            if((string)$gkData!='无'){
                $gkAvgCount++;
                $gkAvgSum+=$gkData;
                $haveData = true;
            }
        }

        if($haveData===false) $avgData = '无';
        else $avgData = $gkAvgCount > 1 ? $gkAvgSum / $gkAvgCount : $gkAvgSum;


        if($avgData=='无'){
            $desc[] = '骨科康复早期介入时间无数据，不得分';
        }else{
            if($avgData<48){
                $data['score'] += 6;
                $desc[] = '骨科康复早期介入时间（从入院到介入时间）：48小时以内得6分';
            }elseif($avgData<72){
                $data['score'] += 4;
                $desc[] = '骨科康复早期介入时间（从入院到介入时间）：72小时以内得4分';
            }elseif($avgData<120){
                $data['score'] += 2;
                $desc[] = '骨科康复早期介入时间（从入院到介入时间）：120小时以内得2分';
            }else{
                $desc[] = '骨科康复早期介入时间（从入院到介入时间）：大于120小时得0分';
            }
        }

        //2.1 早期介入（例数/3年）
        $avgData = $this->project->getAvgDataByIndexCode('efficient_zqjr_gkkf');
        if((string)$avgData=='无'){
            $desc[] = '骨科康复早期介入例数无数据，不得分';
        }else{
            if($avgData>1800){
                $desc[] = '骨科康复大于1800例，得0分';
            }elseif($avgData>=1500){
                $data['score'] += 4;
                $desc[] = '骨科康复1800-1500例，得4分';
            }elseif($avgData>=1200){
                $data['score'] += 3;
                $desc[] = '骨科康复1500例-1200例，得3分';
            }elseif($avgData>=800){
                $data['score'] += 2;
                $desc[] = '骨科康复1200-800例，得2分';
            }elseif($avgData<800){
                $data['score'] += 1;
                $desc[] = '骨科康复<800例，得1分';
            }
        }

        //3.重症康复
        //3.1 早期介入时间
        $avgData = $this->project->getAvgDataByIndexCode('efficient_zqjr_zzsj');

        if((string)$avgData=='无'){
            $desc[] = '重症康复早期介入时间无数据，不得分';
        }else{
            if($avgData<48){
                $data['score'] += 4;
                $desc[] = '重症康复早期介入时间（从入院到介入时间）：48小时以内得4分';
            }elseif($avgData<72){
                $data['score'] += 2;
                $desc[] = '重症康复早期介入时间（从入院到介入时间）：72小时以内得2分';
            }elseif($avgData<120){
                $data['score'] += 1;
                $desc[] = '重症康复早期介入时间（从入院到介入时间）：120小时以内得1分';
            }else{
                $desc[] = '重症康复早期介入时间（从入院到介入时间）：大于120小时得0分';
            }
        }

        //2.1 早期介入（例数/3年）
        $avgData = $this->project->getAvgDataByIndexCode('efficient_zqjr_zzls');
        if((string)$avgData=='无'){
            $desc[] = '重症康复早期介入例数无数据，不得分';
        }else{
            if($avgData>300){
                $desc[] = '重症康复大于300例，得0分';
            }elseif($avgData>=200){
                $data['score'] += 4;
                $desc[] = '重症康复300-200例，得4分';
            }elseif($avgData>=100){
                $data['score'] += 3;
                $desc[] = '重症康复200例-100例，得2分';
            }elseif($avgData<100){
                $data['score'] += 1;
                $desc[] = '重症康复<100例，得1分';
            }
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 住院患者跌倒发生率
     * 该专科全省排名：排名前25%（含25%），得16分；排名25%-50%（含50%），得13分；排名50%-75%（含75%）,得10分；其余得0分。
     * @return array
     */
    function getSafetyDdfsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '住院患者跌倒发生率排名未进前75%，得0分';
        $db = sf::getLib('db');
        $indexCode = 'safety_ddfs';
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        $rankStr = $rank.'/'.$count;
//        $html = Button::setUrl(site_url('index/rank/icu/id/'.$this->project->getProjectId()))->window($rankStr);
        if($rankPercent<=25){
            $data['score']=16;
            $desc[] = '住院患者跌倒发生率排名前25%（'.$rankStr.'），得16分';
        }elseif($rankPercent<=50){
            $data['score']=13;
            $desc[] = '住院患者跌倒发生率排名25%-50%（'.$rankStr.'），得13分';
        }elseif($rankPercent<=75){
            $data['score']=10;
            $desc[] = '住院患者跌倒发生率排名50%-75%（'.$rankStr.'），得10分';
        }else{
            $data['score']=0;
            $desc[] = '住院患者跌倒发生率排名靠后（'.$rankStr.'），不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 住院患者压力性损伤发生率
     * 该专科全省排名：排名前25%（含25%），得16分；排名25%-50%（含50%），得13分；排名50%-75%（含75%）,得10分；其余得0分。
     * @return array
     */
    function getSafetyYlssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '住院患者压力性损伤发生率排名未进前75%，得0分';
        $db = sf::getLib('db');
        $indexCode = 'safety_ylss';
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        $rankStr = $rank.'/'.$count;
        if($rankPercent<=25){
            $data['score']=16;
            $desc[] = '住院患者压力性损伤发生率排名前25%（'.$rankStr.'），得16分';
        }elseif($rankPercent<=50){
            $data['score']=13;
            $desc[] = '住院患者压力性损伤发生率排名25%-50%（'.$rankStr.'），得13分';
        }elseif($rankPercent<=75){
            $data['score']=10;
            $desc[] = '住院患者压力性损伤发生率排名50%-75%（'.$rankStr.'），得10分';
        }else{
            $data['score']=0;
            $desc[] = '住院患者压力性损伤发生率排名靠后（'.$rankStr.'），不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 住院患者呼吸机相关性肺炎感染发生率
     * 该专科全省排名：排名前25%（含25%），得16分；排名25%-50%（含50%），得13分；排名50%-75%（含75%）,得10分；其余得0分。
     * @return array
     */
    function getSafetyFygrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '住院患者呼吸机相关性肺炎感染发生率排名未进前75%，得0分';
        $db = sf::getLib('db');
        $indexCode = 'safety_fygr';
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        $rankStr = $rank.'/'.$count;
        if($rankPercent<=25){
            $data['score']=16;
            $desc[] = '住院患者呼吸机相关性肺炎感染发生率排名前25%（'.$rankStr.'），得16分';
        }elseif($rankPercent<=50){
            $data['score']=13;
            $desc[] = '住院患者呼吸机相关性肺炎感染发生率排名25%-50%（'.$rankStr.'），得13分';
        }elseif($rankPercent<=75){
            $data['score']=10;
            $desc[] = '住院患者呼吸机相关性肺炎感染发生率排名50%-75%（'.$rankStr.'），得10分';
        }else{
            $data['score']=0;
            $desc[] = '住院患者呼吸机相关性肺炎感染发生率排名靠后（'.$rankStr.'），不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 住院患者中心静脉导管相关血流感染发生率
     * 该专科全省排名：排名前25%（含25%），得16分；排名25%-50%（含50%），得13分；排名50%-75%（含75%）,得10分；其余得0分。
     * @return array
     */
    function getSafetyXlgrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '住院患者中心静脉导管相关血流感染发生率排名未进前75%，得0分';
        $db = sf::getLib('db');
        $indexCode = 'safety_xlgr';
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        $rankStr = $rank.'/'.$count;
        if($rankPercent<=25){
            $data['score']=16;
            $desc[] = '住院患者中心静脉导管相关血流感染发生率排名前25%（'.$rankStr.'），得16分';
        }elseif($rankPercent<=50){
            $data['score']=13;
            $desc[] = '住院患者中心静脉导管相关血流感染发生率排名25%-50%（'.$rankStr.'），得13分';
        }elseif($rankPercent<=75){
            $data['score']=10;
            $desc[] = '住院患者中心静脉导管相关血流感染发生率排名50%-75%（'.$rankStr.'），得10分';
        }else{
            $data['score']=0;
            $desc[] = '住院患者中心静脉导管相关血流感染发生率排名靠后（'.$rankStr.'），不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 住院患者导尿管相关尿路感染发生率
     * 该专科全省排名：排名前25%（含25%），得16分；排名25%-50%（含50%），得13分；排名50%-75%（含75%）,得10分；其余得0分。
     * @return array
     */
    function getSafetyNlgrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '住院患者导尿管相关尿路感染发生率排名未进前75%，得0分';
        $db = sf::getLib('db');
        $indexCode = 'safety_nlgr';
        $desc = [];
        $subjectCode = $this->project->getSubjectCode();
        $indexYears = [$this->project->getStartYear(),$this->project->getEndYear()-1,$this->project->getEndYear()];
        $avgData = $this->project->getAvgDataByIndexCode($indexCode);
        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id ORDER BY data desc) t";
        $count = $db->result_first($sql);        //总数

        $sql = "select count(*) from (SELECT stage_id,ROUND(AVG(data),2) data FROM `stage_datas` where index_code = '{$indexCode}' and index_year IN (".implode(',',$indexYears).")  and stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' ) GROUP BY stage_id having data > '{$avgData}' ORDER BY data desc) t";
        $rank = (int)$db->result_first($sql)+1;        //排名
        $rankPercent = sprintf('%.2f',($rank/$count*100));
        $rankStr = $rank.'/'.$count;
        if($rankPercent<=25){
            $data['score']=16;
            $desc[] = '住院患者导尿管相关尿路感染发生率排名前25%（'.$rankStr.'），得16分';
        }elseif($rankPercent<=50){
            $data['score']=13;
            $desc[] = '住院患者导尿管相关尿路感染发生率排名25%-50%（'.$rankStr.'），得13分';
        }elseif($rankPercent<=75){
            $data['score']=10;
            $desc[] = '住院患者导尿管相关尿路感染发生率排名50%-75%（'.$rankStr.'），得10分';
        }else{
            $data['score']=0;
            $desc[] = '住院患者导尿管相关尿路感染发生率排名靠后（'.$rankStr.'），不得分';
        }

        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 本专业特色医疗质量管理情况
     * 1.妇科住院患者死亡率：与全省该专科平均水平比较：低于平均水平20%，得10分；低于平均水平0-19%，得5分；高于平均水平，不得分。
     * 2.择期妇科手术患者手术并发症发生率：包括肺栓塞、深静脉血栓、术后感染、临近器官损伤、术后出血或血肿、手术伤口裂开、猝死、重要器官功能衰竭、生理/代谢紊乱、手术过程中异物遗留、麻醉并发症、植入物相关并发症、移植并发症以及介入操作相关并发症。与全省该专科平均水平比较：低于平均水平20%，得10分；低于平均水平0-19%，得5分；高于平均水平，不得分。
     * 累计不超过20分。
     * @return array
     */
    function getSafetyTsylScore()
    {
        $data['score'] = 0;
        $data['desc'] = '均不符合条件，不得分';

        //妇科住院患者死亡率
        $desc = [];
        $provinceAvgData = $this->project->getProvinceAvgData('safety_tsyl_zrsw');
        $avgData = $this->project->getAvgDataByIndexCode('safety_tsyl_zrsw');
        if((string)$avgData=='无'){
            $desc[] = '妇科住院患者死亡率无数据，不得分';
        }else{
            if($provinceAvgData==0 && $avgData==0){
                //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
                $data['score'] += 10;
                $desc[] = '全省该专科平均水平为0，该专科妇科住院患者死亡率也为0，得10分';
            }elseif($avgData>$provinceAvgData || $provinceAvgData==0){
                $desc[] = '妇科住院患者死亡率高于全省该专科平均水平，不得分';
            }else{
                $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
                $percent = (float)sprintf('%.2f',$percent);
                if($percent>=20){
                    $data['score'] += 10;
                    $desc[] = '妇科住院患者死亡率低于全省该专科平均水平20%以上，得10分';
                }elseif($percent>=0){
                    $data['score'] += 5;
                    $desc[] = '妇科住院患者死亡率低于全省该专科平均水平0-19%，得5分';
                }
            }
        }

        //择期妇科手术患者手术并发症发生率
        $provinceAvgData = $this->project->getProvinceAvgData('safety_tsyl_fkss');
        $avgData = $this->project->getAvgDataByIndexCode('safety_tsyl_fkss');
        if((string)$avgData=='无'){
            $desc[] = '妇科手术患者手术并发症发生率无数据，不得分';
        }else{
            if($provinceAvgData==0 && $avgData==0){
                //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
                $data['score'] += 10;
                $desc[] = '全省该专科平均水平为0，该专科妇科手术患者手术并发症发生率也为0，得10分';
            }elseif($avgData>$provinceAvgData || $provinceAvgData==0){
                $desc[] = '妇科手术患者手术并发症发生率高于全省该专科平均水平，不得分';
            }else{
                $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
                $percent = (float)sprintf('%.2f',$percent);
                if($percent>=20){
                    $data['score'] += 10;
                    $desc[] = '妇科手术患者手术并发症发生率低于全省该专科平均水平20%以上，得10分';
                }elseif($percent>=0){
                    $data['score'] += 5;
                    $desc[] = '妇科手术患者手术并发症发生率低于全省该专科平均水平0-19%，得5分';
                }
            }

        }

        $desc = array_filter($desc);
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 中低风险组死亡率
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得24分；低于平均水平20-29%，得18分；低于平均水平10-19%，得12分；低于平均水平0-9%，得6分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyZdfxScore()
    {
        $data['score'] = 0;
        $data['desc'] = '中低风险组死亡率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_zdfx');
        $avgData = $this->project->getAvgDataByIndexCode('safety_zdfx');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 24;
            $data['desc'] = '全省该专科平均水平为0，该专科中低风险组死亡率也为0，得24分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 24;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平30%以上，得24分';
        }elseif($percent>=20){
            $data['score'] = 18;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平20-29%，得18分';
        }elseif($percent>=10){
            $data['score'] = 12;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平10-19%，得12分';
        }elseif($percent>=0){
            $data['score'] = 6;
            $data['desc'] = '中低风险组死亡率低于全省该专科平均水平0-9%，得6分';
        }
        return $data;
    }

    /**
     * 中低风险组死亡率（妇科）
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得10分；低于平均水平20-29%，得7.5分；低于平均水平10-19%，得5分；低于平均水平0-9%，得2.5分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyZdfxFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '中低风险组死亡率（妇科）高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_zdfx_fk');
        $avgData = $this->project->getAvgDataByIndexCode('safety_zdfx_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 10;
            $data['desc'] = '全省该专科平均水平为0，该专科中低风险组死亡率（妇科）也为0，得10分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 10;
            $data['desc'] = '中低风险组死亡率（妇科）低于全省该专科平均水平30%以上，得10分';
        }elseif($percent>=20){
            $data['score'] = 7.5;
            $data['desc'] = '中低风险组死亡率（妇科）低于全省该专科平均水平20-29%，得7.5分';
        }elseif($percent>=10){
            $data['score'] = 5;
            $data['desc'] = '中低风险组死亡率（妇科）低于全省该专科平均水平10-19%，得5分';
        }elseif($percent>=0){
            $data['score'] = 2.5;
            $data['desc'] = '中低风险组死亡率（妇科）低于全省该专科平均水平0-9%，得2.5分';
        }
        return $data;
    }

    /**
     * 中低风险组死亡率（产科）
     * 与全省该专科平均水平比较：低于平均水平30%及以上，得10分；低于平均水平20-29%，得7.5分；低于平均水平10-19%，得5分；低于平均水平0-9%，得2.5分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyZdfxCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '中低风险组死亡率（产科）高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_zdfx_ck');
        $avgData = $this->project->getAvgDataByIndexCode('safety_zdfx_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 10;
            $data['desc'] = '全省该专科平均水平为0，该专科中低风险组死亡率（产科）也为0，得10分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=30){
            $data['score'] = 10;
            $data['desc'] = '中低风险组死亡率（产科）低于全省该专科平均水平30%以上，得10分';
        }elseif($percent>=20){
            $data['score'] = 7.5;
            $data['desc'] = '中低风险组死亡率（产科）低于全省该专科平均水平20-29%，得7.5分';
        }elseif($percent>=10){
            $data['score'] = 5;
            $data['desc'] = '中低风险组死亡率（产科）低于全省该专科平均水平10-19%，得5分';
        }elseif($percent>=0){
            $data['score'] = 2.5;
            $data['desc'] = '中低风险组死亡率（产科）低于全省该专科平均水平0-9%，得2.5分';
        }
        return $data;
    }

    /**
     * 本专业重点病种（单病种）医疗质量管理情况
     * 累计不超过32分
     * @return array
     */
    function getSafetyYlzlScore()
    {
        $worker = $this->project->worker('apply',true);
        $configs = $worker->getConfigs('safety');
        $safetyData = [];
        foreach ($configs['zdbz'] as $k=>$config){
            if($config==0) continue;
            switch ($k){
                case 'cjfy':
                    //单病种次均费用
                    $safetyData['cjfy'] = $this->getSafetyYlzlCjfyScore();
                    break;
                case 'cfl':
                    //单病种出院31天重返率
                    $safetyData['cfl'] = $this->getSafetyYlzlCflScore();
                    break;
                case 'swl':
                    //患者死亡率
                    $safetyData['swl'] = $this->getSafetyYlzlSwlScore();
                    break;
                case 'bls':
                    //单病种例数
                    $safetyData['bls'] = $this->getSafetyYlzlBlsScore();
                    break;
                case 'zyr':
                    //单病种平均住院日
                    $safetyData['zyr'] = $this->getSafetyYlzlZyrScore();
                    break;
                case 'bsl':
                    //单病种病死率
                    $safetyData['bsl'] = $this->getSafetyYlzlBslScore();
                    break;
            }
        }
        $data['score'] = 0;
        $data['desc'] = '不符合条件，不得分';
        $desc = [];
        foreach ($safetyData as $k=>$v){
            $data['score'] +=  $v['score'];
            $desc[] =  $v['desc'];
        }
        if($data['score']>=32){
            $desc[] = '累计不超过32分';
        }
        $desc = array_filter($desc);
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 患者死亡率
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得20分；低于平均水平0-19%，得10分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlSwlScore()
    {
        $data['score'] = 0;
        $data['desc'] = '患者死亡率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_swl');
        $avgData = $this->project->getAvgDataByIndexCode('safety_ylzl_swl');
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 20;
            $data['desc'] = '患者死亡率低于全省该专科平均水平20%及以上，得20分';
        }elseif($percent>=0){
            $data['score'] = 10;
            $data['desc'] = '患者死亡率低于全省该专科平均水平0-19%，得10分';
        }
        return $data;
    }


    /**
     * 单病种例数
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得16分；低于平均水平0-19%，得8分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlBlsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种例数高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_swl');
        $avgData = $this->project->getAvgDataByIndexCode('safety_ylzl_bls');
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 16;
            $data['desc'] = '单病种例数低于全省该专科平均水平20%及以上，得16分';
        }elseif($percent>=0){
            $data['score'] = 8;
            $data['desc'] = '单病种例数低于全省该专科平均水平0-19%，得8分';
        }
        return $data;
    }


    /**
     * 单病种平均住院日
     * 与全省该专科平均水平比较：低于平均水平20%及以上，得16分；低于平均水平0-19%，得8分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlZyrScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种平均住院日高于全省该专科平均水平，不得分';

        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_zyr');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_zyr','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '单病种平均住院日无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 16;
            $data['desc'] = '全省该专科平均水平为0，该专科单病种平均住院日也为0，得16分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 16;
            $data['desc'] = '单病种平均住院日低于全省该专科平均水平20%及以上，得16分';
        }elseif($percent>=0){
            $data['score'] = 8;
            $data['desc'] = '单病种平均住院日低于全省该专科平均水平0-19%，得8分';
        }
        return $data;
    }

    /**
     * 次均费用
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得16分；低于平均水平0-19%，得8分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlCjfyScore()
    {
        $data['score'] = 0;
        $data['desc'] = '次均费用高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_cjfy');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_cjfy','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '次均费用无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 16;
            $data['desc'] = '全省该专科平均水平为0，该专科次均费用也为0，得16分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 16;
            $data['desc'] = '次均费用低于全省该专科平均水平20%及以上，得16分';
        }elseif($percent>=0){
            $data['score'] = 8;
            $data['desc'] = '次均费用低于全省该专科平均水平0-19%，得8分';
        }
        return $data;
    }

    /**
     * 单病种病死率
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得16分；低于平均水平0-19%，得8分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlBslScore()
    {
        $data['score'] = 0;
        $data['desc'] = '单病种病死率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_bsl');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_bsl','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '单病种病死率无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 16;
            $data['desc'] = '全省该专科平均水平为0，该专科单病种病死率也为0，得16分';
            return $data;
        }

        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 16;
            $data['desc'] = '单病种病死率低于全省该专科平均水平20%及以上，得16分';
        }elseif($percent>=0){
            $data['score'] = 8;
            $data['desc'] = '单病种病死率低于全省该专科平均水平0-19%，得8分';
        }
        return $data;
    }

    /**
     * 出院31天重返率
     * 与该专科全省平均水平比较：低于平均水平20%及以上，得16分；低于平均水平0-19%，得8分；高于平均水平，不得分。
     * @return array
     */
    function getSafetyYlzlCflScore()
    {
        $data['score'] = 0;
        $data['desc'] = '出院31天重返率高于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('safety_ylzl_cfl');
        $indexCodes = $this->project->getChildIndexCodes('safety_ylzl_cfl','safety');
        $avgData = $this->project->getAvgDataByIndexCodes($indexCodes);
        if((string)$avgData=='无'){
            $data['desc'] = '出院31天重返率无数据，不得分';
            return $data;
        }
        if($provinceAvgData==0 && $avgData==0){
            //全省平均是0，如果医院的指标也是0，要得最高分（适用于低优指标）
            $data['score'] = 16;
            $data['desc'] = '全省该专科平均水平为0，该专科出院31天重返率也为0，得16分';
            return $data;
        }
        if($avgData>$provinceAvgData){
            return $data;
        }
        $percent = ($provinceAvgData-$avgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 16;
            $data['desc'] = '出院31天重返率低于全省该专科平均水平20%及以上，得16分';
        }elseif($percent>=0){
            $data['score'] = 8;
            $data['desc'] = '出院31天重返率低于全省该专科平均水平0-19%，得8分';
        }
        return $data;
    }

    /**
     * 合理安全用药
     * 1.住院患者抗菌药物使用强度≤20得10分，逐年降低得5分。
     * 2.门诊患者抗菌药物使用率＜20%得10分，急诊患者抗菌药物使用率<50%得8分，住院患者抗菌药物使用率<60%得5分，其余不得分。
     * @return array
     */
    function getSafetyAqyyScore()
    {
        $data['score'] = 0;
        $data['desc'] = '均不符合条件，不得分';
        $desc = [];
        //1.住院患者抗菌药物使用强度
        $avgData = $this->project->getAvgDataByIndexCode('safety_aqyy_syqd');
        if($avgData<=20){
            $data['score']+=10;
            $desc[] = '住院患者抗菌药物使用强度≤20得10分';
        }else{
            $firstYear = $this->project->getStartYear();
            $secondYear = $this->project->getStartYear()+1;
            $thirdYear = $this->project->getStartYear()+2;
            $thirdYearData = $this->project->getDataByIndexCode('safety_aqyy_syqd',$thirdYear)->getData();
            $secondYearData = $this->project->getDataByIndexCode('safety_aqyy_syqd',$secondYear)->getData();
            $firstYearData = $this->project->getDataByIndexCode('safety_aqyy_syqd',$firstYear)->getData();
            if($thirdYearData<$secondYearData && $secondYearData<$firstYearData){
                $data['score']+=5;
                $desc[] = '住院患者抗菌药物使用强度逐年降低得5分';
            }else{
                $desc[] = '住院患者抗菌药物使用强度不符合要求，不得分';
            }
        }
        //2.门诊患者抗菌药物使用率
        $avgData = $this->project->getAvgDataByIndexCode('safety_aqyy_syl');
        if($avgData<20){
            $data['score']+=10;
            $desc[] = '门诊患者抗菌药物使用率＜20%得10分';
        }elseif($avgData<50){
            $data['score']+=8;
            $desc[] = '门诊患者抗菌药物使用率＜50%得8分';
        }elseif($avgData<60){
            $data['score']+=5;
            $desc[] = '门诊患者抗菌药物使用率＜60%得5分';
        }else{
            $desc[] = '门诊患者抗菌药物使用率不符合要求，不得分';
        }

        if ($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 省级及以上科研项目数（近3年）
     * 牵头承担国家级科研项目4分/项；省部级科研项目2分/项；省卫健委科研项目1分/项（省卫健委科研项目累计不超过6分）。
    累计不超过12分。
     * @return array
     */
    function getInnovateKyxmScore()
    {
        $data['score'] = 0;
        $data['desc'] = '无省级及以上科研项目，不得分';
        $researchProjects = $this->project->researchProjects('apply',true);
        $count['nation'] = 0;
        $count['province'] = 0;
        $count['swjw'] = 0;
        while($researchProject = $researchProjects->getObject()){
            switch ($researchProject->getLevel()){
                case '国家级':
                    $count['nation']++;
                    break;
                case '省部级':
                    $count['province']++;
                    break;
                case '省卫健委':
                    $count['swjw']++;
                    break;
            }
        }
        $desc = [];
        if($count['nation']>0){
            $data['score']+=$count['nation']*4;
            $desc[] = '国家级'.$count['nation'].'项，得'.($count['nation']*4).'分';
        }
        if($count['province']>0){
            $data['score']+=$count['province']*2;
            $desc[] = '省部级'.$count['province'].'项，得'.($count['province']*2).'分';
        }

        if($count['swjw']>5){
            $data['score']+=6;
            $desc[] = '省卫健委'.$count['swjw'].'项，得6分';
        }elseif($count['swjw']>0){
            $data['score']+=$count['swjw']*1;
            $desc[] = '省卫健委'.$count['swjw'].'项，得'.($count['swjw']*1).'分';
        }

        if($data['score']>12) {
            $data['score']=12;
            $desc[] = '累计不超过12分';
        }
        if ($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 省级医学重点学科（甲级）
     * 属于省级医学重点学科（甲级）的专科得4分，否则不得分。。
     * @return array
     */
    function getSpecialtyCxjcZdxkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不属于省级医学重点学科（甲级），不得分';
        if($this->project->getDataByIndexCode('specialty_cxjc_zdxk')->getData()=='是'){
            $data['score'] = 4;
            $data['desc'] = '属于省级医学重点学科（甲级），得4分';
        }
        return $data;
    }

    /**
     * 省级医学重点学科（甲级）
     * 属于省级医学重点学科（甲级）的专科得4分，否则不得分。。
     * @return array
     */
    function getInnovateZdxkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '不属于省级医学重点学科（甲级），不得分';
        if($this->project->getDataByIndexCode('innovate_zdxk')->getData()=='是'){
            $data['score'] = 4;
            $data['desc'] = '属于省级医学重点学科（甲级），得4分';
        }
        return $data;
    }

    /**
     * 省卫生健康适宜技术推广项目数
     * 一项得1分，累计不超过7分。
     * @return array
     */
    function getInnovateSyjsScore()
    {
        $data['score'] = 0;
        $data['desc'] = '无省卫生健康适宜技术推广项目，不得分';
        $syjsProjects = $this->project->syjsProjects('apply',true);
        $projectCount = $syjsProjects->getTotal();
        if($projectCount>0){
            $data['score'] = $projectCount >= 7 ? 7 : $projectCount;
            $data['desc'] = '省卫生健康适宜技术推广项目数：'.$projectCount.'项，得'.$data['score'].'分。';
        }
        return $data;
    }

    /**
     * 临床科研成果所获奖励
     * 作为前三完成单位获得国家级奖励一等奖得8分，二等奖得6分；作为前三完成单位获得省部级一等奖得5分，二等奖得3分，三等奖得2分。
     * 满分：8
     * @return array
     */
    function getInnovateKycgHjxxScore()
    {
        $data['score'] = 0;
        $data['desc'] = '无国家级及省部级奖励';
        $awards = $this->project->awards('apply',true);
        $count['nation_1'] = 0;
        $count['nation_2'] = 0;
        $count['province_1'] = 0;
        $count['province_2'] = 0;
        $count['province_3'] = 0;
        while($award = $awards->getObject()){
            if($award->getCompanyRank()==0 && $award->getCompanyRank()>3) continue;
            if($award->getLevel()=='国家级' && $award->getRank()=='一等奖'){
                $count['nation_1']++;
            }
            if($award->getLevel()=='国家级' && $award->getRank()=='二等奖'){
                $count['nation_2']++;
            }
            if($award->getLevel()=='省部级' && $award->getRank()=='一等奖'){
                $count['province_1']++;
            }
            if($award->getLevel()=='省部级' && $award->getRank()=='二等奖'){
                $count['province_2']++;
            }
            if($award->getLevel()=='省部级' && $award->getRank()=='三等奖'){
                $count['province_3']++;
            }
        }
        $score = 0;
        $desc = [];
        if($count['nation_1']>0){
            $score+=$count['nation_1']*8;
            $desc[] = '国家级奖励一等奖'.$count['nation_1'].'项，得'.($count['nation_1']*8).'分';
        }
        if($count['nation_2']>0){
            $score+=$count['nation_2']*6;
            $desc[] = '国家级奖励二等奖'.$count['nation_2'].'项，得'.($count['nation_2']*6).'分';
        }
        if($count['province_1']>0){
            $score+=$count['province_1']*5;
            $desc[] = '省部级奖励一等奖'.$count['province_1'].'项，得'.($count['province_1']*5).'分';
        }
        if($count['province_2']>0){
            $score+=$count['province_2']*3;
            $desc[] = '省部级奖励二等奖'.$count['province_2'].'项，得'.($count['province_2']*3).'分';
        }
        if($count['province_3']>0){
            $score+=$count['province_3']*2;
            $desc[] = '省部级奖励三等奖'.$count['province_3'].'项，得'.($count['province_3']*2).'分';
        }
        if($score>8){
            $desc[] = '因累计不超过8分，故该项得8分';
        }
        $data['score'] = $score>=8 ? 8 : $score;
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 临床科研成果发明专利
     * 获得国际发明专利得8分，国内发明专利得5分。
     * @return array
     */
    function getInnovateKycgFmzlScore()
    {
        $data['score'] = 0;
        $data['desc'] = '无发明专利，不得分';
        $patents = $this->project->patents('apply',true);
        $count['global'] = 0;
        $count['nation'] = 0;

        while($patent = $patents->getObject()){
            if($patent->getLevel()=='国际发明专利'){
                $count['global']++;
            }
            if($patent->getLevel()=='中国发明专利'){
                $count['nation']++;
            }
        }
        $score = 0;
        $desc = [];
        if($count['global']>0){
            $score+=$count['global']*8;
            $desc[] = '获得国际发明专利'.$count['global'].'项，得'.($count['global']*8).'分';
        }
        if($count['nation']>0){
            $score+=$count['nation']*5;
            $desc[] = '获得中国发明专利'.$count['nation'].'项，得'.($count['nation']*5).'分';
        }
        if($score>8){
            $desc[] = '因累计不超过8分，故该项得8分';
        }
        $data['score'] = $score>=8 ? 8 : $score;
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 临床科研成果发明专利
     * 1.作为前三完成单位获得国家级奖励一等奖得8分，二等奖得6分；作为前三完成单位获得省部级一等奖得5分，二等奖得3分，三等奖得2分。
     * 2.获得国际发明专利得8分，国内发明专利得5分。
     * @return array
     */
    function getInnovateKycgScore()
    {
        //奖励
        $hjData = $this->getInnovateKycgHjxxScore();
        //发明专利
        $fmData = $this->getInnovateKycgFmzlScore();
        $data['score'] = $hjData['score']+$fmData['score'];
        $data['score'] = $data['score']>=8 ? 8 : $data['score'];
        $desc[] = $hjData['desc'];
        $desc[] = $fmData['desc'];
        if($data['score']>=8){
            $desc[] = '累计不超过8分';
        }
        $desc = array_filter($desc);
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 人员基本结构情况
     * 分值：30
     * 1.医师队伍研究生学历人员比例≥70%，得23分；60-69%，得15分；50-59%，得8分；≤49%，不得分。
    2.医师队伍年龄结构合理，年龄50岁及以上的医师比例为20%-40%、40-49岁的医师比例为30%-50%，各得11分，均符合要求得22分，不符合要求不得分。
     * @return array
     */
    function getTalentPersonStructureScore()
    {
        $score = 0;
        $desc = [];
        $members = $this->project->memberStructures('apply',true);
        $ysCount = 0;
        $yjsCount = 0;
        $oldCount = 0;
        $youngCount = 0;
        while($member = $members->getObject()){
            if($member->getPersonnelType()=='医师') $ysCount++;
            if($member->getPersonnelType()=='医师' && strstr($member->getEducation(),'研究生')!==false) $yjsCount++;
            if($member->getPersonnelType()=='医师' && $member->getAge()>=50) $oldCount++;
            if($member->getPersonnelType()=='医师' && $member->getAge()>=40 && $member->getAge()<50) $youngCount++;
        }
        $yjsPercent = (float)sprintf('%.2f',$yjsCount/$ysCount*100);
        $oldPercent = (float)sprintf('%.2f',$oldCount/$ysCount*100);
        $youngPercent = (float)sprintf('%.2f',$youngCount/$ysCount*100);

        $desc[] = '医师总人数：'.$ysCount;
        if($yjsPercent>=70) {
            $score+=23;
            $desc[] = '研究生人数：'.$yjsCount.'，研究生比例：'.$yjsPercent.'%，得23分';
        }
        elseif($yjsPercent>=60) {
            $score+=15;
            $desc[] = '研究生人数：'.$yjsCount.'，研究生比例：'.$yjsPercent.'%，得15分';
        }
        elseif($yjsPercent>=50) {
            $score+=8;
            $desc[] = '研究生人数：'.$yjsCount.'，研究生比例：'.$yjsPercent.'%，得8分';
        }else{
            $desc[] = '研究生人数：'.$yjsCount.'，研究生比例：'.$yjsPercent.'%，不得分';
        }

        if($oldPercent>=20 && $oldPercent<=40) {
            $score+=11;
            $desc[] = '年龄50岁及以上医师人数：'.$oldCount.'，年龄50岁及以上医师比例：'.$oldPercent.'%，得11分';
        }else{
            $desc[] = '年龄50岁及以上医师人数：'.$oldCount.'，年龄50岁及以上医师比例：'.$oldPercent.'%，不得分';
        }
        if($youngPercent>=30 && $youngPercent<=50) {
            $score+=11;
            $desc[] = '40-49岁的医师人数：'.$youngCount.'，40-49岁的医师比例：'.$youngPercent.'%，得11分';
        }else{
            $desc[] = '40-49岁的医师人数：'.$youngCount.'，40-49岁的医师比例：'.$oldPercent.'%，不得分';
        }
        return ['score'=>$score,'desc'=>implode('；',$desc)];
    }

    /**
     * 梯队结构配置情况
     * 分值：45
     * 医师团队高级职称比例20%-40%、中级职称比例30-50%，各得22.5分。范围外不得分。
     * @return array
     */
    function getTalentTeamStructureScore()
    {
        $score = 0;
        $desc = [];
        $members = $this->project->memberStructures('apply',true);
        $ysCount = 0;
        $highCount = 0;
        $middleCount = 0;
        while($member = $members->getObject()){
            if($member->getPersonnelType()=='医师') $ysCount++;
            if($member->getPersonnelType()=='医师' && in_array($member->getTitleType(),['正高','副高'])) $highCount++;
            if($member->getPersonnelType()=='医师' && $member->getTitleType()=='中级') $middleCount++;
        }
        $highPercent = (float)sprintf('%.2f',$highCount/$ysCount*100);
        $middlePercent = (float)sprintf('%.2f',$middleCount/$ysCount*100);

        $desc[] = '医师总人数：'.$ysCount;
        if($highPercent>=20 && $highPercent<=40) {
            $score+=22.5;
            $desc[] = '高级职称医师人数：'.$highCount.'，高级职称医师比例：'.$highPercent.'%，得22.5分';
        }else{
            $desc[] = '高级职称医师人数：'.$highCount.'，高级职称医师比例：'.$highPercent.'%，不符合要求，不得分';
        }
        if($middlePercent>=30 && $middlePercent<=50) {
            $score+=22.5;
            $desc[] = '中级职称医师人数：'.$middleCount.'，中级职称医师比例：'.$middlePercent.'%，得22.5分';
        }else{
            $desc[] = '中级职称医师人数：'.$middleCount.'，中级职称医师比例：'.$middlePercent.'%，不符合要求，不得分';
        }
        return ['score'=>$score,'desc'=>implode('；',$desc)];
    }

    /**
     * 亚专科学科带头人及骨干发展情况
     * 分值：60
     * 获得国家级人才称号60分，省级人才称号45分，省卫健委或者省中药管理局人才称号30分。个人或多人获得多个级别人才称号时，按照就高不就低原则，只取其中一人获得的最高级别人才称号得分。
     * @return array
     */
    function getTalentSeniorScore()
    {
        $members = $this->project->memberSeniors('apply',true);
        $count['nation'] = 0;
        $count['province'] = 0;
        $count['swjw'] = 0;
        $count['szyj'] = 0;
        while($member = $members->getObject()){
            if($member->getHonorLevel()=='国家') $count['nation']++;
            if($member->getHonorLevel()=='省级') $count['province']++;
            if($member->getHonorLevel()=='省卫健委') $count['swjw']++;
            if($member->getHonorLevel()=='省中医局') $count['szyj']++;
        }
        $score = 0;
        $desc = '未获得人才称号，不得分';
        if($count['nation']){
            $score=60;
            $desc = '获得国家级人才称号，得60分';
        }elseif($count['province']){
            $score=45;
            $desc = '获得省级人才称号，得45分';
        }elseif($count['swjw'] || $count['szyj']){
            $score=30;
            $desc = '获得省卫健委或者省中药管理局人才称号，得30分';
        }
        return ['score'=>$score,'desc'=>$desc];
    }

    /**
     * 麻醉科医护比
     * 麻醉科医护比=麻醉科护士总数/麻醉科医师总数。
     * 麻醉科医护比≤2，得满分；2-3，得20分；3-4（不含4），得10分；4-6（不包括6），得5分。否则不得分。
     * @return array
     */
    function getTalentYhbScore()
    {
        $data['score'] = 0;
        $data['desc'] = '麻醉科医护比>6，不得分';
        $avgData = $this->project->getAvgDataByIndexCode('member_yhb_yhb3');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData<=2){
            $data['score']=30;
            $data['desc'] = '麻醉科医护比≤2，得30分';
        }elseif($avgData<3){
            $data['score']=20;
            $data['desc'] = '麻醉科医护比2-3（不含3），得20分';
        }elseif($avgData<4){
            $data['score']=10;
            $data['desc'] = '麻醉科医护比3-4（不含4），得10分';
        }elseif($avgData<6){
            $data['score']=5;
            $data['desc'] = '麻醉科医护比4-6（不包括6），得5分';
        }

        return $data;
    }

    /**
     * 远程会诊、会诊技术指导
     * 分值：15
     * 远程会诊1次1.5分/次，现场指导1.5分/次，累计不超过15分。
    上报非急诊科数据，该项不得分。
     * @return array
     */
    function getInfluenceFsnlYchz()
    {
        $data['score'] = 0;
        $data['desc'] = '无远程会诊、会诊技术指导，不得分';
        $desc = [];
        $count = $this->project->getAvgDataByIndexCode('influence_fsnl_ychz');
        $xccount = $this->project->getAvgDataByIndexCode('influence_fsnl_xchz');
        if($count>=10){
            $data['score']+=15;
            $desc[] = '远程会诊'.$count.'次，得15分';
        }elseif($count<10){
            $score = $count*1.5;
            $data['score']+=$score;
            $desc[] = '远程会诊'.$count.'次，得'.($score).'分';
        }
        if($xccount>=10){
            $data['score']+=15;
            $desc[] = '现场指导'.$xccount.'次，得15分';
        }elseif($xccount<10){
            $score = $xccount*1.5;
            $data['score']+=$score;
            $desc[] = '现场指导'.$xccount.'次，得'.($score).'分';
        }
        if($data['score']>15) $desc[] = '累计不超过15分';
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;
    }

    /**
     * 年出院患者中市外（省外）患者比例
     * 分值：25
     * 与全省该专科平均水平比较：≥20%，得25分；≥0-19%，得13分；低于平均水平，不得分。
     * @return array
     */
    function getInfluenceFsnlSwhzScore()
    {
        $data['score'] = 0;
        $data['desc'] = '年出院患者中市外（省外）患者比例低于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('influence_fsnl_swhz');
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_swhz');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData<$provinceAvgData){
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 25;
            $data['desc'] = '年出院患者中市外（省外）患者比例≥全省该专科平均水平20%，得25分';
        }elseif($percent>=0){
            $data['score'] = 13;
            $data['desc'] = '年出院患者中市外（省外）患者比例≥全省该专科平均水平0-19%，得13分';
        }
        return $data;
    }

    /**
     * 年出院患者中市外（省外）患者比例（妇科）
     * 分值：12.5
     * 与全省该专科平均水平比较：≥20%，得12.5分；≥0-19%，得6.5分；低于平均水平，不得分。
     * @return array
     */
    function getInfluenceFsnlSwhzFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '年出院患者中市外（省外）患者比例（妇科）低于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('influence_fsnl_swhz_fk');
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_swhz_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData<$provinceAvgData){
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 12.5;
            $data['desc'] = '年出院患者中市外（省外）患者比例（妇科）≥全省该专科平均水平20%，得12.5分';
        }elseif($percent>=0){
            $data['score'] = 6.5;
            $data['desc'] = '年出院患者中市外（省外）患者比例（妇科）≥全省该专科平均水平0-19%，得6.5分';
        }
        return $data;
    }

    /**
     * 年出院患者中市外（省外）患者比例（产科）
     * 分值：12.5
     * 与全省该专科平均水平比较：≥20%，得12.5分；≥0-19%，得6.5分；低于平均水平，不得分。
     * @return array
     */
    function getInfluenceFsnlSwhzCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '年出院患者中市外（省外）患者比例（产科）低于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('influence_fsnl_swhz_ck');
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_swhz_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData<$provinceAvgData){
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 12.5;
            $data['desc'] = '年出院患者中市外（省外）患者比例（产科）≥全省该专科平均水平20%，得12.5分';
        }elseif($percent>=0){
            $data['score'] = 6.5;
            $data['desc'] = '年出院患者中市外（省外）患者比例（产科）≥全省该专科平均水平0-19%，得6.5分';
        }
        return $data;
    }

    /**
     * 年接受下级医院急危重症和疑难病患者转诊数量
     * 分值：25
     * 与全省该专科平均水平比较：≥20%，得25分；≥0-19%，得13分；低于平均水平，不得分。
     * @return array
     */
    function getInfluenceFsnlZzslScore()
    {
        $data['score'] = 0;
        $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量低于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('influence_fsnl_zzsl');
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_zzsl');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData<$provinceAvgData){
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 25;
            $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量≥全省该专科平均水平20%，得25分';
        }elseif($percent>=0){
            $data['score'] = 13;
            $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量≥全省该专科平均水平0-19%，得13分';
        }
        return $data;
    }

    /**
     * 年接受下级医院急危重症和疑难病患者转诊数量（妇科）
     * 分值：12.5
     * 与全省该专科平均水平比较：≥20%，得12.5分；≥0-19%，得6.5分；低于平均水平，不得分。
     * @return array
     */
    function getInfluenceFsnlZzslFkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量（妇科）低于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('influence_fsnl_zzsl_fk');
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_zzsl_fk');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData<$provinceAvgData){
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 12.5;
            $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量（妇科）≥全省该专科平均水平20%，得12.5分';
        }elseif($percent>=0){
            $data['score'] = 6.5;
            $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量（妇科）≥全省该专科平均水平0-19%，得6.5分';
        }
        return $data;
    }

    /**
     * 年接受下级医院急危重症和疑难病患者转诊数量（产科）
     * 分值：12.5
     * 与全省该专科平均水平比较：≥20%，得12.5分；≥0-19%，得6.5分；低于平均水平，不得分。
     * @return array
     */
    function getInfluenceFsnlZzslCkScore()
    {
        $data['score'] = 0;
        $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量（产科）低于全省该专科平均水平，不得分';
        $provinceAvgData = $this->project->getProvinceAvgData('influence_fsnl_zzsl_ck');
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_zzsl_ck');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        if($avgData<$provinceAvgData){
            return $data;
        }
        $percent = ($avgData-$provinceAvgData)/$provinceAvgData*100;
        $percent = (float)sprintf('%.2f',$percent);
        if($percent>=20){
            $data['score'] = 12.5;
            $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量（产科）≥全省该专科平均水平20%，得12.5分';
        }elseif($percent>=0){
            $data['score'] = 6.5;
            $data['desc'] = '年接受下级医院急危重症和疑难病患者转诊数量（产科）≥全省该专科平均水平0-19%，得6.5分';
        }
        return $data;
    }

    /**
     * 接收各类护士培训情况
     * 分值：25
     * 接收实习护士（或专科护士或规培护士或进修护士）0.01分/人。累计不超过25分。
     * @return array
     */
    function getInfluenceFsnlPxslScore()
    {
        $data['score'] = 0;
        $data['desc'] = '接收实习护士（或专科护士或规培护士或进修护士）0人，不得分';
        $avgData = $this->project->getAvgDataByIndexCode('influence_fsnl_pxsl');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $data['score'] = $avgData*0.01;
        if( $data['score']>=25){
            $data['score'] = 25;
            $data['desc'] = '接收实习护士（或专科护士或规培护士或进修护士）'.$avgData.'人，得25分';
        }else{
            $data['desc'] = '接收实习护士（或专科护士或规培护士或进修护士）'.$avgData.'人，得'.$data['score'].'分';
        }
        return $data;
    }

    /**
     * 牵头或参与制定国家级诊疗规范、指南等的数量（个）
     * 分值：15
     * 牵头制定诊疗规范/指南15分/个，参与制定（署名）4分/个，累计不超过15分。
     * @return array
     */
    function getInfluenceYxlZlgfScore()
    {
        $guidelines = $this->project->guidelines('apply',true);
        $count['lead'] = 0;
        $count['join'] = 0;
        while($guideline = $guidelines->getObject()){
            if($guideline->getType()=='牵头') $count['lead']++;
            if($guideline->getType()=='参与') $count['join']++;
        }
        $score = 0;
        $desc = [];
        if($count['lead']){
            $score+=$count['lead']*15;
            $desc[] = '牵头制定诊疗规范/指南'.$count['lead'].'个，得'.($count['lead']*15).'分';
        }
        if($count['join']){
            $score+=$count['join']*4;
            $desc[] = '参与制定（署名）'.$count['join'].'个，得'.($count['join']*4).'分';
        }
        if($score>15){
            $score = 15;
            $desc[] = '因累计不超过15分，故该项得15分';
        }
        return ['score'=>$score,'desc'=>implode('：',$desc)];
    }

    /**
     * 承担国家、省、市级质控中心工作个数
     * 分值：20
     * 承担国家级质控中心20分/个，省级质控中心15分/个，市质控中心10分/个，没有则不得分。承担多个级别质控中心时，按照就高不就低原则得分。
     * @return array
     */
    function getInfluenceYxlZkzxScore()
    {
        $centers = $this->project->centers('apply',true);
        $count['nation'] = 0;
        $count['province'] = 0;
        $count['city'] = 0;
        while($center = $centers->getObject()){
            if($center->getLevel()=='国家级') $count['nation']++;
            if($center->getLevel()=='省级') $count['province']++;
            if($center->getLevel()=='市级') $count['city']++;
        }
        $score = 0;
        $desc = [];
        if($count['nation']){
            $score+=$count['nation']*20;
            $desc[] = '承担国家级质控中心'.$count['nation'].'个，得'.($count['nation']*20).'分';
        }elseif($count['province']){
            $score+=$count['province']*15;
            $desc[] = '承担省级质控中心'.$count['province'].'个，得'.($count['province']*15).'分';
        }elseif($count['city']){
            $score+=$count['city']*10;
            $desc[] = '承担市级质控中心'.$count['city'].'个，得'.($count['city']*10).'分';
        }
        return ['score'=>$score,'desc'=>implode('：',$desc)];
    }

    /**
     * 现任或曾任本专科省级及以上主要学术组织常委或编委以上数量
     * 分值：15
     * 1.担任国家级本专业学术组织（医学会、医师协会、预防医学会、口腔医学会、护理协会（含二级分会/专委会））常委及以上每人得8分，担任委员以上每人4分；
     * 2、担任省级专业学术组织（医学会、医师协会、预防医学会、口腔医学会、护理协会（含二级分会/专委会））主任委员及以上每人得7分，副主任委员每人得4分，常委每人2分；
     * 累计不超过15分。
     * @return array
     */
    function getInfluenceYxlXszzScore()
    {
        $academics = $this->project->academics('apply',true);
        $count['nation_cw'] = 0;
        $count['nation_wy'] = 0;
        $count['province_zr'] = 0;
        $count['province_fzr'] = 0;
        $count['province_cw'] = 0;
        $userNames['nation_cw'] = [];
        $userNames['nation_wy'] = [];
        $userNames['province_zr'] = [];
        $userNames['province_fzr'] = [];
        $userNames['province_cw'] = [];
        while($academic = $academics->getObject()){
            if($academic->getLevel()=='国家级' && in_array($academic->getDuty(),['会长','副会长','理事长','副理事长','秘书长','副秘书长','主任委员','副主任委员','常务委员'])) {
                $count['nation_cw']++;
                $userNames['nation_cw'][] = $academic->getUserName();
            }
            if($academic->getLevel()=='国家级' && in_array($academic->getDuty(),['委员','其他'])) {
                $count['nation_wy']++;
                $userNames['nation_wy'][] = $academic->getUserName();
            }
            if($academic->getLevel()=='省级' && in_array($academic->getDuty(),['会长','副会长','理事长','副理事长','秘书长','副秘书长','主任委员'])) {
                $count['province_zr']++;
                $userNames['province_zr'][] = $academic->getUserName();
            }
            if($academic->getLevel()=='省级' && in_array($academic->getDuty(),['副主任委员'])) {
                $count['province_fzr']++;
                $userNames['province_fzr'][] = $academic->getUserName();
            }
            if($academic->getLevel()=='省级' && in_array($academic->getDuty(),['常务委员','其他'])) {
                $count['province_cw']++;
                $userNames['province_cw'][] = $academic->getUserName();
            }
        }
        $userNames['nation_cw'] = array_unique($userNames['nation_cw']);
        $userNames['nation_wy'] = array_unique($userNames['nation_wy']);
        $userNames['province_zr'] = array_unique($userNames['province_zr']);
        $userNames['province_fzr'] = array_unique($userNames['province_fzr']);
        $userNames['province_cw'] = array_unique($userNames['province_cw']);
        $score = 0;
        $desc = [];
        $userUses = [];
        if($userNames['nation_cw']){
            $count = 0;
            $names = [];
            foreach ($userNames['nation_cw'] as $username){
                if(in_array($username,$userUses)) continue;
                $names[] = $username;
                $userUses[] = $username;
                $count++;
            }
            if($count>0){
                $score+=$count*8;
                $desc[] = '国家级本专业学术组织常委以上'.$count.'人：'.implode('、',$names).'，得'.($count*8).'分';
            }
        }
        if($userNames['nation_wy']){
            $count = 0;
            $names = [];
            foreach ($userNames['nation_wy'] as $username){
                if(in_array($username,$userUses)) continue;
                $names[] = $username;
                $userUses[] = $username;
                $count++;
            }
            if($count>0){
                $score+=$count*4;
                $desc[] = '国家级本专业学术组织委员'.$count.'人：'.implode('、',$names).'，得'.($count*4).'分';
            }
        }
        if($userNames['province_zr']){
            $count = 0;
            $names = [];
            foreach ($userNames['province_zr'] as $username){
                if(in_array($username,$userUses)) continue;
                $names[] = $username;
                $userUses[] = $username;
                $count++;
            }
            if($count>0){
                $score+=$count*7;
                $desc[] = '省级本专业学术组织主任'.$count.'人：'.implode('、',$names).'，得'.($count*7).'分';
            }
        }
        if($userNames['province_fzr']){
            $count = 0;
            $names = [];
            foreach ($userNames['province_fzr'] as $username){
                if(in_array($username,$userUses)) continue;
                $names[] = $username;
                $userUses[] = $username;
                $count++;
            }
            if($count>0){
                $score+=$count*4;
                $desc[] = '省级本专业学术组织副主任'.$count.'人：'.implode('、',$names).'，得'.($count*4).'分';
            }
        }
        if($userNames['province_cw']){
            $count = 0;
            $names = [];
            foreach ($userNames['province_cw'] as $username){
                if(in_array($username,$userUses)) continue;
                $names[] = $username;
                $userUses[] = $username;
                $count++;
            }
            if($count>0){
                $score+=$count*2;
                $desc[] = '省级本专业学术组织常委'.$count.'人：'.implode('、',$names).'，得'.($count*2).'分';
            }
        }
        return ['score'=>$score,'desc'=>implode('：',$desc)];
    }

    /**
     * 健康传播影响力
     * 科普演讲、科普作品获国家级（含中华护理学会或同级别学会）表彰2.5分/个；获省级（含四川省护理学会或同级别学会）表彰1分/个。累计最高不超过5分。；
     * 累计不超过5分。
     * @return array
     */
    function getInfluenceYxlJkcbScore()
    {
        $score = 0;
        $desc = [];
        $count['nation'] = 0;
        $count['province'] = 0;
        $works = $this->project->getWorks('jkcb');
        while($work = $works->getObject()){
            if($work->getDuty()=='国家级'){
                $count['nation']++;
            }
            if($work->getDuty()=='省级'){
                $count['province']++;
            }
        }
        if($count['nation']>0){
            $score+=$count['nation']*2.5;
            $desc[] = '获国家级表彰'.$count['nation'].'个，得'.($count['nation']*2.5).'分';
        }
        if($count['province']>0){
            $score+=$count['province']*1;
            $desc[] = '获省级表彰'.$count['province'].'个，得'.$count['province'].'分';
        }
        if($score>5){
            $score = 5;
            $desc[]= '累计最高不超过5分';
        }
        return ['score'=>$score,'desc'=>implode('：',$desc)];
    }

    /**
     * 近3年复旦专科声誉排行榜排名-全国榜单
     * 分值：15
     * 上榜得15分，提名得10分；
     * @return array
     */
    function getPlusFudanNationScore()
    {
        $data['score'] = 0;
        $data['desc'] = '未进全国榜单';
        $ranks = $this->project->getCountryRanks();
        $desc = [];
        $count['sb'] = 0;
        $count['tm'] = 0;
        while($rank = $ranks->getObject()){
            if($rank->getData()=='提名'){
                $count['tm']++;
            }else{
                $count['sb']++;
            }
        }
        if($count['sb']>0){
            $data['score'] += 15;
            $desc[] = '全国榜单上榜'.$count['sb'].'次';
        }
        if($count['tm']>0){
            $data['score'] += 10;
            $desc[] = '全国榜单提名'.$count['tm'].'次';
        }
        if($desc){
            $data['desc'] = implode('；',$desc);
        }

        return $data;
    }

    /**
     * 近3年复旦专科声誉排行榜排名-西南榜单
     * 分值：10
     *  西南前五得10分，提名得5分。
     * @param $value
     * @return array
     */
    function getPlusFudanSouthwestScore()
    {
        $data['score'] = 0;
        $data['desc'] = '未进西南榜单';
        $ranks = $this->project->getSouthwestRanks();
        $desc = [];
        $count['sb'] = 0;
        $count['tm'] = 0;
        $count['rank'] = [];
        while($rank = $ranks->getObject()){
            $rankData = $rank->getData();
            if($rankData=='提名'){
                $count['tm']++;
            }else{
                $result = preg_match('/(\(|（)(.*)(\)|）)/',$rankData,$matches);
                $rank = (int)$matches[2];
                if($rank>0 && $rank<=5) {
                    $count['sb']++;
                }
            }
        }
        if($count['sb']>0){
            $data['score'] += 10;
            $desc[] = '西南榜单前五'.$count['sb'].'次';
        }
        if($count['tm']>0){
            $data['score'] += 5;
            $desc[] = '西南榜单提名'.$count['tm'].'次';
        }
        if($desc){
            $data['desc'] = implode('；',$desc);
        }

        return $data;
    }

    /**
     * 复旦专科声誉排行榜排名（近3年）
     * 1、全国榜单，上榜得15分，提名得10分；
     * 2、西南前五得10分，提名得5分。
     * 累计不超过15分。
     * @param $value
     * @return array
     */
    function getPlusFudanScore()
    {
        //全国榜单
        $nationData = $this->getPlusFudanNationScore();
        //西南榜单
        $westData = $this->getPlusFudanSouthwestScore();
        $data['score'] = $nationData['score']+$westData['score'];
        $data['score'] = $data['score']>=15 ? 15 : $data['score'];
        $desc[] = $nationData['desc'];
        $desc[] = $westData['desc'];
        if($data['score']>=15){
            $desc[] = '累计不超过15分';
        }
        $desc = array_filter($desc);
        if($desc) $data['desc'] = implode('；',$desc);
        return $data;

    }

    /**
     * 全省三级医院医疗服务能力和质量得分排名
     * 分值：20
     *  排名第1-3名得20分，第4-10名得10分。
     * @param $value
     * @return array
     */
    function getPlusHuaxiScore()
    {
        $data['score'] = 0;
        $data['desc'] = '无排名';
        $rank = $this->project->getHuaxiRank();
        if($rank=='-') return $data;
        $rank = (int)$rank;
        if($rank==0) return $data;
        if($rank>10){
            $data['desc'] = '未进前10';
            return $data;
        }
        if($rank>=4){
            $data['score'] = 10;
            $data['desc'] = '排名第'.$rank;
            return $data;
        }
        $data['score'] = 20;
        $data['desc'] = '排名第'.$rank;
        return $data;
    }
}