## 说明
从apply里拷贝过来的数据，需要替换以下内容：
将`project_datas`替换为`stage_datas`
将
```
project_id in (select project_id from projects where subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."' and statement IN (10,18,20,28,29,30))
```
替换为：
```
stage_id in (select stage_id from stages where user_role = 2 and subject_code = '{$subjectCode}' and declare_year = '".$this->project->getDeclareYear()."' and type_current_group = '".$this->project->getTypeCurrentGroup()."' and cat_id = '".$this->project->getCatId()."')
```