<?php
namespace App\model\Assess\Zdzk\Stage;
class Xwk extends Common
{
    /**
     * 四级手术占比
     * 60%及以上得30分。小于60%的按照降序赋分：专科四级手术占比/60%*30。
     * @return array
     */
    function getAbilitySjssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_sjss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=60){
            $data['score'] = 30;
            $data['desc'] = '四级手术占比≥60%，得30分';
        }else{
            $score = $percent/60*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "四级手术占比小于60%的按照降序赋分：{$percent}/60*30";
        }
        return $data;
    }

    /**
     * 微创手术占比
     * 50%及以上得30分。小于50%的按照降序赋分：专科微创手术占比/50%*30。
     * @return array
     */
    function getAbilityWcssScore()
    {
        $data['score'] = 0;
        $data['desc'] = '';
        $avgData = $this->project->getAvgDataByIndexCode('ability_wcss');
        if((string)$avgData=='无'){
            $data['desc'] = '无数据，不得分';
            return $data;
        }
        $percent = (float)sprintf('%.2f',$avgData);
        if($percent>=50){
            $data['score'] = 30;
            $data['desc'] = '微创手术占比≥50%，得30分';
        }else{
            $score = $percent/50*30;
            $data['score'] = (float)sprintf('%.1f',$score);
            $data['desc'] = "微创手术占比小于50%的按照降序赋分：{$percent}/50*30";
        }
        return $data;
    }
}