<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseAgreements;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Agreements extends BaseAgreements
{
	function selectByUserId($user_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_id` = '".$user_id."' AND year = '".config::get("current_declare_year",date("Y"))."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
			$this->setUserId($user_id);
			$this->setYear(config::get("current_declare_year",date("Y")));
		}
		return $this;
	}
}