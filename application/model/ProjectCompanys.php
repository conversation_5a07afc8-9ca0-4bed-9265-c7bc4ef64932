<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseProjectCompanys;

class ProjectCompanys extends BaseProjectCompanys
{
    function selectByProjectId($project_id='',$type='apply')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `project_id` = '{$project_id}' and `type` = '{$type}'");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else{
            $this->setProjectId($project_id);
            $this->setType($type);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    function getData($key='')
    {
        if(parent::getDatas()){
            $arr = $this->dejson(parent::getDatas(),true);
            return $key ? $arr[$key] : $arr;
        }else return $key ? '' : [];
    }

    function setData($arr)
    {
        return parent::setDatas(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }

    private function dejson($str){
        $str = $this->replaceQuote($str);
        $str = stripslashes($str);
        $str = str_replace("\\", '\\\\', $str);
        $str = str_replace("\t", '\\t', $str);
        $str = str_replace("\r\n", '\n', $str);
        return json_decode($str, 1);
    }

    private function replaceQuote($string){
        return preg_replace('/\\\"([^"]*)\\\"/',"“$1”",$string);
    }

    function getPrincipal($key='')
    {
        if(parent::getPrincipal()){
            $arr = json_decode(parent::getPrincipal(),true);
            return $key ? $arr[$key] : $arr;
        }else return $key ? '' : [];
    }

    function setPrincipal($arr)
    {
        return parent::setPrincipal(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }

    function getLinkman($key='')
    {
        if(parent::getLinkman()){
            $arr = json_decode(parent::getLinkman(),true);
            return $key ? $arr[$key] : $arr;
        }else return $key ? '' : [];
    }

    function setLinkman($arr)
    {
        return parent::setLinkman(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }

    function getFinance($key='')
    {
        if(parent::getFinance()){
            $arr = json_decode(parent::getFinance(),true);
            return $key ? $arr[$key] : $arr;
        }else return $key ? '' : [];
    }

    function setFinance($arr)
    {
        return parent::setFinance(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }

    function getStaff($key='')
    {
        if(parent::getStaff()){
            $arr = json_decode(parent::getStaff(),true);
            return $key ? $arr[$key] : $arr;
        }else return $key ? '' : [];
    }

    function setStaff($arr)
    {
        return parent::setStaff(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }
}