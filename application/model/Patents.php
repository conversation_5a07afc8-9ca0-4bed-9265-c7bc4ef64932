<?php

namespace App\Model;

use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BasePatents;

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Patents extends BasePatents
{
    private $corporation = NULL;

    function getCorporation($f=false)
    {
        if($this->corporation === NULL || $f) $this->corporation = sf::getModel("Corporations")->selectByUserId(parent::getCorporationId());
        return $this->corporation;
    }

    function selectByPatentId($patent_id = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `patent_id` = '" . $patent_id . "' ");
        if ($db->num_rows($query)) {
            $this->fillObject($db->fetch_array($query));
        } else {
            $this->setPatentId($patent_id ? $patent_id : sf::getLib("MyString")->getRandString());
            $this->setUserId(input::getInput("session.roleuserid"));
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    function selectByWfId($wf_id='')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `wf_id` = '{$wf_id}' ");
        if($db->num_rows($query)){
            $this->fillObject($db->fetch_array($query));
        }else{
            $this->setPatentId(sf::getLib("MyString")->getRandString());
            $this->setWfId($wf_id);
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    function selectBySn($sn)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `sn` = '{$sn}' ");
        if ($db->num_rows($query)) {
            $this->fillObject($db->fetch_array($query));
        } else {
            $this->setPatentId(sf::getLib("MyString")->getRandString());
            $this->setSn($sn);
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachments($limit = 0)
    {
        $addwhere = "(`item_id` = '" . $this->getPatentId() . "' and `item_type` = 'patent') or (`item_type` = 'talent_patent' and item_id = '" . $this->getUserPatentId() . "' and user_id = '" . $this->getUserId() . "')";
        return sf::getModel("Filemanager")->selectAll($addwhere, "order by id asc", $limit);
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'patent', $limit = 0, $orders = 'order by no asc,id asc')
    {
        $addwhere = "`item_id` = '" . $this->getPatentId() . "'";
        if ($itemType != 'all') {
            $addwhere .= " and `item_type` = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }

    // 取得专利状态
    public function getState()
    {
        switch ($this->getStatement()) {
            case '1':
                return '<span class="blue">填写中</span>';
                break;
            case '2':
                return '<span class="orange">待单位审核</span>';
                break;
            case '3':
                return '<span class="red">单位退回</span>';
                break;
            case '8':
                return '<span class="orange">等待受理</span>';
                break;
            case '9':
                return '<span class="red">科创部退回</span>';
                break;
            case '10':
                return '<span class="green">审核通过</span>';
                break;
        }
    }

// 取得专利类型
    public function getTyp()
    {
        switch ($this->getType()) {
            case '1':
                return '国家发明';
                break;
            case '2':
                return '国家实用新型';
                break;
            case '3':
                return '国家外观设计';
                break;
            case '4':
                return '软件著作权';
                break;
            case '5':
                return '国际专利';
                break;
            default:
                return '';
                break;
        }
    }

    // 取得专利状态
    public function getStatu()
    {
        switch ($this->getStatus()) {
            case '1':
                return '受理';
                break;
            case '2':
                return '初审';
                break;
            case '3':
                return '实审';
                break;
            case '4':
                return '授权';
                break;
            case '5':
                return '专利驳回';
                break;
            default:
                return '';
                break;
        }
    }

    /**
     * 人员
     * @return [type] [description]
     */
    public function selectMembers()
    {
        return sf::getModel("ResultMembers")->selectAll("item_id = '" . parent::getPatentId() . "' and item_type = 'patent'");
    }

    //去除重复
    function getRealTotal($statement = '1')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT subject FROM `patents` where statement = " . $statement . " group by subject");
        return $db->num_rows($query);
    }

    function getYear()
    {
        return substr(parent::getDate(), 0, 4);
    }

    function getParentId()
    {
        $corporation = sf::getModel('Corporations')->selectByUserId(parent::getCorporationId());
        return $corporation->getParentId();
    }

    function getUserRank($userid='')
    {
        $userid = $userid?: input::session('roleuserid');
        $member = sf::getModel('ResultMembers')->selectByUserId($userid,$this->getPatentId(),'patent');
        return $member->isNew() ? '-' : $member->getRankStr();
    }

    function addUserToMember()
    {
        if(input::session('userlevel')==2){
            $user = sf::getModel('Declarers')->selectByUserId(input::session('roleuserid'));
            if(!$user->isNew()){
                $member = sf::getModel("ResultMembers")->selectByUserId($user->getUserId(),$this->getPatentId(),'patent');
                if($member->isNew()){
                    $member->setUserName($user->getUserName());
                    $member->setCompanyId($user->getCorporationId());
                    $member->setCompanyName($user->getCorporationName());
                    $member->save();
                }
            }
        }
    }
}