<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseSummaryDatas;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class SummaryDatas extends BaseSummaryDatas
{
    private $platform = NULL;
    private $summary = NULL;

    public function platform($f=false)
    {
        if($this->platform === NULL || $f) $this->platform = sf::getModel("Platforms")->selectByPlatformId($this->getPlatformId());
        return $this->platform;
    }

    public function summary($f=false)
    {
        if($this->summary === NULL || $f) $this->summary = sf::getModel("Summarys")->selectBySummaryId($this->getSummaryId());
        return $this->summary;
    }

    function selectBySummaryId($summaryId,$indexCode,$indexYear='')
    {
        $addwhere = "`summary_id` = '{$summaryId}' and index_code = '{$indexCode}'";
        if($indexYear) $addwhere.=" and index_year = '{$indexYear}'";
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE {$addwhere} order by id desc");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setDeclareYear($this->summary(true)->getDeclareYear());
            $this->setSummaryId($summaryId);
            $this->setIndexCode($indexCode);
            $this->setIndexYear($indexYear);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    public function __toString()
    {
        return (string)$this->getData();
    }
}