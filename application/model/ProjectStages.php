<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseProjectStages;

class ProjectStages extends BaseProjectStages
{
	private $project 	= NULL;
	private $worker 	= NULL;
	private $widgets	= array();
    private $corporation = NULL;

    function getCorporation($f=false)
    {
        if($this->corporation === NULL || $f) $this->corporation = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
        return $this->corporation;
    }

	function selectByStageId($stage_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `stage_id` = '".$stage_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
			$this->setStageId($stage_id?$stage_id:sf::getLib("MyString")->getRandString());
			$this->setUserId(input::session("roleuserid"));
			$this->setUserName(input::session("fullname"));
			$this->setYear(date("Y"));
		}
		return $this;
	}
	
	function selectByProjectId($project_id='',$year='',$type='stage')
	{
		if($year == '') $year = date("Y");
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `project_id` = '".$project_id."' and year = '".$year."' and type = '".$type."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
			$this->setStageId($stage_id?$stage_id:sf::getLib("MyString")->getRandString());
			$this->setType($type);
			$this->setUserId(input::session("roleuserid"));
			$this->setUserName(input::session("fullname"));
			$this->setProjectId($project_id);
			$this->setYear($year);
		}
		return $this;
	}
	
	function getProject($f=false)
	{	
		if($this->project === NULL || $f) $this->project = sf::getModel("Projects")->selectByProjectId(parent::getProjectId());
		return $this->project;
	}
	
	function getBaseinfo()
	{
		return unserialize(parent::getBaseinfo());
	}
	
	function setBaseinfo($v)
	{
		$v = is_array($v) ? $v : array();
		if($v['finance_chief']['fullname']){//保存财务负责人姓名
			$this->setFinanceChief(trim($v['finance_chief']['fullname']));	
		}
		parent::setBaseinfo(serialize(removeQuotation($v)));
	}
	
	function getTarget()
	{
		return unserialize(parent::getTarget());
	}
	
	function setTarget($v)
	{
		$v = is_array($v) ? $v : array(); 
		parent::setTarget(serialize(removeQuotation($v)));
	}
	
	function getMoney()
	{
		return unserialize(parent::getMoney());
	}
	
	function setMoney($v)
	{
		$v = is_array($v) ? $v : array(); 
		parent::setMoney(serialize(removeQuotation($v)));
	}
	
	function getConfigs()
	{
		return json_decode(parent::getConfigs(),true);
	}
	
	function setConfigs($v)
	{
		$v = is_array($v) ? $v : array(); 
		parent::setTarget(json_encode(removeQuotation($v),JSON_UNESCAPED_UNICODE));
	}
	
	function getState()
	{
        switch($this->getStatement())
        {
            case 1:
                return '填写中';
            case 2:
                if($this->getWaitForCompany()<$this->getCompanyLevel()){
                    return '待上级单位审核';
                }
                return '待承担单位审核';
            case 3:
                if($this->getWaitForCompany()<$this->getCompanyLevel()){
                    return '上级单位退回';
                }
                return '<span style="color:red">承担单位退回</span>';
            case 9:
                return '待科创部审核';
            case 10:
                return '已签署';
            case 12:
                return '<span style="color:red">科创部退回</span>';
            default:
                return '状态未知';
        }
	}
	
	/**
	 * 数据验证
	 */
	function validate()
	{
	    return [];
		$message = array();
		$configs = $this->getWidgetConfigs();
		$widget_configs = $this->widget_configs;
		$pager = $this->worker($type)->widgets();
		while($w = $pager->getObject()){ 
			$_msg = array();
			$_widget = NULL;
			if(count($configs[$w->getWidgetName()]) > 0){
				$_configs = array_merge($configs[$w->getWidgetName()],$widget_configs);
			}else $_configs = $widget_configs;
			$_widget = $w->getWidget($_configs);
			$_msg = $_widget->setProject($this)->validate();
			if(count($_msg) > 0) $message = array_merge($message,$_msg);		
		}
		return $message;
	}
	
	/**
	 * 判断是否可以打印
	 */
	function enablePrint()
	{
		if($this->getStatement() >= 5) return true;
		else return false;
	}
	
	/**
	 * 判断是否可以转化为ZIP压缩包
	 */
	function enableZip($type='task')
	{
		if(!$file_name = $this->getPdfPath()) return false;
		//判断是否可以转换
		$info = pathinfo($file_name);
		$ext = strtolower($info['extension']);
		if(in_array($ext,array('pdf'))) return true;
		else return false;
	}
	
	/**
	 * 获取引擎工作器
	 * @param  string $type [description]
	 * @return [type]       [description]
	 */
	function worker($f=false)
	{
		if($this->worker === NULL || $f) $this->worker = sf::getModel("EngineWorkers",$this->getWorkerId());
		return $this->worker;
	}
	
	/**
	 * 取得部件配置信息
	 */
	function getWidgetConfigs($widget_name='')
	{
		//指南配置
		//$guide_configs = $this->getProject(true)->getGuide()->getConfigs();
		//解析器配置
		//$worker_configs = $this->worker()->getConfigs();
		//合并
		//$_configs = array_merge($worker_configs,$guide_configs);
		$_configs = $this->worker()->getConfigs();
		if($widget_name) return $_configs[$widget_name];
		else return $_configs;
	}
	
	/**
	 * 获取项目部件
	 * @param  string $type [description]
	 * @return [type]       [description]
	 */
	function widgets($_widget='')
	{
		if($this->isNew()) return false;
		$_widget = strtolower($_widget);
		if(in_array($_widget,array_keys($this->widgets))) return $this->widgets[$_widget]->setProject($this);
		else{
			//读取指南附件配置
			$configs = $this->getWidgetConfigs();
			$widget_configs = $this->widget_configs;
			$pager = $this->worker()->widgets();
			while($w = $pager->getObject()){
				if(count($configs[$w->getWidgetName()]) > 0){
					$_configs = array_merge($configs[$w->getWidgetName()],$widget_configs);
				}else $_configs = $widget_configs;
				
				$this->widgets[$w->getWidgetName()] = $w->getWidget($_configs);
			}
			if(in_array($_widget,array_keys($this->widgets))) return $this->widgets[$_widget]->setProject($this);
		}
		return '部件不存在！';
	}
	
	/**
	 * 按照项目设置部件配置信息
	 * @param  string $type [description]
	 * @return [type]       [description]
	 */
	function setWidgetConfigs($key,$val)
	{
		$this->widget_configs[$key] = $val;
		return $this;
	}
	
	function contents($type='')
	{
		if(!$type) return array();
		$files = sf::getModel("ProjectContents")->selectAll(" project_id = '".parent::getStageId()."' AND widget_name = '".$type."' ",' order by id desc',1);
		$arr = $files->toArray();
		return $arr[0];	
	}
	
	/**
	 * 获取申报单位信息
	 * @param  string $type [description]
	 * @return [type]       [description]
	 */
	function getCompany($f=false)
	{
		if($this->company === NULL || $f) $this->company = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
		return $this->company;
	}
	
	/**
	 * 获取申报单位信息
	 * @param  string $type [description]
	 * @return [type]       [description]
	 */
	function getUser($f=false)
	{
		if($this->user === NULL || $f) $this->user = sf::getModel("Declarers")->selectByUserId(parent::getUserId());
		return $this->user;
	}

    /**
     * <AUTHOR>
     * @DateTime  2019-08-06
     * @copyright 根据类型获取项目附件
     * @license   [license]
     * @version   [version]
     * @return    [type]      [description]
     */
    function attachements($item_type='',$single_model=false){
        if(!$item_type) return array();

        if($single_model)
            $sql = ' limit 0,1';

        $files = sf::getModel("ProjectAttachments")->selectAll(" item_id = '".parent::getStageId()."' AND item_type = '".$item_type."' ",' order by id desc'.$sql);
        return $files->toArray();
    }

    /**
     * 获取上一级待审核的单位id
     * @return false|string
     */
    function getWaitForCompanyId()
    {
        $level = $this->getWaitForCompany();
        switch ($level){
            case 1:
                return $this->getFirstId();
            case 2:
                return $this->getSecondId();
            case 3:
                return $this->getThirdId();
        }
        return false;
    }
}