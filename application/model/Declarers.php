<?php
namespace App\Model;
use App\Facades\Button;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\Config;
use App\Model\BaseDeclarers;

class Declarers extends BaseDeclarers
{
	private $user = NULL;
	private $corporation = NULL;
	
	function selectByUserId($user_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_id` = '".$user_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setUserId($user_id ? $user_id : sf::getLib("MyString")->getRandString());
		return $this;
	}
	
	function selectByIdcard($idcard='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_idcard` = '".$idcard."' ");

		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
            $this->setUserIdcard($idcard);
            $this->setUserId(sf::getLib("MyString")->getRandString());
        }
		return $this;
	}

	function selectByUserNameAndCompanyName($userName,$companyName)
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `personname` = '{$userName}' and corporation_name = '{$companyName}'");

		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else{
            $this->setPersonname($userName);
            $this->setCorporationName($companyName);
        }
		return $this;
	}
	
	public function getUser($f=false)
	{
		if($this->user === NULL || $f) $this->user = sf::getModel("Users")->selectByUserId($this->getAccountId());
		return $this->user;
	}
	
	public function selectUsers()
	{
		return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '".$this->getUserId()."' AND role_id = '2')");
	}
	
	public function users()
	{
		return $this->selectUsers();
	}
	
	function getAccountId()
	{
		$db = sf::getLib("db");
		$sql = "select user_id from user_roles where user_role_id = '".$this->getUserId()."' and role_id = '2' ";
		$row = $db->fetch_first($sql);
		return $row['user_id'] ? $row['user_id'] : $this->getUserId();
	}

	public function selectTechplans()
	{
		return sf::getModel("UserTechplans")->selectAll("user_id = '".parent::getUserId()."'");
	}

	public function selectAwards()
	{
		return sf::getModel("UserAwards")->selectAll("user_id = '".parent::getUserId()."'");
	}

	public function selectHighAwards()
	{
		return sf::getModel("UserHighAwards")->selectAll("user_id = '".parent::getUserId()."'");
	}
	public function selectUserPatents()
	{
		return sf::getModel("UserPatents")->selectAll("user_id = '".parent::getUserId()."'");
	}
	public function selectPatents()
	{
		return sf::getModel("Patents")->selectAll("user_id = '".parent::getUserId()."'");
	}

	public function selectUserPapers()
	{
		return sf::getModel("UserPapers")->selectAll("user_id = '".parent::getUserId()."'");
	}
	public function selectPapers()
	{
		return sf::getModel("Papers")->selectAll("user_id = '".parent::getUserId()."'");
	}
	public function works()
	{
		return sf::getModel("Works")->selectAll("user_id = '".parent::getUserId()."'");
	}
	
	public function selectEducations()
	{
		return sf::getModel("UserEducations")->selectAll("user_id = '".parent::getUserId()."'");
	}	

	public function selectWorks()
	{
		return sf::getModel("UserWorks")->selectAll("user_id = '".parent::getUserId()."'");
	}

	public function selectHonors()
	{
		return sf::getModel("UserHonors")->selectAll("user_id = '".parent::getUserId()."'");
	}
	public function selectPraises()
	{
		return sf::getModel("UserPraises")->selectAll("user_id = '".parent::getUserId()."'");
	}
	public function selectFocuss()
	{
		return sf::getModel("UserFocuss")->selectAll("user_id = '".parent::getUserId()."'");
	}
	public function selectEngines()
	{
		return sf::getModel("UserEngines")->selectAll("user_id = '".parent::getUserId()."'");
	}	
	public function selectOffices()
	{
		return sf::getModel("UserOffices")->selectAll("user_id = '".$this->getUserId()."'");
	}

	function hasUser($userName='')
	{
		return $this->getUser(true)->hasUser($userName);
	}
	
	function getUserIdcard($len=0,$is_verify=false)
	{
		if(!$is_verify) return $this->_getUserIdCard();
		if($this->getVerify()) return $this->_getUserIdCard();
		else return '<s style="color:red">'.$this->_getUserIdCard().'</s>';
	}

    /**
     * 返回单位ID
     */
    function getCompanyId()
    {
        return $this->getCorporationId();
    }

    /**
     * 返回姓名
     */
    function getFullName()
    {
        return $this->getPersonname();
    }

    /**
     * 设置姓名
     */
    function setFullName($full_name)
    {
        return parent::setPersonname($full_name);
    }
	
	/**
	 * 是否有项目
	 */
    function hasProject($type='',$year='',$project_id='',$current_group='',$catIds=[])
    {
        $addWhere = "user_id = '".parent::getUserId()."' ";
        $project_id && $addWhere .= "AND project_id != '".$project_id."' ";
        if($catIds) {
            $addWhere.=" and cat_id IN (".implode(',',$catIds).")";
        }

        switch(strtoupper($type))
        {
            case 'COMPLETE':
                $addWhere .= "AND statement = '29' ";
                if($this->getIgnoreComplete()) return false;
                break;
            case 'MAJORPROJECT':
                $addWhere .= "AND statement = '29' AND radicate_money > 100 ";
                if($this->getIgnoreComplete()) return false;
                break;
            case 'DECLARER':
                if($year) $addWhere .= "AND declare_year = '".$year."' ";
                if($current_group) $addWhere .= "AND type_current_group = '".$current_group."' ";
                $addWhere .= "AND statement >= 2 and user_name <> '/' AND guide_id NOT IN (SELECT id FROM guides WHERE property like '%no_limit%') ";
                if($this->getIgnoreDeclare()) return false;
                break;
            case 'UNDERWAY'://填报中的
                if($year) $addWhere .= "AND declare_year = '".$year."' ";
                $addWhere .= "AND statement = 1 and user_name <> '/' AND guide_id NOT IN (SELECT id FROM guides WHERE property like '%no_limit%') ";
                if($this->getIgnoreDeclare()) return false;
                break;
            case 'OVERDUE':
                $addWhere .= "AND statement = '29' AND unix_timestamp(real_end_at) < unix_timestamp('2020-09-30 23:59:59') ";
                break;
            default:
                $addWhere .= "AND statement = '29' AND user_name <> '/' ";
                break;
        }
        if(sf::getModel("Projects")->selectAll($addWhere,'ORDER BY id DESC',1)->getTotal()) return true;
        else return false;
    }
	
	/**
	 * 取得项目列表
	 */
	function selectProject($limit=0)
	{
		return sf::getModel("projects")->selectAll("user_id = '".$this->getUserId()."'","ORDER BY id DESC",$limit);
	}

	/**
	 * 取得需要填写中期的项目列表
	 */
	function getStageProjects()
	{
        //如果要填中期评估报告，先把stage_open改为1
		$projects =  sf::getModel("projects")->selectAll("stage_open = 1 and `statement` IN (29,30) and user_id = '".$this->getUserId()."' AND cat_id = 174","ORDER BY id DESC");
        $datas = [];
        while($project =$projects->getObject()){
            $datas[$project->getProjectId()] =  $project->getSubject();
        }
        if(empty($datas)) {
            $datas[0] = '无';
        }
        return $datas;
	}

	/**
	 * 取得参与的项目列表
	 */
	function selectJoinProject($limit=0)
	{
		return sf::getModel("Projects")->selectAll("project_id in (SELECT project_id FROM `project_members` where certificate = '".$this->getUserIdcard()."' and `type` = 'apply')","ORDER BY id DESC",$limit);
	}
	
	function hasUserByCardId($card_id='')
	{
		if($card_id == '') return true;
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_idcard` = '".$card_id."' ");
		if($db->num_rows($query)) return true;
		else return false;
	}
	
	public function cleanObject()
	{
		$this->user = NULL;
		$this->corporation = NULL;
		parent::cleanObject();
	}
	
	public function remove($ids='')
	{
		return false;//屏蔽该方法
		return parent::remove("user_id in('".$ids."')");
	}
	
	public function delete()
	{
		if($this->hasProject(false)) return false;
		sf::getModel("UserRoles")->removeRole($this->getUserId(),2);//删除角色
		return parent::delete();
	}
	
	//取得申报单位高级信息
	public function getCorporations()
	{
		return sf::getModel("corporations")->selectByUserId(parent::getCorporationId());
	}
	
	public function getCorporation()
	{
		return sf::getModel("corporations")->selectByUserId(parent::getCorporationId());
	}

	public function getZdsysCompany($zdsysUserId)
	{
		return sf::getModel("Corporations")->selectByZdsysUserId($zdsysUserId);
	}

	public function getDepartment()
	{
		$corporation = sf::getModel("Corporations")->selectByUserId(parent::getCorporationId());
		return sf::getModel("Corporations")->selectByUserId($corporation->getParentId());
	}

	public function getParent()
	{
		$corporation = sf::getModel("Corporations")->selectByUserId(parent::getCorporationId());
		return $corporation->getParentName();
	}	
	public function getAge()
	{
	    if(parent::getUserBirthday()){
            return date("Y") - substr(parent::getUserBirthday(),0,4);
        }
	    if(parent::getCardType()=='身份证'){
            return sf::getLib('idcard')->getAge(parent::getUserIdcard());
        }
        return '-';
    }
	
	function getState()
	{
		switch(parent::getIsLock())
		{
			case 0:
			return '<label class="label label-success">已认证</label>';
			case 1:
			return '<label class="label label-warning">待单位审核</label>';
			case 2:
			return '<label class="label label-danger">单位退回</label>';
			case 3:
			return '<label class="label label-danger">认证失败</label>';
			case 4:
			return '<s style="color:#F00;">列入黑名单</s>';
			case 5:
			return '<label class="label label-warning">待认证</label>';
			case 6:
			return '<span style="color:#0C6;">更新证件待核</span>';
//			case 7:
//			return '<label class="label label-warning">待上级单位审核</label>';
//			case 8:
//			return '<label class="label label-danger">上级单位退回</label>';
			case 9:
			return '<label class="label label-info">资料修改中</label>';
			default:
			return '<s style="color:#F00;">未知状态</s>';
		}
	}

    /**
     * 待上报的产品数
     */
    function getProductNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
        return $row['num'];
    }
	/**
	 * 待上报的著作数
	 */
	function getWorkNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
		return $row['num'];
	}
    /**
     * 已审核的著作数
     */
    function getAcceptWorkNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
	/**
	 * 待上报的论文数
	 */
	function getPaperNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
		return $row['num'];
	}
    /**
     * 已审核的论文数
     */
    function getAcceptPaperNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
    /**
     * 已审核的新产品数
     */
    function getAcceptProductNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
    /**
     * 已审核的科普文章数
     */
    function getAcceptArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
    /**
     * 已审核的软著数
     */
    function getAcceptSoftNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
	/**
	 * 待上报的专利数
	 */
	function getPatentNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
		return $row['num'];
	}
    /**
     * 待上报的著作权数
     */
    function getSoftNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
        return $row['num'];
    }
    /**
     * 待上报的奖励数
     */
    function getRewardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
        return $row['num'];
    }
    /**
     * 待上报的文章数
     */
    function getArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
        return $row['num'];
    }
    /**
     * 待上报的标准数
     */
    function getStandardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM standards WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
        return $row['num'];
    }
    /**
     * 已审核的专利数
     */
    function getAcceptPatentNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
    /**
     * 待上报的工法数
     */
    function getMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE user_id = '".$this->getUserId()."' and statement in ('1') ");
        return $row['num'];
    }
    /**
     * 已审核的工法数
     */
    function getAcceptMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
    /**
	 * 填报中的项目数
	 */
	function getDeformityNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' and statement in ('0','1','3','6','11') ");
		return $row['num'];
	}
    /**
     * 已立项项目总数
     */
    function getDeformityNum1()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' and statement in ('29') ");
        return $row['num'];
    }
    /**
     * 已结题项目总数
     */
    function getDeformityNum2()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' and statement in ('31') ");
        return $row['num'];
    }
    /**
	 * 填报中的科技奖励数
	 */
	function getDeformityAwardNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM awards WHERE user_id = '".$this->getUserId()."' and statement in ('0','1','3','6','12') ");
		return $row['num'];
	}
    /**
     * 已审核的科技奖励数
     */
    function getAcceptRewardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE user_id = '".$this->getUserId()."' and statement = 10 ");
        return $row['num'];
    }
	/**
	 * 上报的项目数
	 */
	function getSubmitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' and statement > ".config::get("USER_WRITE"));
		return $row['num'];
	}
	 /**
	 * 填报中的预算书数
	 */
	 function getBudgetNum()
	 {
	 	$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' and `budget_open` > 0 and `state_for_budget_book` < 10 AND type_id IN (SELECT type_id from types_switch WHERE has_budget = 1)");
	 	return $row['num'];
	 }
	 /**
	 * 填报中的任务书数
	 */
	 function getTaskNum()
	 {
	 	$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' AND (`statement` = 29 OR task_open > 0) AND cat_id in (174,230,233) AND `state_for_plan_book` IN ('0','1','3','5','7','9','11') AND declare_year>='2023'");
	 	return $row['num'];	
	 }
	 /**
	 * 填报中的中期评估数
	 */
	 function getStageNum()
	 {
         return 0;
	 	$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM stages WHERE user_id = '".$this->getUserId()."' AND statement in (-1,0,1,3,6,12) and worker_id > 0");
	 	return $row['num'];
	 }
	 /**
	 * 填报中的结题申请数
	 */
	 function getApplyNum()
	 {
	 	$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' AND `statement` = 29 AND `state_for_plan_book` = 10 AND  state_for_apply in ('0','1','3','5')");
	 	return $row['num'];	
	 }
	 /**
	 * 填报中的中期报告数
	 */
	 function getZqbgNum()
	 {
	 	$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' AND (`statement` = 29 OR task_open > 0) AND `state_for_plan_book` = 10 AND `state_for_interimreport` IN ('0','4') AND declare_year>='2015'");
	 	return $row['num'];	
	 }
	 /**
	 * 填报中的验收书数
	 */
	 function getCompleteNum()
	 {
	 	$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE user_id = '".$this->getUserId()."' and `statement` = 29  and `state_for_complete_book` in ('0','1','3','5','7','9','11') AND type_id IN (SELECT type_id from types_switch WHERE has_complete = 1)");
	 	return $row['num'];	
	 }
	 
	 /**
	 * 取得快到期的项目列表
	 */
	 function selectExpires()
	 {
	 	return sf::getModel("Projects")->selectAll("user_id = '".$this->getUserId()."' and `statement` = 29 and real_end_at < '".date("Y-m-d",time() + 30*24*60*60)."' and real_end_at > '".date("Y-m-d")."'");
	 }

	 /**
	 * 取得已到期的项目列表
	 */
	 function selectHasExpires()
	 {
	 	return sf::getModel("Projects")->selectAll("user_id = '".$this->getUserId()."' and `statement` = 29 and real_end_at <= '".date("Y-m-d")."' ");
	 }

	 /**
	 * 取得该填写季度报告的项目数量
	 */
	 function getNeedQuartersCount()
	 {
	     $db = sf::getLib('db');
	     //该写季度报告的项目数量
	     $count = $db->result_first("select count(*) c from projects where user_id = '".$this->getUserId()."' and `statement` = 29 and (project_type in (3782,3783,3786) or cat_id = 16)");
	     //已填写的数量
         $season = (int)ceil((date('n'))/3)-1;//上季度是第几季度
         $year = date('Y', mktime(0, 0, 0,$season*3-3+1,1,date('Y')));
         $hasCount = $db->result_first("select count(*) c from project_quarters where user_id = '".$this->getUserId()."' and `year` = '{$year}' and `quarter` = '{$season}'");

	 	return $count-$hasCount;
	 }
	 /**
	  * 附件
	  */
	 function getAttachments()
	 {
	 	return sf::getModel("Filemanager")->selectAll("item_id = '".$this->getUserId()."' AND item_type IN ('user','declare')","ORDER BY created_at ASC");
	 }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'user', $limit = 0, $orders='order by no asc,id asc')
    {
        $addwhere = "`item_id` = '".$this->getUserId()."' and `item_type` = '{$itemType}'";
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }
	 
	 /**
	  * 创新人才附件
	  */
	 function getTalentAttachments($item_id='',$item_type='')
	 {
	 	if($item_id == $item_type){
	 		return sf::getModel("Filemanager")->selectAll(" item_type like 'talent_%' AND user_id = '".$this->getUserId()."'","ORDER BY item_type ASC");
	 	}else{
	 		return sf::getModel("Filemanager")->selectAll(" item_id = '".$item_id."' AND user_id = '".$this->getUserId()."' AND item_type = '".$item_type."' ","ORDER BY item_type ASC");
	 	}
	 }	 
	 /**
	  * 领军人才附件
	  */
	 function getHighAttachments($item_id='',$item_type='')
	 {
	 	if($item_id == $item_type){
	 		return sf::getModel("Filemanager")->selectAll(" item_type like 'high_%' AND user_id = '".$this->getUserId()."'","ORDER BY item_type ASC");
	 	}else{
	 		return sf::getModel("Filemanager")->selectAll(" item_id = '".$item_id."' AND user_id = '".$this->getUserId()."' AND item_type = '".$item_type."' ","ORDER BY item_type ASC");
	 	}
	 }

	/**
	 * 检查身份证是否正确
	 */
	function getVerify()
	{
		if($this->getCardType() == '身份证'){
			if(isIdcard($this->getUserIdcard())) return true;	
			else return false;
		}
		return true;
	}
	
	function selectHistorys($showMax=0)
	{
		return sf::getModel("historys")->selectAll("project_id = '".$this->getUserId()."' ",'ORDER BY `updated_at` DESC',$showMax);	
	}
	
	/**
	 * 验证信息是否完整
	 */
	public function validate()
	{
		$message = array();
		
		if(!$this->getPersonname()) 			$message[] = '姓名必须填写；';
		if(!$this->getCorporationId()) 			$message[] = '申报单位必须填写；';
		if(!$this->getUserSex()) 				$message[] = '性别必须填写；';
		if(!$this->getUserBirthday()) 			$message[] = '出生日期必须填写；';
		if(!$this->getVerify()) 				$message[] = '身份证号码必须正确填写；';
		if(!isMobile($this->getUserMobile())) 	$message[] = '手机必须正确填写；';
		if(!$this->getUserEmail()) 				$message[] = '电子邮件必须填写；';
		if(!$this->getTitleType()) 				$message[] = '职称必须填写；';
		if($this->getIsLock() == 4) 			$message[] = '已被列入黑名单；';
//		 if(!$this->getAttachments()->getTotal()) 	$message[] = '请在【个人中心】=》【基本信息】=》【附件上传】一栏上传必要的附件！';
		return $message;
	}
	
	/**
	 * 发送短消息给用户
	 */
	public function sendMessage($message='',$item_id='',$item_type='projects',$send_at='')
	{
		if(!isMobile($this->getUserMobile())) return false;
		return sf::getModel("ShortMessages")->sendSms($this->getUserMobile(),$message,$item_id,$item_type,$send_at);
	}
	
	/**
	 * 查找相识的用户
	 */
	public function selectSameUser()
	{
		return $this->selectAll("user_id != '".parent::getUserId()."' AND (personname = '".parent::getPersonname()."' OR user_idcard = '".parent::getUserIdcard()."')","ORDER BY id ASC");	
	}
	
	/**
	 * 查找可以申报的项目类型
	 */
	public function selectTypeList($all=false)
	{
		$addWhere = "is_show = '1' AND end_at > '".date("Y-m-d H:i:s")."' AND start_at < '".date("Y-m-d H:i:s")."' ";
		//显示自己可以申报的申报书
		$sql = "SELECT type_id FROM types_switch WHERE allow_grade LIKE '%".$this->getUserGrade(false)."%' ";
		//如果有未结题的项目或者已经申报项目就只能申报不限制的申报书
		if($this->hasProject('COMPLETE') || $this->hasProject('DECLARER',config::get('current_declare_year',date("Y")))) 
			$sql .= " AND no_limit = 1 ";
		//增加子条件
		$addWhere .= " AND id IN ($sql) ";
		//如果没有认证将不显示
		if($this->getIsLock()) $addWhere .= "AND id = 0 ";//永远不成立的条件
		
		return sf::getModel("types")->selectAll($addWhere,'ORDER BY cat_id ASC,end_at ASC');	
	}
	
	function getUserGrade($isHtml=true)
	{
		if(!$isHtml) return parent::getUserGrade();
		switch(parent::getUserGrade())
		{
			case 'A':
			return '<SUP style="color:#F00;">'.parent::getUserGrade().'</SUP>';
			break;
			case 'B':
			return '<SUP style="color:#F66;">'.parent::getUserGrade().'</SUP>';
			break;
			case 'C':
			return 
			'<SUP style="color:#69F;">'.parent::getUserGrade().'</SUP>';
			break;
			default:
			return '<SUP style="color:#0C6;">'.parent::getUserGrade().'</SUP>';
			break;
		}
	}
	
	function getRepeatCount()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM ".$this->table." WHERE trim(user_idcard) = '".$this->getUserIdcard()."' ");
		return $row['num'];		
	}
	
	/*function getPersonname()
	{
		$user_name = parent::getPersonname();
		if(substr(parent::getUserId(),0,8) == 'MZGCXMGL') $user_name .= '<em>(苗)</em>';
		return $user_name;
	}*/
	
	function getMark()
	{
		$html ='';
		return $html;
	}

	function getLockMark()
	{
	    return;
		$html = "";
		if($this->hasLock()){
			$html .='<a title="点击开启个人资料修改权限" class = "green" onclick="return confirm(\'确定要开启个人资料修改权限吗？\')" href="'.site_url('admin/declarer/open/id/'.$this->getUserId()).'">[解] </a>';
		}
		return $html;	
	}
	function getUserName($len=0,$is_verify=false)
	{
		if(parent::getPersonname()) return parent::getPersonname();
		else{
			$user = $this->getUser(true);
			if($user->isNew()) return '';
			else return $user->getUserUsername();
		}
	}
	
	function _getUserIdCard()//取得专家身份证
	{
		if(parent::getUserIdcard()) return parent::getUserIdcard();
		else{
			$user = $this->getUser(true);
			if($user->isNew()) return '';
			else return $user->getUserIdCard();
		}
	}

	/**
	 * 是否锁定
	 */
	function hasLock()
	{
		if(in_array($this->getIsLock(),[1,4,5,6,7])) return true;
		return false;
	}
	
	/**
	 * 根据权限定义操作菜单
	 */
	function getCustomButton($type='show')
	{
		$htmlStr  = '';
		$htmlStr .= Button::back();
		$htmlStr .= Button::setName('查看记录')->setUrl(site_url("admin/history/index/type/declarers/id/".$this->getUserId()))->setWidth('550px')->setHeight('80%')->setIcon('time')->window();
		switch(input::getInput("session.userlevel"))
		{
			case 1:
			if($this->getIsLock() != 0)
				$htmlStr .= Button::window("认证",site_url("admin/declarer/dosubmit/userid/".$this->getUserId()),"doit");
			$htmlStr .= Button::window("退回",site_url("admin/declarer/doBack/userid/".$this->getUserId()),"delete");
			$htmlStr .= Button::window("账号信息",site_url("admin/declarer/users/userid/".$this->getUserId()),"list");
			$htmlStr .= Button::window("发送短消息",site_url("message/short/edit/item_type/declarer/item_id/".$this->getUserId().'/mobile/'.$this->getUserMobile()),"send",500,360);
            $htmlStr .= Button::setUrl(site_url("admin/declarer/more/userid/".$this->getUserId()))->setClass('btn-alt-warning')->window('用户合并');
			$htmlStr .= Button::window("编辑用户",site_url("admin/declarer/edit/userid/".$this->getUserId()),"edit");
            $htmlStr .= Button::setUrl(site_url("admin/declarer/doBlack/userid/".$this->getUserId()))->setClass('btn-alt-danger')->window('打入黑名单');
            $htmlStr .= Button::setUrl(site_url("admin/declarer/doDelete/userid/".$this->getUserId()))->setClass('btn-alt-danger')->window('删除角色');
			break;
			case 2:
			if($this->getUserId() == input::getInput("session.roleuserid")){
			    if($this->getIsLock()==0){
                    $htmlStr .= Button::setIcon('edit')->setEvent("return showConfirm('修改后要重新认证，确定要修改资料吗？','".site_url("user/profile/base/unlock/yes/userid/".$this->getUserId())."')")->button("修改资料");
                }
				if(!in_array($this->getIsLock(),[0,1,4,6,7]))
					$htmlStr .= Button::link("申请认证",site_url("user/profile/submit/userid/".$this->getUserId()),"submit");
			}
			break;
			case 3:
			if($this->getCorporationId() == input::getInput("session.roleuserid")){
				// if(in_array($this->getIsLock(),[1,2]))
				// 	$htmlStr .= Button::window("认证",site_url("unit/declarer/doTalent/userid/".$this->getUserId()),"submit");
				// $htmlStr .= Button::window("退回",site_url("unit/declarer/doBack/userid/".$this->getUserId()),"delete",500,360,'btn btn-danger');
				$htmlStr .= Button::setUrl(site_url("unit/declarer/users/userid/".$this->getUserId()))->setIcon('fa fa-exclamation-triangle')->window("重置密码");
			}
			break;
			case 6:
                $htmlStr .= Button::setUrl(site_url("admin/declarer/users/userid/".$this->getUserId()))->setIcon('fa fa-user')->window("账号信息");
                $htmlStr .= Button::window("发送短消息",site_url("message/short/edit/item_type/declarer/item_id/".$this->getUserId().'/mobile/'.$this->getUserMobile()),"send");
                $htmlStr .= Button::window('兼职处理',site_url("admin/declarer/multiple/userid/".$this->getUserId()),'doit');
			break;
			default:
			break;	
		}
		return $htmlStr;
	}
	
	function toBlack()
	{
		$this->setIsLock(4);//设置为黑名单
		//设置账号不可登录
		$users = $this->users();
		while($user = $users->getObject()){
			$user->setIsLock(4);
			$user->save();
		}
		return $this->save();	
	}
	
	function getCorporationName()
	{
		return sf::getModel("corporations")->selectByUserId(parent::getCorporationId())->getSubject();
	}
		/**
	 * 科技项目数
	 */
		function getProjectNum()
		{
			$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM user_techplans WHERE user_id = '".$this->getUserId()."' ");
			return $row['num'];
		}
		/**
	 * 科技奖励数
	 */
		function getAwardNum()
		{
			$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM user_awards WHERE user_id = '".$this->getUserId()."' ");
			return $row['num'];
		}
		/**
	 * 专利数
	 */
		function getUserPatentNum()
		{
			$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM user_patents WHERE user_id = '".$this->getUserId()."' ");
			return $row['num'];
		}
		/**
	 * 论文著作数
	 */
		function getUserPaperNum()
		{
			$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM user_papers WHERE user_id = '".$this->getUserId()."' ");
			return $row['num'];
		}
	/**
	 * 创新人才
	 */
	function getTalen()
	{
	    return;
		switch (parent::getTalent()) {
			case '1':
			return '创新人才';
			break;
			case '2':
			return '核心创新人才';
			break;
			case '3':
			return '科协会员';
			break;
			default:
			return '';
			break;
		};
	}
	/**
	 * 人才标记
	 */
	function getTalentStr()
	{
	    return;
		$talent = parent::getTalent();
		$str = '';
		if($talent < 1){
			return $str;
		}
		if($talent == 3){
			$str .= '<span class="badge badge-success">科协会员</span>';
			if(parent::getTalentState() == 29){
				$str .= '<span class="badge badge-info">创新人才</span>';
			}
		}else{
			$str .= '<span class="badge badge-info">创新人才</span>';
		}
		return $str;
	}
	public function setJobQualification($v)
	{
		if(!isset($v)) return $this;
		$v = implode(',',$v);
		parent::setJobQualification($v);

	}
	public function getJobQualification($v='')
	{
		return explode(',',parent::getJobQualification($v));
	}
	public function setUserAcademichonor($v)
	{
		if(!isset($v)) return $this;
		$v = implode(',',$v);
		parent::setUserAcademichonor($v);

	}
	public function getUserAcademichonor($v='')
	{
		return explode(',',parent::getUserAcademichonor($v));
	}
	function getScore($onlyNumber=false)
	{
		if(in_array(input::getInput("session.userlevel"),array('2')))
		{
			if(parent::getScore() == '') return '还未评审';
			return '';
		}
		if($onlyNumber) return parent::getScore();
		if(parent::getScore() != ''){
			return link_to("apply/assess/showTalent/id/".parent::getUserId(),parent::getScore(),array('title'=>'查看评分详细情况'));
		}else if($this->getIsLock() == 18){
			return link_to("apply/assess/showTalent/id/".parent::getUserId(),'等待评审',array('title'=>'查看专家分配情况'));		
		}
		else{
			return '<s style="color:red">还未评审</s>';
		}
	}
	/**
	 * 取得评审信息
	 */
	function selectAssess()
	{
		return sf::getModel("ProjectTalentGrade")->selectAll("project_id = '".parent::getUserId()."' AND type='project' ","ORDER BY score DESC");
	}

	function getScor()
	{
		return parent::getScore();
	}
	function getScoreByItem($num=1){
		$grades = sf::getModel("ProjectTalentGrade")->selectAll("project_id = '".parent::getUserId()."' order by score desc")->toArray();
		if(count($grades)<1){
			return '暂未评审';
		}
		if(count($grades) > 5){
			array_shift($grades);
			array_pop($grades);
		}
		$result = 0;
		foreach ($grades as $key => $value){
			$mark = explode('$',$value['mark']);
			$result += $mark[$num-1];
		}
		$score = $result/count($grades);
		$rule = sf::getModel("Rules",4);
		$total = $rule->getMark()[$num-1];
		return round($score*$total/$rule->getAccuracy(),2);
	}
	/*
	获取项目的评分情况
	*/
	function getGrade()
	{
		return sf::getModel('ProjectTalentGrade')->selectAll(" project_id = '".parent::getUserId()."'");
	} 
	/*
	获取短信次数
	*/
	function countMessage()
	{
		$log = sf::getModel("LogGetpwds");
		$num1 = $log->getCount(parent::getUserId());
		$num2 = 0;
		$lognums = sf::getModel("NumberGetpwds")->selectAll("user_id = '".parent::getUserId()."'");
		while ($lognum = $lognums->getObject()) {
			$num2 += $lognum->getNumber();
		}
		return $num1+$num2;
	}

    /**
     * 取得所有工作单位
     */
    function multiples($withFirstCompany = false)
    {
        if(!$withFirstCompany && $this->getIsMultiple() < 1) return false;
        $addWhere = "user_id in (select company_id from declarer_multiples where user_id = '".$this->getUserId()."') ";
        if($withFirstCompany) $addWhere .= " OR user_id = '".$this->getCorporationId()."' ";
        return sf::getModel("Corporations")->selectAll($addWhere);
    }

    /**
     * 取得兼职单位
     */
    function getMultiplesCompany()
    {
        $addWhere = "user_id in (select company_id from declarer_multiples where user_id = '".$this->getUserId()."') ";
        $companys =  sf::getModel("Corporations")->selectAll($addWhere);
        $datas = [];
        while($company = $companys->getObject()){
            $datas[] = $company->getSubject();
        }
        return implode('、',$datas);
    }

    /**
     * 是否有兼职单位
     */
    function getIsMultiple()
    {
        return sf::getModel("DeclarerMultiples")->selectAll("user_id = '".$this->getUserId()."'")->getTotal()>0;
    }

    /**
     * 取得兼职单位Id
     */
    function getMultipleId()
    {
        $addWhere = "user_id in (select company_id from declarer_multiples where user_id = '".$this->getUserId()."') ";
        $companys =  sf::getModel("Corporations")->selectAll($addWhere);
        if($companys->getTotal()==0){
            return false;
        }
        $company = $companys->getObject();
        return $company->getUserId();
    }

    function getAllProjects()
    {
        //主持的项目
        $projects['own'] = $this->selectProject();
        //参与的项目
        $projects['join'] = $this->selectJoinProject();
        return $projects;
    }

    function getRewards()
    {
        return sf::getModel('Rewards')->selectAll("reward_id in (select item_id from result_members where item_type = 'reward' and user_id = '".$this->getUserId()."')");
    }

    function getMethods()
    {
        return sf::getModel('Methods')->selectAll("method_id in (select item_id from result_members where item_type = 'method' and user_id = '".$this->getUserId()."')");
    }

    function getPatents()
    {
        return sf::getModel('Patents')->selectAll("patent_id in (select item_id from result_members where item_type = 'patent' and user_id = '".$this->getUserId()."')");
    }

    function getSofts()
    {
        return sf::getModel('Softs')->selectAll("soft_id in (select item_id from result_members where item_type = 'soft' and user_id = '".$this->getUserId()."')");
    }

    function getPapers()
    {
        return sf::getModel('Papers')->selectAll("paper_id in (select item_id from result_members where item_type = 'paper' and user_id = '".$this->getUserId()."')");
    }

    function getWorks()
    {
        return sf::getModel('Works')->selectAll("work_id in (select item_id from result_members where item_type = 'work' and user_id = '".$this->getUserId()."')");
    }

    /**
     * 设置是否有兼职情况
     */
    function setIsMultiple($v)
    {
        if($v < 0) $v = 0;
        return parent::setIsMultiple($v);
    }

    function addMultiple($company_id)
    {
        if($this->isNew()) return false;

        $data = array();
        $data['user_id'] = $this->getUserId();
        $data['company_id'] = $company_id;
        $db = sf::getLib("db");
        if($db->insert($data,'declarer_multiples')){
            $this->setIsMultiple($this->getIsMultiple() + 1);
            return  $this->save();
        }else return false;
    }

    function deleteMultiple($company_id)
    {
        if($this->isNew()) return false;
        $db = sf::getLib("db");
        $db->query("DELETE FROM `declarer_multiples` WHERE user_id = '".$this->getUserId()."' and company_id = '".$company_id."' ");
        if($db->affected_rows()){
            $this->setIsMultiple($this->getIsMultiple() - 1);
            return $this->save();
        }else return false;
    }

    function getUserHonor()
    {
        return parent::getTitleType();
    }

    function getSubject()
    {
        return $this->getFullName();
    }
}
?>