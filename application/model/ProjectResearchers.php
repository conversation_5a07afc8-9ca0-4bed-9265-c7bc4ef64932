<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseProjectResearchers;

class ProjectResearchers extends BaseProjectResearchers
{
	function selectByProjectId($project_id='',$type='apply'){
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `project_id` = '".$project_id."' and `type` = '{$type}'");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else{
            $this->setProjectId($project_id?$project_id:sf::getLib("MyString")->getRandString());
            $this->setType($type);
        }
        return $this;
    }

    function researcher(){
        return sf::getModel('Projects')->selectByProjectId(parent::getProjectId())->user();
    }

    function getSubject($len=0){
        $v = parent::getSubject();
        if(!$v) return $this->researcher()->getPersonname();
        else return $v;
    }

    function getSex($len=0){
        $v = parent::getSex();
        if(!$v) return $this->researcher()->getUserSex();
        else return $v;
    }

    function getBirthday($len=0){
        $v = parent::getBirthday();
        if(!$v) return $this->researcher()->getUserBirthday();
        else return $v;
    }

    function getPhone($len=0){
        $v = parent::getPhone();
        if(!$v) return $this->researcher()->getUserPhone();
        else return $v;
    }

    function getMobile($len=0){
        $v = parent::getMobile();
        if(!$v) return $this->researcher()->getUserMobile();
        else return $v;
    }

    function getEmail($len=0){
        $v = parent::getEmail();
        if(!$v) return $this->researcher()->getUserEmail();
        else return $v;
    }

    function getAddress($len=0){
        $v = parent::getAddress();
        if(!$v) return $this->researcher()->getUserHomeAddress();
        else return $v;
    }

    function getDegree($len=0){
        $v = parent::getDegree();
        if(!$v) return $this->researcher()->getUserDegree();
        else return $v;
    }

    function getAdministrativePost($len=0){
        $v = parent::getAdministrativePost();
        if(!$v) return $this->researcher()->getUserOccupation();
        else return $v;
    }

    function getTitle($len=0){
        $v = parent::getTitle();
        if(!$v) return $this->researcher()->getUserHonor();
        else return $v;
    }

    function getProfessional($len=0){
        $v = parent::getProfessional();
        if(!$v) return $this->researcher()->getUserWork();
        else return $v;
    }

    function setInstructor($v){
        if(!is_array($v)) $v=[];
        return parent::setInstructor(json_encode($v,JSON_UNESCAPED_UNICODE));
    }

    function getInstructor($key=''){
        $v = parent::getInstructor();
        $arr = json_decode($v,true);

        if($key) return $arr[$key];
        else return $arr;
    }

    function setWorkExperience($v){
        if(!is_array($v)) $v=[];
        return parent::setWorkExperience(json_encode($v,JSON_UNESCAPED_UNICODE));
    }

    function getWorkExperience($key=''){
        $v = parent::getWorkExperience();
        $arr = json_decode($v,true);

        if($key) return $arr[$key];
        else return $arr;
    }

    function setLearningExperience($v){
        if(!is_array($v)) $v=[];
        return parent::setLearningExperience(json_encode($v,JSON_UNESCAPED_UNICODE));
    }

    function getLearningExperience($key=''){
        $v = parent::getLearningExperience();
        $arr = json_decode($v,true);

        if($key) return $arr[$key];
        else return $arr;
    }
	
	function setMore($v){
        if(!is_array($v)) $v=[];
        return parent::setMore(json_encode($v,JSON_UNESCAPED_UNICODE));
    }

    function getMore($key=''){
        $v = parent::getMore();
        $arr = json_decode($v,true);

        if($key) return $arr[$key];
        else return $arr;
    }

    public function copyToTask($projectId)
    {
        $researcher = sf::getModel('ProjectResearchers')->selectByProjectId($projectId,'apply');
        $db = sf::getLib("db");
        $row = $db->fetch_first("select * from `".$this->table."` where id = '".$researcher->getId()."'");
        if($row){
            unset($row['id']);
            $row['type'] = 'task';
            $row['age'] = (int)$row['age'];
            $db->insert($row,$this->table);
        }
    }
}