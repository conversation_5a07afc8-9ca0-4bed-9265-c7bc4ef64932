<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BasePlatformChanges;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class PlatformChanges extends BasePlatformChanges
{
    private $platform = NULL;
    private $corporation = NULL;
    private $map = array('subject'=>'平台名称变更','principal_name'=>'平台负责人变更','corporation_name'=>'依托单位名称变更','corporation_id'=>'依托单位主体变更','department_id'=>'主管部门变更','cooperation'=>'共建单位变更','other'=>'其他');


    function selectByChangeId($changeId = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `change_id` = '{$changeId}' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setChangeId($changeId ?: sf::getLib("MyString")->getRandString());
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    function getCorporation($f=false)
    {
        if($this->corporation === NULL || $f) $this->corporation = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
        return $this->corporation;
    }

    function getMap($k='')
    {
        if($k) return $this->map[$k];
        else return $this->map;
    }

    function platform($f=false)
    {
        if($this->platform === NULL || $f) $this->platform = sf::getModel("Platforms")->selectByPlatformId($this->getPlatformId());
        return $this->platform;
    }

    function attachments()
    {
        return sf::getModel("filemanager")->selectAll("item_id = '".$this->getId()."' AND item_type ='change' ","ORDER BY created_at ASC");
    }

    function historys()
    {
        return sf::getModel("ChangeHistorys")->selectAll("project_id = '".$this->getId()."'","ORDER BY updated_at DESC");
    }

    function addHistory($content='',$is_hiden=0,$file_path='')
    {
        if($this->isNew()) return false;
        return sf::getModel("ChangeHistorys")->addHistory($this->getId(),$content,'project',$is_hiden,$file_path);
    }

    function getSummaryHtml()
    {
        return nl2br(htmlspecialchars($this->getSummary()));
    }

    function mark()
    {
        return '<i class="glyphicon glyphicon-search" onclick="return showWindow(\'审批记录\',\''.site_url("timeline/change/id/".$this->getId()).'\');"></i>';
    }

    function getBadge()
    {
        if($this->getIsShift() == 2) return '<span class="text-danger">[转]</span>';
        else return '';
    }

    function setChangeType($v)
    {
        if(is_array($v)) $v=implode('$',$v);
        return parent::setChangeType($v);
    }

    function getChangeType()
    {
        return explode('$',parent::getChangeType());
    }

    function getChangeTypeName($dv='、')
    {
        $name = array();
        $type = $this->getChangeType();
        for($i=0,$n=count($type);$i<$n;$i++){
            $name[] = $this->getMap($type[$i]);
        }
        return implode($dv,$name);
    }

    function getChangeTypeNameArr()
    {
        $name = array();
        $type = $this->getChangeType();
        for($i=0,$n=count($type);$i<$n;$i++){
            $name[] = $this->getMap($type[$i]);
        }
        return $name;
    }

    function setTodo($v)
    {
        if(is_array($v)) $v = json_encode($v,JSON_UNESCAPED_UNICODE);
        return parent::setTodo($v);
    }

    function getTodo($k='')
    {
        $todo = json_decode(parent::getTodo(),true);
        if($k) return $todo[$k];
        else return $todo;
    }

    function setBefore($v)
    {
        if(is_array($v)) $v = json_encode($v,JSON_UNESCAPED_UNICODE);
        return parent::setBefore($v);
    }

    function getBefore($k='')
    {
        $before = json_decode(parent::getBefore(),true);
        if($k) return $before[$k];
        else return $before;
    }

    function setAfter($v)
    {
        if(is_array($v)) $v = json_encode($v,JSON_UNESCAPED_UNICODE);
        return parent::setAfter($v);
    }

    function getAfter($k='')
    {
        $after = json_decode(parent::getAfter(),true);
        if($k) return $after[$k];
        else return $after;
    }

    function setBeforeMoney($v)
    {
        if(is_array($v)) $v = json_encode($v,JSON_UNESCAPED_UNICODE);
        return parent::setBeforeMoney($v);
    }

    function getBeforeMoney($k='')
    {
        $before = json_decode(parent::getBeforeMoney(),true);
        if($k) return $before[$k];
        else return $before;
    }

    function setAfterMoney($v)
    {
        if(is_array($v)) $v = json_encode($v,JSON_UNESCAPED_UNICODE);
        return parent::setAfterMoney($v);
    }

    function getAfterMoney($k='')
    {
        $after = json_decode(parent::getAfterMoney(),true);
        if($k) return $after[$k];
        else return $after;
    }

    function setAfterTarget($v)
    {
        if(is_array($v)) $v = json_encode($v,JSON_UNESCAPED_UNICODE);
        return parent::setAfterTarget($v);
    }

    function getAfterTarget($k='')
    {
        $after = json_decode(parent::getAfterTarget(),true);
        if($k) return $after[$k];
        else return $after;
    }

    function setBeforeTarget($v)
    {
        if(is_array($v)) $v = json_encode($v,JSON_UNESCAPED_UNICODE);
        return parent::setBeforeTarget($v);
    }

    function getBeforeTarget($k='')
    {
        $after = json_decode(parent::getBeforeTarget(),true);
        if($k) return $after[$k];
        else return $after;
    }

    function getChangeContent($name='')
    {
        $htmlStr = '';
        $type = $this->getChangeType();
        $before = $this->getBefore();
        $after = $this->getAfter();
        for($i=0,$n=count($type);$i<$n;$i++)
        {
            if($name != '') $htmlStr .= '<input type="checkbox" name="'.$name.'[]" value="'.$type[$i].'" />';
            if($type[$i]=='department_id') {
                $htmlStr .= ($i + 1) . '）' .$this->getMap($type[$i]).': “'.$before['department_name'].'” 变更为 “'.$after['department_name']."”<br />";
            }elseif(in_array($type[$i],['stop','task'])) {
                $htmlStr .= ($i + 1) . '）' .$this->getMap($type[$i])."<br />";
            }else{
                $htmlStr .= ($i + 1) . '）' .$this->getMap($type[$i]).': “'.$before[$type[$i]].'” 变更为 “'.$after[$type[$i]]."”<br />";
            }

        }
        return $htmlStr;
    }

    function getChangeBefore()
    {
        $htmlStr = '';
        $type = $this->getChangeType();
        $before = $this->getBefore();
        for($i=0,$n=count($type);$i<$n;$i++)
        {
            $htmlStr .= ($n>1 ? ('（'.($i + 1) . '）' .$this->getMap($type[$i]).': ') : '').$before[$type[$i]].'<br />';
        }
        return $htmlStr;
    }

    function getChangeAfter()
    {
        $htmlStr = '';
        $type = $this->getChangeType();
        $after = $this->getAfter();
        for($i=0,$n=count($type);$i<$n;$i++)
        {
            $htmlStr .= ($n>1 ? ('（'.($i + 1) . '）' .$this->getMap($type[$i]).': ') : '').$after[$type[$i]].'<br />';
        }
        return $htmlStr;
    }

    /**
     * 验证信息是否完整
     */
    public function validate()
    {
        $message = array();
        $type   = $this->getChangeType();
        $before = $this->getBefore();
        $after  = $this->getAfter();
        if(count($type)<1) $message[] = '变更类型必须选择';
        for($i=0,$n=count($type);$i<$n;$i++)
        {
            if(in_array($type[$i],['stop','task'])) continue;
            if(!$before[$type[$i]]) $message[] = '“'.$this->getMap($type[$i]).'” 变更前的内容不能为空';
            if(!$after[$type[$i]])  $message[] = '“'.$this->getMap($type[$i]).'” 变更后的内容不能为空';
        }
        if(!$this->getLinkman()) $message[] = '联系人不能为空';
        if(!$this->getLinkmanMobile()) $message[] = '联系人手机不能为空';
        if(in_array('total_money',$this->getChangeType()) && !$this->getAfterMoney()) $message[] = '请填写经费变更明细';
        if(in_array('total_money',$this->getChangeType())){
            if($this->getAfterMoney('total_money')!=$this->getAfterMoney('total_hj')){
                $message[] = '经费变更明细中的项目总经费应等于支出合计';
            }
            if($this->getAfter('total_money')!=$this->getAfterMoney('total_money')){
                $message[] = '变更申请表中的项目经费应等于经费变更明细中的项目总经费';
            }
            if($this->getAfter('total_money')==$this->getBefore('total_money')){
                $message[] = '变更后的经费不能与变更前的经费相同';
            }
        }
        if(in_array('target',$this->getChangeType()) && !$this->getAfterTarget()) $message[] = '请填写阶段目标变更明细';
        //项目验证
        $platform = $this->platform(true);
        if($platform->isNew()) $message[] = '对应的平台不存在';
        if($platform->getStatement() !=0) $message[] = '对应的平台处于不能变更的状态';
        return $message;
    }

    function setShortUrl($hash='')
    {
        $shortUrl = sf::getModel("ShortUrls")->selectByHash($hash);
        if($shortUrl->isNew()){
            $shortUrl->setType('change');
            $shortUrl->setProjectId($this->getId());
            $shortUrl->setUserId(input::getInput("session.roleuserid"));
            $shortUrl->setUserName(input::getInput("session.nickname"));
            $shortUrl->setIsValid(1);
            $shortUrl->setUpdatedAt(date("Y-m-d H:i:s"));
            $shortUrl->setUserIp(input::getIp());
            return $shortUrl->save();
        }
        return false;
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'project_change', $limit = 0, $orders='order by no asc,id asc')
    {
        $addwhere = "`item_id` = '".$this->getChangeId()."'";
        if ($itemType != 'all') {
            $addwhere .= " and `item_type` = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }
    /**
     * 执行项目变更
     * @return string 返回执行结果
     */
    function todo($op=array())
    {
        $_msg = $todos = array();
        if($this->isNew()) return false;
        if($todos = $this->getTodo()) return $todos;//不能重复执行
        $project = $this->project(true);
        if($project->isNew()) return false;
        $types  = $this->getChangeType();
        $before = $this->getBefore();
        $after  = $this->getAfter();
        for($i=0,$n=count($types);$i<$n;$i++)
        {
            $type = trim($types[$i]);
            if(!in_array($type,$op)){
                $todos[$type] = array('result'=>false,'msg'=>"未选中更新！");
                continue;
            }
            switch($type)
            {
                case 'user_name'://负责人变更
                    if($after['user_id'])
                    {
                        if($before['user_id'] == $project->getUserId())
                        {
                            $project->setUserId($after['user_id']);
                            $project->setUserName($after['user_name']);
                            $__msg = '根据变更申请将项目负责人由'.$before['user_name'].'变更为'.$after['user_name'];
                            $_msg[] = $__msg;
                            $todos['user_name'] = array('result'=>true,'msg'=>$__msg);
                        }else{
                            $todos['user_name'] = array('result'=>false,'msg'=>"项目负责人变更：变更前参数检测不一致，请核实后手动变更");
                        }
                    }else{
                        $todos['user_name'] = array('result'=>false,'msg'=>"项目负责人变更：参数不足，请手动变更");
                    }
                    break;
                case 'corporation_name'://承担单位名称变更
                    if($after['corporation_name'])
                    {
                        if($before['corporation_name'] == $project->getCorporationName())
                        {
                            $project->setCorporationName($after['corporation_name']);
                            $__msg = '根据变更申请将承担单位名称由'.$before['corporation_name'].'变更为'.$after['corporation_name'];
                            $_msg[] = $__msg;
                            $todos['corporation_name'] = array('result'=>true,'msg'=>$__msg);
                        }else{
                            $todos['corporation_name'] = array('result'=>false,'msg'=>"承担单位名称变更：变更前参数检测不一致，请核实后手动变更");
                        }
                    }else{
                        $todos['corporation_name'] = array('result'=>false,'msg'=>"承担单位名称变更：参数不足，请手动变更");
                    }
                    break;
                case 'department_id'://主管部门变更
                    if($after['department_id'])
                    {
                        if($before['department_id'] == $project->getDepartmentId())
                        {
                            $project->setDepartmentId($after['department_id']);
                            $project->setDepartmentName($after['department_name']);
                            $__msg = '根据变更申请将主管部门由'.$before['department_name'].'变更为'.$after['department_name'];
                            $_msg[] = $__msg;
                            $todos['department_id'] = array('result'=>true,'msg'=>$__msg);
                        }else{
                            $todos['department_id'] = array('result'=>false,'msg'=>"主管部门变更：变更前参数检测不一致，请核实后手动变更");
                        }
                    }else{
                        $todos['department_id'] = array('result'=>false,'msg'=>"主管部门变更：参数不足，请手动变更");
                    }
                    break;
                case 'real_end_at':
                    if($after['real_end_at']){
                        if($before['real_end_at'] == $project->getRealEndAt())
                        {
                            $project->setIsDelay(1);//设置为延期项目
                            $project->setRealEndAt($after['real_end_at']);
                            $__msg = '根据变更申请将项目截止时间由'.$before['real_end_at'].'变更为'.$after['real_end_at'];
                            $_msg[] = $__msg;
                            $todos['real_end_at'] = array('result'=>true,'msg'=>$__msg);
                        }else{
                            $todos['real_end_at'] = array('result'=>false,'msg'=>"执行期变更：更新前时间与系统不一致，请核实后手动变更");
                        }
                    }else{
                        $todos['real_end_at'] = array('result'=>false,'msg'=>"执行期变更：变更参数缺失，请核实后手动变更");
                    }
                    break;
                case 'stop':
                    $project->setStatement(40);//设置为已终止
                    $project->setStopAt(date('Y-m-d'));
                    $project->save();
                    $todos['stop'] = array('result'=>true,'msg'=>'项目已终止');
                    break;
                case 'cooperation':
                    if($after['cooperation']){
                        if($before['cooperation'] == $project->getCooperation())
                        {
                            $project->setCooperation($after['cooperation']);
                            $__msg = '根据变更申请将项目合作单位由'.$before['cooperation'].'变更为'.$after['cooperation'];
                            $_msg[] = $__msg;
                            $todos['cooperation'] = array('result'=>true,'msg'=>$__msg);
                        }else{
                            $todos['cooperation'] = array('result'=>false,'msg'=>"合作单位变更：变更前参数与系统不一致，请核实后手动变更");
                        }
                    }else{
                        $todos['cooperation'] = array('result'=>false,'msg'=>"合作单位变更：变更参数缺失，请核实后手动变更");
                    }
                    break;
                default:
                    break;
            }
        }
        if(count($_msg)){//执行变更并记录
            $project->save();
            sf::getModel("Historys")->addHistory($project->getProjectId(),implode(';',$_msg),'projects');
            sf::getModel("Historys")->addHistory($this->getChangeId(),implode(';',$_msg),'project_change');
        }
        //保存记录
        $this->setTodo($todos);
        $this->setStateForTodo(10);
        $this->save();
        return $todos;
    }

    function getState()
    {
        switch($this->getStatement())
        {
            case 1:
                return '填写中';
            case 2:
                return '待依托单位审核';
            case 3:
                return '<span style="color:red">依托单位退回</span>';
            case 4:
                return '待主管部门审核';
            case 5:
                return '<span style="color:red">主管部门退回</span>';
            case 9:
                return '待科技厅审核';
            case 10:
                return '同意变更';
            case 12:
                return '<span style="color:red">科技厅退回</span>';
            default:
                return '状态未知';
        }
    }

    /**
     * 获取上一级待审核的单位id
     * @return false|string
     */
    function getWaitForCompanyId()
    {
        $level = $this->getWaitForCompany();
        switch ($level){
            case 1:
                return $this->getFirstId();
            case 2:
                return $this->getSecondId();
            case 3:
                return $this->getThirdId();
        }
        return false;
    }
}