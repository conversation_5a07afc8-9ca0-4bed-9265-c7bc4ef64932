<?php
namespace App\Plugin;
class UEditor
{
	/**
	 * Name of the FCKeditor instance.
	 *
	 * @access protected
	 * @var string
	 */
	var $InstanceName ;
	/**
	 * Path to FCKeditor relative to the document root.
	 *
	 * @var string
	 */
	var $BasePath ;
	/**
	 * Width of the FCKeditor.
	 * Examples: 100%, 600
	 *
	 * @var mixed
	 */
	var $Width ;
	/**
	 * Height of the FCKeditor.
	 * Examples: 400, 50%
	 *
	 * @var mixed
	 */
	var $Height ;
	/**
	 * Name of the toolbar to load.
	 *
	 * @var string
	 */
	var $ToolbarSet ;
	/**
	 * Initial value.
	 *
	 * @var string
	 */
	var $Value ;
	/**
	 * This is where additional configuration can be passed.
	 * Example:
	 * $oFCKeditor->Config['EnterMode'] = 'br';
	 *
	 * @var array
	 */
	var $Config ;

	/**
	 * Main Constructor.
	 * Refer to the _samples/php directory for examples.
	 *
	 * @param string $instanceName
	 */
    function UEditor( $instanceName = 'content')
    {
        $this->__construct( $instanceName ) ;
    }
	
	function __construct( $instanceName = 'content',$value='',$width='99%',$height='450')
    {
        $this->InstanceName = $instanceName ;
        $this->Width = $width ;
        $this->Height = $height ;
        $this->ToolbarSet = 'full' ;
        $this->setValue($value);
    }
	
	function setWidth($width="100%")
	{
		$this->Width = $width;
	}
	
	function setHeight($height="450")
	{
		$this->Height = $height;
	}
	
	function setToolBar($bar = 'Default')
	{
		$this->ToolbarSet = $bar;
	}
	
	function setName($name = 'content')
	{
		$this->InstanceName = $name;
	}
	
	function setValue($value = '')
	{
		$this->Value = stripslashes($value);
	}

	/**
	 * Display FCKeditor.
	 *
	 */
	function create()
	{
		return $this->CreateHtml();
	}

	/**
	 * Return the HTML code required to run FCKeditor.
	 *
	 * @return string
	 */
	function CreateHtml()
	{
		$html = '';

		$html = '<textarea name="'.$this->InstanceName.'" id="'.$this->InstanceName.'" class="editor " type="'.$this->ToolbarSet.'" height="'.$this->Height.'" width="">'.$this->Value.'</textarea>';

		$html .= '<div class="clearfix"></div>';
		return $html ;
	}
}
