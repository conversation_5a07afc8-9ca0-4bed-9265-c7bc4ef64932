<?php
namespace App\controller\Apply;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\lang;
/**
 * 项目的查看页面
 *
 */

class Month extends BaseController
{	
	
	function load()
	{
		$this->month = sf::getModel("Months")->selectByMonthId(input::getInput("mix.id"));
		
		if($this->month->isNew())
			$this->page_debug(lang::get("The project do not exist!"),getFromUrl());	
	}
	
	function index()
	{
		$this->show();
	}
	
	function show()
	{
        if($this->month->getConfigs("path.apply"))
			$this->jump(site_url($this->month->getConfigs("path.apply")."/show/id/".$this->month->getMonthId()));
        $this->error('该项目无具体内容信息',getFromUrl());
		$data['month'] = $this->month;
		$data['page'] = input::getInput("get.page") ? input::getInput("get.page") : 1;
		view::set($data);
		view::apply("inc_body","declare/".getFolder($data['project']->getTypeId())."/project/show");
		view::display("page");
	}
	
	/**
	 * 打印项目
	 */
	function output()
	{
		if(!in_array($_SESSION['userlevel'],array('1','2','3','4','5','6','7','8','11','14','16'))) $this->page_debug(lang::get("You do not have permission to do it!"),'javascript:window.close();');
		if($this->month->getConfigs("path.apply")) 
			$this->jump(site_url($this->month->getConfigs("path.apply")."/output/id/".$this->month->getMonthId()));

		if($this->month->getConfigs("file.project")) $this->download();//如果存在直接下载
		$data['month'] = $this->month;
		view::set($data);
		view::display("declare/".getFolder($data['project']->getTypeId())."/project/output");
	}
	
	/**
	 * 下载WORD文档
	 */
	function download()
	{
		if(!in_array($_SESSION['userlevel'],array('1','2','3','4','5','6','7','8','11','14','16'))) $this->page_debug(lang::get("You do not have permission to do it!"),'javascript:window.close();');
		if($this->month->getConfigs("path.apply")) 
			$this->jump(site_url($this->month->getConfigs("path.apply")."/download/id/".$this->month->getMonthId()));
		
		if($file_name = $this->month->getConfigs("file.project")){
			@header("Location:/up_files/".$file_name);
			exit;
		}else{
			header("Content-type:application");
			header("Content-Disposition: attachment; filename=".$this->month->getMonthId().'.doc');
			$data['start'] = date("Y",strtotime($this->month->getStartAt()));
			$data['end'] = date("Y",strtotime($this->month->getEndAt()));
			$data['month'] = $this->month;
			$data['download'] = 'yes';
			view::set($data);
			$output = view::getContent("declare/".getFolder($this->month->getTypeId())."/project/output");
			$output = str_replace('"/up_files/','"'.base_url().'up_files/',$output);
			exit($output);
		}
	}

    /**
     * 编辑项目
     */
    function edit()
    {
        if(!$this->month->isNew() && in_array($this->month->getStatement(),array('0','1','3','6','12','22','24')) && input::session('roleuserid') == $this->month->getUserId()){
            $_SESSION['declare']['month_id'] = $this->month->getMonthId();
            $_SESSION['declare']['type_id'] = $this->month->getTypeId();
            $this->month->setStatement(1);
            $this->month->save();
            if($this->month->getConfigs("path.apply")) $this->jump(site_url($this->month->getConfigs("path.apply")."/index/id/".$this->month->getMonthId()));
            else $this->jump(site_url("engine/monther/edit/id/".$this->month->getMonthId()));
        }else{
            unset($_SESSION['declare']['month_id']);
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
    }

}
?>