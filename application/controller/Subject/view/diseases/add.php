<div class="content">
    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">所属专科<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input class="form-control" type="text" disabled value="<?=$category->getSubject()?>">
                                <input type="hidden" name="id" value="<?=$category->getId()?>">
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">病种代码<em>*</em></label>
                        <div class="col-md-5 col-sm-5 col-xs-5">
                            <input type="text" class="form-control" name="code" value="<?=$disease->getCode()?>">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">病种名称<em>*</em></label>
                        <div class="col-md-5 col-sm-5 col-xs-5">
                            <div class="input-group">
                                <input name="subject" type="text" id="subject" required="required" value="<?=$disease->getSubject()?>" class="form-control"/>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-8 ml-auto">
                            <?=Button::setType('submit')->setIcon('save')->button('保存资料')?>
                        </div>
                    </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
