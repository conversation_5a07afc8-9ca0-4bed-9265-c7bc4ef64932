<?php
namespace App\Controller;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use App\Facades\PDF;

class M extends BaseController
{	
	public $auth = array('index','t','p','b','s','up');

	function index()
	{
		$hash = input::get("h");
		$shorturl = sf::getModel("ShortUrls")->selectByHash($hash);
		view::set("msg",$shorturl->getMessage());
		view::apply("inc_body","mobile/q/index");
		view::display("mobile/page");
	}
	
	function b()
	{
		$hash = substr(trim(input::getInput("mix.h")),0,20);
		if($hash){
			$shorturl = sf::getModel("ShortUrls")->selectByHash($hash);
			if($shorturl->isNew()) $msg = '<p class="no">版本不存在</p>';
			else $msg = '<p>该版本生成于：'.$shorturl->getUpdatedAt("Y-m-d H:i:s").'</p>';
			view::set("shorturl",$shorturl);
		}else $msg = '<p class="no">识别错误</p>';
		
		view::set("msg",$msg);
		view::display("q/v");
	}
	
	function s()
	{
		$hash = substr(trim(input::getInput("mix.h")),0,20);
		if($hash){
			$shorturl = sf::getModel("ShortUrls")->selectByHash($hash);
			if($shorturl->isNew()) $msg = '<p class="no">版本不存在</p>';
			else $msg = '<p>该版本生成于：'.$shorturl->getUpdatedAt("Y-m-d H:i:s").'</p>';
			view::set("shorturl",$shorturl);
		}else $msg = '<p class="no">无效</p>';
		
		view::set("msg",$msg);
		view::display("q/v");
	}
	
	function t()
	{
		$hash = substr(trim(input::getInput("mix.h")),0,20);
		if($hash){
			$shorturl = sf::getModel("ShortUrls")->selectByHash($hash);
			if($shorturl->isNew()) $msg = '<p class="no">版本不存在</p>';
			else $msg = '<p>该版本生成于：'.$shorturl->getUpdatedAt("Y-m-d H:i:s").'</p>';
			view::set("shorturl",$shorturl);
		}else $msg = '<p class="no">无效</p>';
		
		view::set("msg",$msg);
		view::display("q/v");
	}
	
	function p()
	{
		$hash = substr(trim(input::getInput("mix.h")),0,20);
		if($hash){
			$shorturl = sf::getModel("ShortUrls")->selectByHash($hash);
			if($shorturl->isNew()) $msg = '<p class="no">版本不存在</p>';
			else $msg = '<p>该版本生成于：'.$shorturl->getUpdatedAt("Y-m-d H:i:s").'</p>';
			view::set("shorturl",$shorturl);
		}else $msg = '<p class="no">无效</p>';
		
		view::set("msg",$msg);
		view::display("q/v");
	}

    function up()
    {
		$sign = input::mix('sign');
        if(!$sign){
            $this->page_debug('二维码无效');
        }
        $opt = myencrypt($sign,'D');
		parse_str($opt, $opts);
		session_write_close();
		session_id($opts['session_id']);
		session_start();
        view::set('item_id',$opts['item_id']?:input::session('roleuserid'));
        view::set('item_type',$opts['item_type']?:'mobile');
		view::set('table',$opts['table']?:'filemanager');
        view::display("q/up");
    }

}
?>