<style>
    #page-nav {
        -moz-border-radius: 6px 6px 6px 6px;
        border: 1px solid #DEDFE1;
        float: left;
        margin: 0 0 15px 15px;
        padding: 0 6px;
        width: 250px;
        line-height: 23px;
        position: fixed;
        top: 115px;
        left: 0;
        background: #fff;
    }
    #page-nav strong {
        border-bottom: 1px dashed #DDDDDD;
        display: block;
        line-height: 30px;
        padding: 0 4px;
    }
    #index-ul {
        margin: 0;
        padding-bottom: 10px;
        max-height: 400px;
        overflow: auto;
    }
    #index-ul .selected{
        color: #e04f1a!important;
    }
    #index-ul .selected{
        color: #e04f1a!important;
    }
    #index-ul ul{
        display: none;
    }
    #index-ul .selected > a{
        color: #e04f1a!important;
    }
    #index-ul .selected > ul{
        display: block !important;
    }
</style>
<div id="page-nav" style="display: none">
    <strong>申报书目录  <a id="extend" style="cursor: pointer;" onclick="$('#index-ul').toggle();if($('#index-ul').css('display')=='none'){$(this).text('[打开]')}else{$(this).text('[收缩]')}">[收缩]</a></strong>
    <ul id="index-ul"></ul>
</div>
<script>
    $(function (){
        var html = '';
        $("bookmark").each(function (){
            var content = $(this).attr('content');
            if(content!=undefined){
                html += '<li><i class="fa fa-caret-right text-primary mr-1"></i><a href="javascript:;" onclick="scrollToLocation(\''+content+'\')">'+content+'</a></li>';
            }
        });
        if(html){
            $("#page-nav").show();
            $("#index-ul").html(html);
        }

        var html = getBookMarkByPid(0);
        if(html){
            $("#index-ul").html(html);
        }
    });

    $(window).scroll(function () {
        //目录跟随
        var nav = $("#index-ul li"),
            length = nav.length-1,//获取目录个数
            item = new Array(),
            sTop = $(window).scrollTop();
        for (var i = 0; i <= length; i++) {
            var target =$("#index-ul li").eq(i).find("a").eq(0).text();
            var itemTop = $("bookmark[content='"+target+"']").offset().top;
            if (sTop >= (itemTop - 120)) {
                nav.eq(i).addClass("selected").siblings("li").removeClass("selected");
                nav.eq(i).siblings("li").find("li").each(function (){
                    $(this).removeClass("selected");
                });
            }
        }

    });



    function getBookMarkByPid(pid)
    {
        var html = pid > 0 ? '<ul>' : '';
        $("bookmark[data-pid='"+pid+"']").each(function (){
            var content = $(this).attr('content');
            var id = $(this).data('id');
            var level = parseInt($(this).data('level'));
            var styleStr = '';
            switch (level){
                case 2:
                    styleStr='style="text-indent: 2em"';
                    break;
                case 3:
                    styleStr='style="text-indent: 4em"';
                    break;
            }
            html+='<li '+styleStr+'><a href="javascript:;" onclick="scrollToLocation(\''+content+'\')">'+content+'</a>';
            html+=getBookMarkByPid(id);
            html+='</li>';
        });
        html += pid > 0 ? '</ul>' : '';
        return html;
    }

    function scrollToLocation(target) {
        var mainContainer = $('html');
        if(target!=undefined){
            var pos = $("bookmark[content='"+target+"']").offset().top-70
            mainContainer.scrollTop(pos);
        }
    }

    jQuery(function () { Dashmix.helpers('sparkline'); });
</script>