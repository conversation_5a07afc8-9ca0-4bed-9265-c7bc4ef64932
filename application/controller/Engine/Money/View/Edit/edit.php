<form id="validateForm" name="validateForm" method="post" class="form-horizontal" action="">
  <?php if($configs['jfly']):?>
  <table class="table table-bordered table-striped table-hover <?=input::getMix('area')=='jfly'?'':'d-none'?>">
    <tr>
      <th colspan="2" class="text-left"><?=$configs['langs']['jfly']?:'经费来源'?><span class="pull-right">（单位：万元）</span></th>
    </tr>
    <tr>
      <th class="text-center">科目名称</th>
      <th width="27%" class="text-center">经费</th>
    </tr>
    <?php if($configs['jfly']['gov_money']):?>
    <tr>
      <td><?=$configs['langs']['jfly.gov_money']?:'申请国家级、省部级等外部资助'?></td>
      <td align="center"><input name="gov_money" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getGovMoney()?:'0.00'?>" data-group="1" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfly']['declare_money']):?>
    <tr>
      <td><?=$configs['langs']['jfly.declare_money']?:'申请集团经费补助'?></td>
      <td align="center"><input name="declare_money" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getDeclareMoney()?:'0.00'?>" data-group="1" /></td>
    </tr>
    <?php endif;?>
      <?php if($configs['jfly']['own_money']):?>
          <tr>
              <td><?=$configs['langs']['jfly.own_money']?:'自筹经费'?></td>
              <td align="center"><input name="own_money" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getOwnMoney()?:'0.00'?>" data-group="1" /></td>
          </tr>
      <?php endif;?>
    <?php if($configs['jfly']['other_money']):?>
    <tr>
      <td><?=$configs['langs']['jfly.other_money']?:'其他经费'?></td>
      <td align="center"><input name="other_money" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getOtherMoney()?:'0.00'?>" data-group="1" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfly']['others']):?>
    <tr>
      <td><?=$configs['langs']['jfly.others']?:'其他财政经费'?></td>
      <td align="center"><input name="data[others]" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getData('others')?:'0.00'?>" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfly']['total_money']):?>
    <tr >
      <td>项目总经费</td>
      <td align="center"><input readonly name="total_money" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getTotalMoney()?:'0.00'?>" data-group="1" /></td>
    </tr>
    <?php endif;?>
  </table>
  <?php endif;?>
  <?php if($configs['jfzc']):?>
  <table class="table table-bordered table-striped table-hover table-valign-middle auto_count2 presetfields mb-5  <?=input::getMix('area')=='jfzc'?'':'d-none'?>">
    <tr>
      <th colspan="5" class="text-left"><?=$configs['langs']['jfzc']?:'经费支出'?><span class="pull-right">（单位：万元）</span></th>
    </tr>
    <tr>
      <th width="32%" class="text-center" >科目名称</th>
      <th width="17%" class="text-center">专项经费</th>
      <th width="17%" class="text-center">自筹经费</th>
      <th width="17%" class="text-center">其他资金</th>
      <th width="17%" class="text-center">合计</th>
    </tr>
    <?php if($configs['jfzc']['indirect']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.indirect']?:'（一）间接费用'?></td>
      <td align="center"><input  name="indirect[cz]" type="text" size="8" class="number form-control sum sum-zx float" value="<?=$project->money($type)->getIndirect('cz')?:'0.00'?>" data-group="2" /></td>
      <td align="center"><input name="indirect[zc]" type="text" size="8" class="number form-control sum sum-zc float" value="<?=$project->money($type)->getIndirect('zc')?:'0.00'?>" data-group="2" /></td>
      <td align="center"><input name="indirect[qt]" type="text" size="8" class="number form-control sum sum-qt float" value="<?=$project->money($type)->getIndirect('qt')?:'0.00'?>" data-group="2" /></td>
      <td align="center"><input readonly name="indirect[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getIndirect('hj')?:'0.00'?>" data-group="2" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['performance']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.performance']?:'  其中：绩效支出'?></td>
      <td align="center"><input name="performance[cz]" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getPerformance('cz')?:'0.00'?>" data-group="3" /></td>
      <td align="center"><input name="performance[zc]" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getPerformance('zc')?:'0.00'?>" data-group="3" /></td>
      <td align="center"><input name="performance[qt]" type="text" size="8" class="number form-control sum float" value="<?=$project->money($type)->getPerformance('qt')?:'0.00'?>" data-group="3" /></td>
      <td align="center"><input readonly name="performance[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getPerformance('hj')?:'0.00'?>" data-group="3" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['direct']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.direct']?:'（二）直接费用'?></td>
      <td align="center"><input readonly name="direct[cz]" type="text" size="8" class="number form-control sum-direct sum-zx sum-direct-zx-total float" value="<?=$project->money($type)->getDirect('cz')?:'0.00'?>" data-group="4" /></td>
      <td align="center"><input readonly name="direct[zc]" type="text" size="8" class="number form-control sum-direct sum-zc sum-direct-zc-total float" value="<?=$project->money($type)->getDirect('zc')?:'0.00'?>" data-group="4" /></td>
      <td align="center"><input readonly name="direct[qt]" type="text" size="8" class="number form-control sum-direct sum-qt sum-direct-qt-total float" value="<?=$project->money($type)->getDirect('qt')?:'0.00'?>" data-group="4" /></td>
      <td align="center"><input readonly name="direct[hj]" type="text" size="8" class="number form-control sum-direct-total float" value="<?=$project->money($type)->getDirect('hj')?:'0.00'?>" data-group="4" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['device']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.device']?:'设备费'?></td>
      <td align="center"><input readonly name="device[cz]" type="text" size="8" class="number form-control sum sum-device sum-direct-zx sum-zx-device-total float" value="<?=$project->money($type)->getDevice('cz')?:'0.00'?>" data-group="5" /></td>
      <td align="center"><input readonly name="device[zc]" type="text" size="8" class="number form-control sum sum-device sum-direct-zc sum-zc-device-total float" value="<?=$project->money($type)->getDevice('zc')?:'0.00'?>" data-group="5" /></td>
      <td align="center"><input readonly name="device[qt]" type="text" size="8" class="number form-control sum sum-device sum-direct-qt sum-qt-device-total float" value="<?=$project->money($type)->getDevice('qt')?:'0.00'?>" data-group="5" /></td>
      <td align="center"><input readonly name="device[hj]" type="text" size="8" class="number form-control sum-device-total float" value="<?=$project->money($type)->getDevice('hj')?:'0.00'?>" data-group="5" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['device1']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.device1']?:'（1）购置设备费'?></td>
      <td align="center"><input name="device1[cz]" type="text" size="8" class="number form-control sum sum-zx-device float" value="<?=$project->money($type)->getDevice1('cz')?:'0.00'?>" data-group="6" /></td>
      <td align="center"><input name="device1[zc]" type="text" size="8" class="number form-control sum sum-zc-device float" value="<?=$project->money($type)->getDevice1('zc')?:'0.00'?>" data-group="6" /></td>
      <td align="center"><input name="device1[qt]" type="text" size="8" class="number form-control sum sum-qt-device float" value="<?=$project->money($type)->getDevice1('qt')?:'0.00'?>" data-group="6" /></td>
      <td align="center"><input readonly name="device1[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getDevice1('hj')?:'0.00'?>" data-group="6" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['device2']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.device2']?:'（2）设备试制、改造、租赁费'?></td>
      <td align="center"><input name="device2[cz]" type="text" size="8" class="number form-control sum sum-zx-device float" value="<?=$project->money($type)->getDevice2('cz')?:'0.00'?>" data-group="7" /></td>
      <td align="center"><input name="device2[zc]" type="text" size="8" class="number form-control sum sum-zc-device float" value="<?=$project->money($type)->getDevice2('zc')?:'0.00'?>" data-group="7" /></td>
      <td align="center"><input name="device2[qt]" type="text" size="8" class="number form-control sum sum-qt-device float" value="<?=$project->money($type)->getDevice2('qt')?:'0.00'?>" data-group="7" /></td>
      <td align="center"><input readonly name="device2[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getDevice2('hj')?:'0.00'?>" data-group="7" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['device3']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.device3']?:'（3）设备改造与租赁费'?></td>
      <td align="center"><input name="device3[cz]" type="text" size="8" class="number form-control terms" value="<?=$project->money($type)->getDevice3('cz')?:'0.00'?>" /></td>
      <td align="center"><input name="device3[zc]" type="text" size="8" class="number form-control terms" value="<?=$project->money($type)->getDevice3('zc')?:'0.00'?>" /></td>
      <td align="center"><input name="device3[hj]" type="text" size="8" class="number form-control sum" value="<?=$project->money($type)->getDevice3('hj')?:'0.00'?>" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['material']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.material']?:'材料费'?></td>
      <td align="center"><input name="material[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getMaterial('cz')?:'0.00'?>" data-group="8" /></td>
      <td align="center"><input name="material[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getMaterial('zc')?:'0.00'?>" data-group="8" /></td>
      <td align="center"><input name="material[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getMaterial('qt')?:'0.00'?>" data-group="8" /></td>
      <td align="center"><input readonly name="material[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getMaterial('hj')?:'0.00'?>" data-group="8" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['test']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.test']?:'测试化验加工费'?></td>
      <td align="center"><input name="test[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getTest('cz')?:'0.00'?>" data-group="9" /></td>
      <td align="center"><input name="test[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getTest('zc')?:'0.00'?>" data-group="9" /></td>
      <td align="center"><input name="test[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getTest('qt')?:'0.00'?>" data-group="9" /></td>
      <td align="center"><input readonly name="test[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getTest('hj')?:'0.00'?>" data-group="9" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['power']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.power']?:'燃料动力费'?></td>
      <td align="center"><input name="power[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getPower('cz')?:'0.00'?>" data-group="10" /></td>
      <td align="center"><input name="power[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getPower('zc')?:'0.00'?>" data-group="10" /></td>
      <td align="center"><input name="power[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getPower('qt')?:'0.00'?>" data-group="10" /></td>
      <td align="center"><input readonly name="power[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getPower('hj')?:'0.00'?>" data-group="10" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['xxhjs']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.xxhjs']?:'信息化系统建设费'?></td>
      <td align="center"><input name="data[xxhjs_cz]" type="text" size="8" class="number form-control terms" value="<?=$project->money($type)->getData('xxhjs_cz')?:'0.00'?>" /></td>
      <td align="center"><input name="data[xxhjs_zc]" type="text" size="8" class="number form-control terms" value="<?=$project->money($type)->getData('xxhjs_zc')?:'0.00'?>" /></td>
      <td align="center"><input name="data[xxhjs_hj]" type="text" size="8" class="number form-control sum" value="<?=$project->money($type)->getData('xxhjs_hj')?:'0.00'?>" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['cooperation']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.cooperation']?:'差旅费/会议费/国际合作与交流费'?></td>
      <td align="center"><input name="cooperation[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getCooperation('cz')?:'0.00'?>" data-group="11" /></td>
      <td align="center"><input name="cooperation[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getCooperation('zc')?:'0.00'?>" data-group="11" /></td>
      <td align="center"><input name="cooperation[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getCooperation('qt')?:'0.00'?>" data-group="11" /></td>
      <td align="center"><input readonly name="cooperation[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getCooperation('hj')?:'0.00'?>" data-group="11" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['cooperation2']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.cooperation2']?:'差旅费/会议费'?></td>
      <td align="center"><input name="cooperation[cz]" type="text" size="8" class="number form-control terms" value="<?=$project->money($type)->getCooperation('cz')?:'0.00'?>" /></td>
      <td align="center"><input name="cooperation[zc]" type="text" size="8" class="number form-control terms" value="<?=$project->money($type)->getCooperation('zc')?:'0.00'?>" /></td>
      <td align="center"><input name="cooperation[hj]" type="text" size="8" class="number form-control sum" value="<?=$project->money($type)->getCooperation('hj')?:'0.00'?>" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['reference']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.reference']?:'出版/文献/信息传播/知识产权事务费'?></td>
      <td align="center"><input name="reference[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getReference('cz')?:'0.00'?>" data-group="12" /></td>
      <td align="center"><input name="reference[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getReference('zc')?:'0.00'?>" data-group="12" /></td>
      <td align="center"><input name="reference[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getReference('qt')?:'0.00'?>" data-group="12" /></td>
      <td align="center"><input readonly name="reference[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getReference('hj')?:'0.00'?>" data-group="12" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['manpower']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.manpower']?:'劳务费'?></td>
      <td align="center"><input name="manpower[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getManpower('cz')?:'0.00'?>" data-group="13" /></td>
      <td align="center"><input name="manpower[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getManpower('zc')?:'0.00'?>" data-group="13" /></td>
      <td align="center"><input name="manpower[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getManpower('qt')?:'0.00'?>" data-group="13" /></td>
      <td align="center"><input readonly name="manpower[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getManpower('hj')?:'0.00'?>" data-group="13" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['consultancy']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.consultancy']?:'专家咨询费'?></td>
      <td align="center"><input name="consultancy[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getConsultancy('cz')?:'0.00'?>" data-group="14" /></td>
      <td align="center"><input name="consultancy[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getConsultancy('zc')?:'0.00'?>" data-group="14" /></td>
      <td align="center"><input name="consultancy[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getConsultancy('qt')?:'0.00'?>" data-group="14" /></td>
      <td align="center"><input readonly name="consultancy[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getConsultancy('hj')?:'0.00'?>" data-group="14" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['other']):?>
    <tr>
      <td><?=$configs['langs']['jfzc.other']?:'其他费用'?></td>
      <td align="center"><input name="other[cz]" type="text" size="8" class="number form-control sum sum-direct-zx float" value="<?=$project->money($type)->getOther('cz')?:'0.00'?>" data-group="15" /></td>
      <td align="center"><input name="other[zc]" type="text" size="8" class="number form-control sum sum-direct-zc float" value="<?=$project->money($type)->getOther('zc')?:'0.00'?>" data-group="15" /></td>
      <td align="center"><input name="other[qt]" type="text" size="8" class="number form-control sum sum-direct-qt float" value="<?=$project->money($type)->getOther('qt')?:'0.00'?>" data-group="15" /></td>
      <td align="center"><input readonly name="other[hj]" type="text" size="8" class="number form-control sum-total float" value="<?=$project->money($type)->getOther('hj')?:'0.00'?>" data-group="15" /></td>
    </tr>
    <?php endif;?>
    <?php if($configs['jfzc']['zchj']):?>
        <tr>
            <td>支出合计</td>
            <td align="center"><input readonly name="total[cz]" type="text" size="8" class="number form-control sum-all sum-zx-total float" value="<?=$project->money($type)->getTotal('cz')?:'0.00'?>" data-group="16" /></td>
            <td align="center"><input readonly name="total[zc]" type="text" size="8" class="number form-control sum-all sum-zc-total float" value="<?=$project->money($type)->getTotal('zc')?:'0.00'?>" data-group="16" /></td>
            <td align="center"><input readonly name="total[qt]" type="text" size="8" class="number form-control sum-all sum-qt-total float" value="<?=$project->money($type)->getTotal('qt')?:'0.00'?>" data-group="16" /></td>
            <td align="center"><input readonly name="total[hj]" type="text" size="8" class="number form-control sum-all-total sum-total float" value="<?=$project->money($type)->getTotal('hj')?:'0.00'?>" data-group="16" /></td>
        </tr>
    <?php endif;?>
  </table>
      <script>
          $(function () {
              $(".sum-zx").keyup(function () {
                  var me = $(this);
                  //求专项经费合计
                  sum(me,".sum-zx");
                  //求总合计
                  sum(me,".sum-all");
              });

              $(".sum-zc").keyup(function () {
                  var me = $(this);
                  //求自筹经费合计
                  sum(me,".sum-zc");
                  //求总合计
                  sum(me,".sum-all");
              });

              $(".sum-qt").keyup(function () {
                  var me = $(this);
                  //求自筹经费合计
                  sum(me,".sum-qt");
                  //求总合计
                  sum(me,".sum-all");
              });

              //专项设备费合计
              $(".sum-zx-device").keyup(function () {
                  var me = $(this);
                  //求专项经费(设备费)合计
                  sum(me,".sum-zx-device");
                  //求(设备费)合计
                  sum(me,".sum-device");
                  //求专项经费(直接费用)合计
                  sum(me,".sum-direct-zx");
                  //求专项经费合计
                  sum(me,".sum-zx");
                  //求直接费用合计
                  sum(me,".sum-direct");
                  //求总合计
                  sum(me,".sum-all");
              });

              //自筹设备费合计
              $(".sum-zc-device").keyup(function () {
                  var me = $(this);
                  //求自筹经费(设备费)合计
                  sum(me,".sum-zc-device");
                  //求(设备费)合计
                  sum(me,".sum-device");
                  //求自筹经费(直接费用)合计
                  sum(me,".sum-direct-zc");
                  //求自筹经费合计
                  sum(me,".sum-zc");
                  //求直接费用合计
                  sum(me,".sum-direct");
                  //求总合计
                  sum(me,".sum-all");
              });

              //其他设备费合计
              $(".sum-qt-device").keyup(function () {
                  var me = $(this);
                  //求其他经费(设备费)合计
                  sum(me,".sum-qt-device");
                  //求(设备费)合计
                  sum(me,".sum-device");
                  //求自筹经费(直接费用)合计
                  sum(me,".sum-direct-qt");
                  //求自筹经费合计
                  sum(me,".sum-qt");
                  //求直接费用合计
                  sum(me,".sum-direct");
                  //求总合计
                  sum(me,".sum-all");
              });

              $(".sum-direct-zx").keyup(function () {
                  var me = $(this);
                  //求专项经费(直接费用)合计
                  sum(me,".sum-direct-zx");
                  //求专项经费合计
                  sum(me,".sum-zx");
                  //求专项经费(直接费用)合计
                  sum(me,".sum-direct-zx");
                  //求直接费用合计
                  sum(me,".sum-direct");
                  //求总合计
                  sum(me,".sum-all");
              });

              $(".sum-direct-zc").keyup(function () {
                  var me = $(this);
                  //求自筹经费(直接费用)合计
                  sum(me,".sum-direct-zc");
                  //求自筹经费合计
                  sum(me,".sum-zc");
                  //求自筹经费(直接费用)合计
                  sum(me,".sum-direct-zc");
                  //求直接费用合计
                  sum(me,".sum-direct");
                  //求总合计
                  sum(me,".sum-all");
              });

              $(".sum-direct-qt").keyup(function () {
                  var me = $(this);
                  //求自筹经费(直接费用)合计
                  sum(me,".sum-direct-qt");
                  //求自筹经费合计
                  sum(me,".sum-qt");
                  //求自筹经费(直接费用)合计
                  sum(me,".sum-direct-qt");
                  //求直接费用合计
                  sum(me,".sum-direct");
                  //求总合计
                  sum(me,".sum-all");
              });
          });

          function sum(me,tag)
          {
              var sum_total = me.parents("table").find(tag+"-total");
              var total = 0;
              me.parents("table").find(tag).each(function () {
                  total+=parseFloat($(this).val());
              });
              total = total.toFixed(2);
              sum_total.val(total);
          }
      </script>
  <?php endif;?>
<?php if($configs['jfjs']):?>
    <table class="table table-bordered table-striped table-hover table-valign-middle auto_count2 presetfields mb-5">
        <tr>
            <th colspan="4" class="text-left"><span class="pull-right">（单位：万元）</span></th>
        </tr>
        <tr>
            <th width="40%" class="text-center" >科目</th>
            <th width="20%" class="text-center">预算数</th>
            <th width="20%" class="text-center">实际数</th>
            <th width="20%" class="text-center">结余</th>
        </tr>
        <?php if($configs['jfjs']['indirect']):?>
        <tr>
            <td>（一）间接费用</td>
            <td align="center"><input  name="indirect[ys]" type="text" size="8" class=" form-control sub sum-ys float" value="<?=$project->money($type)->getIndirect('ys')?:'0.00'?>" data-group="1" data-point="4" /></td>
            <td align="center"><input name="indirect[sj]" type="text" size="8" class="number form-control sub sum-sj float" value="<?=$project->money($type)->getIndirect('sj')?:'0.00'?>" data-group="1" data-point="4" /></td>
            <td align="center"><input name="indirect[jy]" type="text" size="8" class="number form-control sub_total sum-jy float" value="<?=$project->money($type)->getIndirect('jy')?:'0.00'?>" data-group="1" data-point="4" readonly/></td>
        </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['indirect']):?>
        <tr>
            <td>  其中：绩效支出</td>
            <td align="center"><input name="performance[ys]" type="text" size="8" class="number form-control sub float" value="<?=$project->money($type)->getPerformance('ys')?:'0.00'?>" data-group="2" data-point="4" /></td>
            <td align="center"><input name="performance[sj]" type="text" size="8" class="number form-control sub float" value="<?=$project->money($type)->getPerformance('sj')?:'0.00'?>" data-group="2" data-point="4" /></td>
            <td align="center"><input name="performance[jy]" type="text" size="8" class="number form-control sub_total float" value="<?=$project->money($type)->getPerformance('jy')?:'0.00'?>" data-group="2" data-point="4" readonly /></td>
        </tr>
      <?php endif;?>
        <?php if($configs['jfjs']['direct']):?>
        <tr>
            <td>（二）直接费用</td>
            <td align="center"><input readonly name="direct[ys]" type="text" size="8" class="number form-control sum sum-ys sum-direct-ys-total float" value="<?=$project->money($type)->getDirect('ys')?:'0.00'?>" data-group="3" data-point="4" /></td>
            <td align="center"><input readonly name="direct[sj]" type="text" size="8" class="number form-control sum sum-sj sum-direct-sj-total float" value="<?=$project->money($type)->getDirect('sj')?:'0.00'?>" data-group="3" data-point="4" /></td>
            <td align="center"><input readonly name="direct[jy]" type="text" size="8" class="number form-control sum sum-jy sum-direct-jy-total float" value="<?=$project->money($type)->getDirect('jy')?:'0.00'?>" data-group="3" readonly data-point="4" /></td>
        </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['device']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.device']?:'设备费'?></td>
                <td align="center"><input name="device[ys]" type="text" size="8" class="number form-control sum-direct-ys sum-ys-device-total sum float" value="<?=$project->money($type)->getDevice('ys')?:'0.00'?>" data-group="4" readonly  data-point="4" /></td>
                <td align="center"><input name="device[sj]" type="text" size="8" class="number form-control sum-direct-sj sum-sj-device-total sum float" value="<?=$project->money($type)->getDevice('sj')?:'0.00'?>" data-group="4" readonly data-point="4" /></td>
                <td align="center"><input name="device[jy]" type="text" size="8" class="number form-control sum-direct-jy sum-jy-device-total sum float" value="<?=$project->money($type)->getDevice('jy')?:'0.00'?>" data-group="4" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['device1']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.device1']?:'（1）购置设备费'?></td>
                <td align="center"><input name="device1[ys]" type="text" size="8" class="number form-control sub sum-ys-device sum float" value="<?=$project->money($type)->getDevice1('ys')?:'0.00'?>" data-group="5" data-point="4" /></td>
                <td align="center"><input name="device1[sj]" type="text" size="8" class="number form-control sub sum-sj-device sum float" value="<?=$project->money($type)->getDevice1('sj')?:'0.00'?>" data-group="5" data-point="4" /></td>
                <td align="center"><input name="device1[jy]" type="text" size="8" class="number form-control sub_total sum-jy-device sum float" value="<?=$project->money($type)->getDevice1('jy')?:'0.00'?>" data-group="5" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['device2']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.device2']?:'（2）设备试制、改造、租赁费'?></td>
                <td align="center"><input name="device2[ys]" type="text" size="8" class="number form-control sub sum-ys-device float" value="<?=$project->money($type)->getDevice2('ys')?:'0.00'?>" data-group="6" data-point="4" /></td>
                <td align="center"><input name="device2[sj]" type="text" size="8" class="number form-control sub sum-sj-device float" value="<?=$project->money($type)->getDevice2('sj')?:'0.00'?>" data-group="6" data-point="4" /></td>
                <td align="center"><input name="device2[jy]" type="text" size="8" class="number form-control sub_total sum-jy-device float" value="<?=$project->money($type)->getDevice2('jy')?:'0.00'?>" data-group="6" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['material']):?>
            <tr>
                <td>2、材料费</td>
                <td align="center"><input name="material[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getMaterial('ys')?:'0.00'?>" data-group="7" data-point="4" /></td>
                <td align="center"><input name="material[sj]" type="text" size="8" class="number form-control sub sum-direct-sj sum float" value="<?=$project->money($type)->getMaterial('sj')?:'0.00'?>" data-group="7" data-point="4" /></td>
                <td align="center"><input name="material[jy]" type="text" size="8" class="number form-control sub_total sum-direct-jy sum float" value="<?=$project->money($type)->getMaterial('jy')?:'0.00'?>" data-group="7" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['test']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.test']?:'测试化验加工费'?></td>
                <td align="center"><input name="test[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getTest('ys')?:'0.00'?>" data-group="8" data-point="4" /></td>
                <td align="center"><input name="test[sj]" type="text" size="8" class="number form-control sub sum-direct-sj sum float" value="<?=$project->money($type)->getTest('sj')?:'0.00'?>" data-group="8" data-point="4" /></td>
                <td align="center"><input name="test[jy]" type="text" size="8" class="number form-control sub_total sum-direct-jy sum float" value="<?=$project->money($type)->getTest('jy')?:'0.00'?>" data-group="8" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['power']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.power']?:'燃料动力费'?></td>
                <td align="center"><input name="power[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getPower('ys')?:'0.00'?>" data-group="9" data-point="4" /></td>
                <td align="center"><input name="power[sj]" type="text" size="8" class="number form-control sub sum-direct-sj sum float" value="<?=$project->money($type)->getPower('sj')?:'0.00'?>" data-group="9" data-point="4" /></td>
                <td align="center"><input name="power[jy]" type="text" size="8" class="number form-control sub_total sum-direct-jy sum float" value="<?=$project->money($type)->getPower('jy')?:'0.00'?>" data-group="9" readonly  data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['cooperation']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.cooperation']?:'差旅费/会议费/国际合作与交流费'?></td>
                <td align="center"><input name="cooperation[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getCooperation('ys')?:'0.00'?>" data-group="10" data-point="4" /></td>
                <td align="center"><input name="cooperation[sj]" type="text" size="8" class="number form-control sub sum-direct-sj sum float" value="<?=$project->money($type)->getCooperation('sj')?:'0.00'?>" data-group="10" data-point="4" /></td>
                <td align="center"><input name="cooperation[jy]" type="text" size="8" class="number form-control sub_total sum-direct-jy sum float" value="<?=$project->money($type)->getCooperation('jy')?:'0.00'?>" data-group="10" readonly/></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['reference']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.reference']?:'出版/文献/信息传播/知识产权事务费'?></td>
                <td align="center"><input name="reference[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getReference('ys')?:'0.00'?>" data-group="11" data-point="4" /></td>
                <td align="center"><input name="reference[sj]" type="text" size="8" class="number form-control sub sum-direct-sj sum float" value="<?=$project->money($type)->getReference('sj')?:'0.00'?>" data-group="11" data-point="4" /></td>
                <td align="center"><input name="reference[jy]" type="text" size="8" class="number form-control sub_total sum-direct-jy sum float" value="<?=$project->money($type)->getReference('jy')?:'0.00'?>" data-group="11" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['manpower']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.manpower']?:'劳务费'?></td>
                <td align="center"><input name="manpower[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getManpower('ys')?:'0.00'?>" data-group="12" data-point="4" /></td>
                <td align="center"><input name="manpower[sj]" type="text" size="8" class="number form-control sub sum-direct-sj sum float" value="<?=$project->money($type)->getManpower('sj')?:'0.00'?>" data-group="12" data-point="4" /></td>
                <td align="center"><input name="manpower[jy]" type="text" size="8" class="number form-control sub_total sum-direct-jy sum float" value="<?=$project->money($type)->getManpower('jy')?:'0.00'?>" data-group="12" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['consultancy']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.consultancy']?:'专家咨询费'?></td>
                <td align="center"><input name="consultancy[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getConsultancy('ys')?:'0.00'?>" data-group="13" data-point="4" /></td>
                <td align="center"><input name="consultancy[sj]" type="text" size="8" class="number form-control sub sum-direct-sj sum float" value="<?=$project->money($type)->getConsultancy('sj')?:'0.00'?>" data-group="13" data-point="4" /></td>
                <td align="center"><input name="consultancy[jy]" type="text" size="8" class="number form-control sub_total sum-direct-jy sum float" value="<?=$project->money($type)->getConsultancy('jy')?:'0.00'?>" data-group="13" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['other']):?>
            <tr>
                <td><?=$configs['langs']['jfjs.other']?:'其他费用'?></td>
                <td align="center"><input name="other[ys]" type="text" size="8" class="number form-control sub sum-direct-ys sum float" value="<?=$project->money($type)->getOther('ys')?:'0.00'?>" data-group="14" data-point="4" /></td>
                <td align="center"><input name="other[sj]" type="text" size="8" class="number form-control sub sum sum-direct-sj float" value="<?=$project->money($type)->getOther('sj')?:'0.00'?>" data-group="14" data-point="4" /></td>
                <td align="center"><input name="other[jy]" type="text" size="8" class="number form-control sub_total sum sum-direct-jy float" value="<?=$project->money($type)->getOther('jy')?:'0.00'?>" data-group="14" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
        <?php if($configs['jfjs']['jshj']):?>
            <tr>
                <td>支出合计</td>
                <td align="center"><input name="data[ys_hj]" readonly type="text" size="8" class="number form-control sum-ys-total sum-total float" data-group="1" value="<?=$project->money($type)->getData('ys_hj')?:'0.00'?>" data-point="4" /></td>
                <td align="center"><input name="data[sj_hj]" readonly type="text" size="8" class="number form-control sum-sj-total sum-total float" data-group="2" value="<?=$project->money($type)->getData('sj_hj')?:'0.00'?>" data-point="4" /></td>
                <td align="center"><input name="data[jy_hj]" readonly type="text" size="8" class="number form-control sum-jy-total sum-total float" data-group="3" value="<?=$project->money($type)->getData('jy_hj')?:'0.00'?>" readonly data-point="4" /></td>
            </tr>
        <?php endif;?>
    </table>
    <script>
        $(".sub").keyup(function () {
            var group = $(this).data('group');
            var total = 0;
            var me = $(this);
            var arr = new Array();
            var pointLen = parseInt($(this).data('point')) ? parseInt($(this).data('point')) : 2;       //小数点位数
            me.parents("body").find(".sub").each(function () {
                var g = $(this).data('group');
                if(g!=group) return;
                if($(this).hasClass('float')){
                    var val1 = parseFloat($(this).val());
                    if(isNaN(val1)){
                        val1=0;
                    }
                    arr.push(val1);
                }else{
                    var val = parseInt($(this).val());
                    if(isNaN(val)){
                        val=0;
                    }
                    arr.push(val);
                }
            });
            var result = 0;
            if(arr.length==2){
                result = arr[0] - arr[1];
            }
            if(group===undefined){
                var sub_total = me.parents("body").find(".sub-total");
                if(sub_total.hasClass('float')){
                    result = result.toFixed(pointLen);
                    result = parseFloat(result);
                }
                sub_total.val(result);
            }else{
                var sub_total = me.parents("body").find(".sub_total[data-group='"+group+"']");
                if(sub_total.hasClass('float')){
                    result = result.toFixed(pointLen);
                    result = parseFloat(result);
                }
                console.log(result);
                sub_total.val(result);
            }
        });

        //预算数合计
        $(".sum-ys").keyup(function () {
            var me = $(this);
            sum(me,".sum-ys");
            jys(me);
        });
        $(".sum-sj").keyup(function () {
            var me = $(this);
            sum(me,".sum-sj");
            jys(me);
        });
        $(".sum-jy").keyup(function () {
            var me = $(this);
            sum(me,".sum-jy");
            jys(me);
        });

        //直接费用预算数合计
        $(".sum-direct-ys").keyup(function () {
            var me = $(this);
            sum(me,".sum-direct-ys");
            sum(me,".sum-ys");
            jys(me);
        });

        //直接费用实际数合计
        $(".sum-direct-sj").keyup(function () {
            var me = $(this);
            sum(me,".sum-direct-sj");
            sum(me,".sum-sj");
            jys(me);
        });

        //设备费预算数合计
        $(".sum-ys-device").keyup(function () {
            var me = $(this);
            var group = $(this).data('group');
            sum(me,".sum-ys-device");
            sum(me,".sum-direct-ys");
            sum(me,".sum-ys");
            sum(me,".sum-jy-device");
            jys(me);
        });

        //设备费实际数合计
        $(".sum-sj-device").keyup(function () {
            var me = $(this);
            sum(me,".sum-sj-device");
            sum(me,".sum-direct-sj");
            sum(me,".sum-sj");
            jys(me);
        });

        function jys(me)
        {
            sum(me,".sum-jy-device");
            sum(me,".sum-direct-jy");
            sum(me,".sum-jy");
        }

        function sum(me,tag)
        {
            var sum_total = me.parents("table").find(tag+"-total");
            var total = 0;
            me.parents("table").find(tag).each(function () {
                total+=parseFloat($(this).val());
            });
            total = total.toFixed(4);
            total = parseFloat(total);
            sum_total.val(total);
        }
    </script>
<?php endif;?>
  <?php if($configs['rczc']):?>
  <table class="table table-bordered table-striped table-hover">
          <tr>
              <th colspan="2" class="text-left"><?=$configs['langs']['jfly']?:'经费来源'?><span class="pull-right">（单位：万元）</span></th>
          </tr>
          <tr>
              <th class="text-center">科目名称</th>
              <th width="27%" class="text-center">经费</th>
          </tr>
          <tr>
              <td><?=$configs['langs']['jfly.declare_money']?:'申请专项经费'?></td>
              <td align="center"><input name="declare_money" type="text" size="8" class="number form-control sum" value="<?=$project->money($type)->getDeclareMoney()?:'0.00'?>" /></td>
          </tr>
          <tr>
              <td><?=$configs['langs']['jfly.own_money']?:'单位自筹经费'?></td>
              <td align="center"><input name="own_money" type="text" size="8" class="number form-control sum" value="<?=$project->money($type)->getOwnMoney()?:'0.00'?>" /></td>
          </tr>
          <tr >
              <td>项目总经费</td>
              <td align="center"><input name="total_money" type="text" size="8" class="number form-control sum" value="<?=$project->money($type)->getTotalMoney()?:'0.00'?>" /></td>
          </tr>
      </table>
  <table class="table table-bordered table-striped table-hover">
    <tr>
      <th rowspan="2" align="center">专家姓名</th>
      <th rowspan="2" align="center">国籍</th>
      <th rowspan="2" align="center">来川次序</th>
      <th rowspan="2" align="center">工作天数</th>
      <th rowspan="2" align="center">国际旅费<br><span style="color:red">≤1.5</span></th>
      <th rowspan="2" align="center">专家生活费<br><span style="color:red">≤1.8</span></th>
      <th rowspan="2" align="center">城市间交通费<br><span style="color:red">≤0.3</span></th>
      <th rowspan="2" align="center">零用费<br><span style="color:red">与工薪二选一</span></th>
      <th colspan="2" align="center">工薪<br><span style="color:red">与零用费二选一</span></th>
      <th rowspan="2" align="center">小计<br><span style="color:red">该项自动计算</span></th>
    </tr>
    <tr>
      <th align="center">合同约定金额</th>
      <th align="center">申请资助<br><span style="color:red">≤合同约定金额*60%</span></th>
    </tr>
      <?php
        $experts = $project->experts();
        $expertArr = [];
        $i=0;
        while($expert = $experts->getObject()){
            $expertArr[$i]['name'] = $expert->getName().($expert->getNameCn() ? '('.$expert->getNameCn().')': '');
            $expertArr[$i]['nation'] = $expert->getNation();
            $workDays = $expert->getWorkDays();
            $expertArr[$i]['times'] = $workDays['total'];
            $expertArr[$i]['days'] = $workDays['days'];
            $expertArr[$i]['data'] = $workDays['data'];
            $i++;
        }
      ?>
    <?php $pay = $project->money($type)->getPay();?>
    <?php for($i=0,$n=0;$i<count($expertArr);$i++):?>
    <?php for($j=0;$j<$expertArr[$i]['times'];$j++):?>
    <tr>
      <td><input name="pay[zjxm][]" type="text" size="20" value="<?=$expertArr[$i]['name']?>" <?=$expertArr[$i]['name']?'readonly':''?> class="form-control"/></td>
      <td><input name="pay[zjgj][]" type="text" size="20" value="<?=$expertArr[$i]['nation']?>" <?=$expertArr[$i]['name']?'readonly':''?> class="form-control"/></td>
      <td><input name="pay[lhcs][]" type="number" size="5" value="<?=($j+1)?>" <?=$expertArr[$i]['name']?'readonly':''?> class="form-control number"/></td>
      <td><input name="pay[gzts][]" type="number" size="5" value="<?=$expertArr[$i]['data']['days'][$j]?>" <?=$expertArr[$i]['name']?'readonly':''?> class="form-control number"/></td>
      <td><input name="pay[gjlf][]" type="number" size="5" value="<?=$pay['gjlf'][$n]?>" min="0" step="0.01" max="1.5" class="form-control number"/></td>
      <td><input name="pay[zjshf][]" type="number" size="5" value="<?=$pay['zjshf'][$n]?>" min="0" step="0.01" max="1.8" class="form-control number"/></td>
      <td><input name="pay[csjtf][]" type="number" size="5" value="<?=$pay['csjtf'][$n]?>" min="0" step="0.01" max="0.3" class="form-control number"/></td>
      <td><input name="pay[zjlhq][]" type="number" size="5" value="<?=$pay['zjlhq'][$n]?>" min="0" step="0.01" max="0.9" class="form-control number"/></td>
      <td><input name="pay[htje][]" type="number" size="5" value="<?=$pay['htje'][$n]?>" min="0" step="0.01" class="form-control number"/></td>
      <td><input name="pay[sqje][]" type="number" size="5" value="<?=$pay['sqje'][$n]?>" min="0" step="0.01" class="form-control number"/></td>
      <td><input readonly name="pay[heji][]" type="number" size="5" value="<?=$pay['heji'][$n]?>" min="0" step="0.01" class="form-control number"/></td>
    </tr>
    <?php $n++;endfor;?>
    <?php endfor;?>
  </table>
  <?php endif;?>
  <?php if($configs['lysm']):?>
   <table class="table table-bordered table-striped table-hover">
    <tr>
      <th class="text-left"><?=$configs['langs']['lysm']?:'其他来源经费说明'?><small>(说明经费来源情况，并附相关的证明材料)</small></th>
    </tr>
    <tr>
      <td><textarea name="zjyt" type="textarea" class="form-control" rows="5"><?=$project->money($type)->getZjyt()?></textarea></td>
    </tr>
    </table>
  <?php endif;?>
  <div class="space"></div>
  <div class="fixedbottom">
    <div class="col-md-12 text-center">
        <?=btn('button','保存资料','submit','save')?>
        <?=btn('button','关闭窗口','button','close','closeWindow()','btn-sm','btn-alt-danger')?>
    </div>
  </div>
</form>
<style type="text/css">
.fixedbottom{position:fixed!important;bottom: 0;width: 100%;padding:5px 0;}
</style>
<script src="<?=site_path('js/math.js')?>?v=20220208"></script>
