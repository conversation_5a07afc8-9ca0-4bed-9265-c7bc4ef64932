<?php
namespace App\Controller\Engine\Summary\Content;
use Sofast\Core\Sf;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Contract\iWidget;

class Widget implements iWidget
{   
    private $summary = NULL;
    /**
     * 应用层配置
     */
    private $configs = array();
    /**
     * 模块层配置
     */
    private $widgetConfigs = array();
    private $view = NULL;
	private $widget_name = 'attachement';
    
	function __construct($widget_name,$configs = NULL)
	{
		$this->widget_name = $widget_name;
		$this->widgetConfigs = Configs::getConfigs();
		if($configs !== NULL){
			$this->configs = $configs;
		}else $this->configs = Configs::getDefault();

		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		return $this;
	}
	
	public function setProject($project)
	{
		$this->summary = $project;
		return $this;
	}

    public function getWidgetName()
    {
        return $this->widget_name;
    }
	
	public function setConfig($configs=array())
	{
		if(count($configs)) $this->configs = $configs;
		return $this;
	}
	
	public function getConfig()
	{
		return $this->configs;
	}
	
	public function output($tpl='')
	{
		$this->view->set("summary",$this->summary);
		$this->view->set("configs",$this->getConfig());
		$this->view->set("widget_name",$this->widget_name);
		if($this->configs['action']['download']){
			$file = $this->summary->contents($this->widget_name);
			if(!$file['file_path']) return "{{error:文件未上传}}";
			if($file['pdf_path']) return "{{file:".$file['pdf_path']."}}";
			else return "{{file:".site_path('up_files/'.$file['file_path'])."}}";
		}

		if($tpl) return $this->view->getContent($tpl);
		else{
			if($this->configs['attachement']['output_template']) $tpl = trim($this->configs['attachement']['output_template']);
			else $tpl = 'Edit/output';
			$htmlStr = '';
			if(count($this->configs['attachement'])) $htmlStr .= $this->view->getContent($tpl);
			return $htmlStr;
		}
	}
	
	/**
	 * 模块配置
	 */
	public function manager()
	{
		$this->view->set("configs",$this->configs);
		$this->view->set("widgetConfigs",$this->widgetConfigs);
		return $this->view->getContent("Manager");
	}
	
	/**
	 * 完整性检验
	 * 返回数组
	 */
	function validate(){
		$result = [];
		$file = $this->summary->contents($this->widget_name);
        $this->configs['attachement']['subject'] = str_replace('<br>','；',$this->configs['attachement']['subject']);
		if(!$file['file_path']) $result[] = '正文 【'.$this->configs['attachement']['subject'].'】 未上传';
		return $result;
	}
	
	function __call($tpl,$args)
	{
		return $this->output($tpl);
	}
	
	function __toString()
	{
		return $this->output();	
	}
}
?>