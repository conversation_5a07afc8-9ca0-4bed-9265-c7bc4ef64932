<?php
namespace App\controller\Engine;
use App\Controller\BaseController;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use App\Facades\PDF;
use App\Lib\Attachment;

class Stager extends BaseController
{
    private $configs = array();
    private $modulars = array();
    private $worker = NULL;
    private $view = NULL;
    private $stage = NULL;
    private $declare_year = 2022;   //填报年度

    function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->declare_year = 2022;
    }

    /**
     * 新增中期
     */
    function create()
    {
        $this->stage = sf::getModel("Stages")->selectByStageId('');
        $this->stage->setDeclareYear($this->declare_year);
        $this->stage->init();   //初始化参数

        if(input::post())
        {
            if(empty(input::post('linkman'))) $this->error('请填写联络人姓名');
            if(empty(input::post('linkman_mobile'))) $this->error('请填写联系电话');
            if($this->stage->getUserRole()==2){
                if(empty(input::post('project_id'))){
                    $this->error('请选择项目名称');
                }
                $project = sf::getModel('Projects')->selectByProjectId(input::post('project_id'));
                if($project->isNew()){
                    $this->error('没有找到该项目');
                }
                if($project->getUserId()!=$this->stage->getUserId()){
                    $this->error('没有权限选择该项目');
                }
                $this->stage->setProjectId($project->getProjectId());
                $this->stage->setSubject($project->getSubject());
            }

            if($this->stage->isRepeat()) $this->error('已存在'.$this->stage->getDeclareYear().'年的中期评估表，不能重复填写！');

            $this->stage->setLinkman(input::post('linkman'));
            $this->stage->setLinkmanMobile(input::post('linkman_mobile'));
            $this->stage->setStatement(1);
            $this->stage->setConfigs('path.apply',"engine/stager");
            $this->stage->setCreatedAt(date('Y-m-d H:i:s'));
            $this->stage->save();
            $_SESSION['stage']['stage_id'] = $this->stage->getStageId();
            $_SESSION['worker']['worker_id'] = $this->stage->getWorkerId();
            $this->jump(site_url("engine/stager/edit/id/".$this->stage->getStageId()));
        }


        $this->view->set("declare_year",$this->declare_year);
        $this->view->set("stage",$this->stage);
        $this->view->apply("inc_body","Worker/Stage/Create");
        $this->view->display("page");
    }

    function index()
    {
        $this->edit();
    }


    function edit()
    {
        $this->stage = sf::getModel("Stages")->selectByStageId(input::getMix('id'));
        if($this->stage->isNew()) $this->page_debug("中期报告不存在！",getFromUrl());
        if($this->stage->getUserId() != input::session("roleuserid")) $this->page_debug("你没有权限执行该操作！",getFromUrl());
        $this->worker = $this->stage->worker('stage');
        if($this->worker->isNew()) $this->page_debug("参数错误！",getFromUrl());
        //不在编辑状态不能编辑
        if(!in_array($this->stage->getStatement(),array('-1','0','1','3','6','12'))) $this->page_debug("当前状态你没有权限执行该操作！",getFromUrl());
        //标记为编辑模式
        $this->stage->setWidgetConfigs('action',array('edit'=>true,'worker'=>'stager'));
        $this->view->set("edit",'yes');
        $this->view->set("stage",$this->stage);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template());
    }

    function show()
    {
        $this->stage = sf::getModel("Stages")->selectByStageId(input::getMix('id'));
        if($this->stage->isNew()) $this->page_debug("中期报告不存在！",getFromUrl());
        if($this->stage->getWorkerId()==0) $this->page_debug("该项目中期报告无具体内容信息！",getFromUrl());
        $this->worker = $this->stage->worker('stage');
        $this->stage->setWidgetConfigs('action',array('show'=>true,'worker'=>'stager'));
        $this->view->set("stage",$this->stage);
        $this->view->set("configs",$this->worker->getConfigs());
        $this->view->display("Worker/".$this->worker->template());
    }

    function download()
    {
        $this->output();
    }

    /**
     * <AUTHOR>
     * @DateTime  2019-08-11
     * @copyright 重载模块
     * @license   [license]
     * @version   [version]
     * @return    [type]      [description]
     */
    function reloadWidget()
    {
        $this->stage = sf::getModel("Stages")->selectByStageId(input::mix("id"));
        if($this->stage->isNew()) exit('中期报告不存在！');
        $this->stage->setWidgetConfigs('action',array('edit'=>'yes','worker'=>'stager'));
        exit($this->stage->widgets(input::mix("widget"),'stage'));
    }

    /**
     * 申报书盖章等页面打印稿生成
     * @return [type] [description]
     */
    function printer()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        $this->stage = sf::getModel("Stages")->selectByStageId(input::mix("id"));
        if($this->stage->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->stage->setWidgetConfigs('action',array('print'=>'yes','worker'=>'stager'));
        $this->worker = $this->stage->worker('stage');
        $this->view->set("print",'yes');
        $this->view->set("stage",$this->stage);
        $this->view->set("configs",$this->stage->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //$this->stage->setShortUrl($hash,'attachement');
        //提取打印内容
        $type = input::getMix('type');
        $pattern = '/\<widget type\="printer"\>(.*?)\<\/widget\>/s';
        if($type)  $pattern = '/\<widget type\="printer"\ name="'.$type.'">(.*?)\<\/widget\>/s';
        preg_match_all($pattern,$htmlStr,$matches);
        if($matches[1]){
            $htmlStr = implode('<pagebreak></pagebreak>',$matches[1]);
            $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        }else{
            $htmlStr = stristr($htmlStr,'<widget type="printer">');
            $htmlStr = stristr($htmlStr,'</widget>',true);
            $htmlStr = str_replace('<widget type="printer">','<link rel="stylesheet" href="'.site_path("css/template.css").'">',$htmlStr);
        }

        $pdf =  PDF::reload('zh-CN')->setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="50%" style="border: 0px solid #fff;">四川省临床重点专科建设项目中期评估表</td><td width="50%" style="text-align: right;border: 0px solid #fff;">'.$this->stage->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->stage->getSubject())
            ->setSubject($this->stage->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->stage->getUserName())
            ->setContent($htmlStr)
            ->hasCover(false)
            ->setWaterMark('四川省临床重点专科建设项目中期评估表',0.1)
            ->show();
    }

    /**
     * 生成月度报告
     */
    function output()
    {
        @ini_set('memory_limit', '200M');//设置最大使用内存为200M
        $this->stage = sf::getModel("Stages")->selectByStageId(input::mix("id"));
        if($this->stage->isNew()) $this->page_debug("中期报告不存在！",getFromUrl());
        //已经生成的直接下载
        if($this->stage->enablePrint()){
            if($file_name = $this->stage->getConfigs("file.apply")){//如果已经生成文件，直接打开下载
                $refresh = input::mix("refresh") ? true : false;
                if(!$refresh){//不刷新就直接跳转
                    addHistory($this->stage->getStageId(),'下载中期报告','stage',1);
                    @header("Location:".site_path('up_files/'.$file_name));exit();
                    exit;
                }
            }
        }

        $this->stage->setWidgetConfigs('action',array('download'=>'yes','worker'=>'stager'));

        $this->worker = $this->stage->worker('stage');
        $this->view->set("download",'yes');
        $this->view->set("stage",$this->stage);
        $this->view->set("configs",$this->stage->getWidgetConfigs());
        $htmlStr = $this->view->getContent("Worker/".$this->worker->template());
        $hash = substr(md5(strip_tags($htmlStr)),0,20);
        //替换一下
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //拆分内容
        $title = '四川省临床重点专科建设项目中期评估表';
        $htmlArray = explode('{{file:',$htmlStr);

        $pdf =  PDF::reload('zh-CN')->setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="50%" style="border: 0px solid #fff;">'.$title.'</td><td width="50%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;">- {PAGENO} -</td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->stage->getSubject())
            ->setSubject($this->stage->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->stage->getCompanyName())
            ->hasCover(false);
        //设置输出内容
//        if($this->stage->getUserRole()==3){
//            //医院只有正文
//            $content = $htmlArray[1];
//            if(substr($content,0,4) == 'http') $file = strstr($content,'}}',true);
//            else $file = config::get("upload_path").strstr($content,'}}',true);
//            $pdf->setContent($file,'file');
//        }else{
            for($i=0,$n=count($htmlArray);$i<$n;$i++){
                if($i > 0){//分析插入文件
                    //如果是WORD文档将直接传输URL地址
                    if(substr($htmlArray[$i],0,4) == 'http') $file = strstr($htmlArray[$i],'}}',true);
                    else $file = config::get("upload_path").strstr($htmlArray[$i],'}}',true);
                    $pdf->setContent($file,'file');

                    $content = substr(strstr($htmlArray[$i],'}}'),2);
                    $pdf->setContent($content);
                }else {
                    $pdf->setContent($htmlArray[$i]);
                }
            }
//        }


        //保存的文件名
        $savename = time().mt_rand(1000,9999).'.pdf';
        $file_path = date("Y").'/'.date("m").'/'.$savename;
        $dir_path = WEBROOT."/up_files/".dirname($file_path);
        if (!file_exists($dir_path)) { //检查目录是否存在
            if (!@mkdir($dir_path, 0755, true)) {
                exit('不能创建目录: ' . \dirname($file_path));
            }
        }
        if($this->stage->enablePrint('apply')){
            //水印
            $pdf->setWaterMark($title,0.1);
            $pdf->save(WEBROOT."/up_files/".$file_path);

            //记录到项目配置
            $this->stage->setConfigs("file.apply",$file_path);
            @header("Location:".site_path('up_files/'.$file_path));exit();
            exit();
        }else{
            //水印
            $pdf->setWaterMark($title,0.1);

            $pdf->show();
            exit();
        }
    }




}
