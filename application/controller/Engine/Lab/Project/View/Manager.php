<style type="text/css">
.widget-config { border:solid 1px #efefef; padding:5px;}
.widget-config dt { border-bottom:dashed 2px #efefef;}
.widget-config dd { float:left; padding:3px 5px;}
</style>
<h2>基本信息配置</h2>
<?php  foreach($widgetConfigs as $_key => $_val):?>
<dl class="widget-config">
  <dt>
    <label>
      <input type="checkbox" name="configs[<?=$_key?>]" value="1"<?php if($configs[$_key]):?> checked="checked"<?php endif;?> />
      <?=$_val['name']?>
    </label>
  </dt>
  <?php if(count($_val['childs'])):?>
  <?php  foreach($_val['childs'] as $__key => $__val):?>
  <dd>
    <label>
      <input type="checkbox" name="configs[<?=$_key?>][<?=$__key?>]" value="1"<?php if($configs[$_key][$__key]):?> checked="checked"<?php endif;?> />
      <?=$__val['name']?>
    </label>
  </dd>
  <?php  endforeach;?>
  <?php endif;?>
  <p style="clear:both;"></p>
</dl>
<?php  endforeach;?>
<div class="widget-config">
    <h4>执行器</h4>
    <div><?=get_radio(array('worker'=>'申报书引擎','tasker'=>'任务书引擎','stage'=>'中期报告引擎','completer'=>'验收报告引擎'),'configs[engine]',($configs['engine']?:'worker'),'',false)?></div>
</div>