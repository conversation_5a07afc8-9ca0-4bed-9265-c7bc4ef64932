<?php
namespace App\Controller\quarter;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class office extends BaseController
{

    public function load()
    {
        $this->hash = md5(input::getInput("mix.controller").input::getInput("mix.method").input::getInput("mix.guideid"));
        $data['hash'] = $this->hash;
        view::set($data);
    }

    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('quarter/office/wait_list','statement  = 9');
    }

    public function accept_list()
    {
        $this->gird('quarter/office/accept_list','statement = 10');
    }

    public function back_list()
    {
        $this->gird('quarter/office/back_list','statement = 12');
    }

    public function gird($tpl = 'quarter/office/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        input::getInput("mix.guideid") && $addWhere .= " AND `guide_id` = '".input::getInput("mix.guideid")."'";
        input::getInput("mix.platform_type") && $addWhere .= " AND `platform_type` = '".input::getInput("mix.platform_type")."'";
        input::getInput("mix.department_id") && $addWhere .= " AND (`department_id` = '".input::getInput("mix.department_id")."' or `top_department_id` = '".input::getInput("mix.department_id")."')";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."'";
        if(input::getInput("mix.field") && input::getInput("mix.search")){
            $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        }
        $guide = sf::getModel('QuarterGuides',input::getInput("mix.guideid"));

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['quarters']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['quarters']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['quarters']['orderStr'] = base64_encode($addSql);
        }else{
//            $addWhere = base64_decode($_SESSION['quarters']['sqlStr']);
        }
        $form_vars = array('subject','corporation_name','department_id','platform_type','guideid','statement');
        view::set('guide',$guide);
        view::set("pagers",sf::getModel('Quarters')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }


    public function manage()
    {
        $pagers = sf::getModel('QuarterGuides')->getPager("","order by id desc");
        view::set('pagers',$pagers);
        view::apply("inc_body",'quarter/office/manage');
        view::display('page');
    }

    public function edit()
    {
        $guide = sf::getModel('QuarterGuides',input::getMix('id'));
        if($guide->isNew() || input::session('userlevel')==1){
            $types = get_select_data('platform_type');
            $scope['total'] = getPlatformTotal();
            foreach ($types as $id=>$type){
                $scope[$id] = getPlatformTotal($type);
            }
            $guide->setScope($scope);
        }
        if(input::post()){
            $guide->setSubject(input::post('subject'));
            $guide->setStartAt(input::post('start_at'));
            $guide->setEndAt(input::post('end_at'));
            $guide->setStatStartAt(input::post('start_year').'-'.input::post('start_month'));
            $guide->setStatEndAt(input::post('end_year').'-'.input::post('end_month'));
            $guide->setZdjg(input::post('zdjg'));
            $guide->setPzjg(input::post('pzjg'));
            $guide->setPzwh(input::post('pzwh'));
            $guide->setYxqz(input::post('yxqz'));
            $guide->setFormId(input::post('form_id'));
            $guide->setEnabled(input::post('enabled'));
            $guide->setScope(input::post('scope'));
            if($guide->isNew()) $guide->setCreatedAt(date('Y-m-d H:i:s'));
            $guide->setUpdatedAt(date('Y-m-d H:i:s'));
            $guide->save();
            $this->success(lang::get('Has been saved!'),site_url('quarter/office/edit/id/'.$guide->getId()));
        }
        view::set('guide',$guide);
        view::apply("inc_body",'quarter/office/edit');
        view::display('page');
    }

    public function delete()
    {
        $guide = sf::getModel('QuarterGuides',input::getMix('id'));
        $this->error("暂时不能删除");
    }

    /**
     * 上交情况
     */
    public function submit()
    {
        $pagers = sf::getModel('QuarterGuides')->getPager("enabled = 1","order by id desc");
        view::set('pagers',$pagers);
        view::set('refresh',input::getMix('refresh'));
        view::apply("inc_body",'quarter/office/submit');
        view::display('page');
    }


    /**
     * 受理
     */
    public function doAccept()
    {
        $quarter = sf::getModel('Quarters')->selectByItemId(input::getInput('mix.id'));
        //如果项目不存在
        if($quarter->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $quarter->setStatement(10);
        $quarter->save();

        sf::getModel("historys")->addHistory($quarter->getItemId(),'报表已审核','quarter');//保存历史记录
        $this->success('已审核',getFromUrl());
    }

    public function doBack()
    {
        $quarter = sf::getModel("Quarters")->selectByItemId(input::mix("id"));
        if($quarter->isNew()) $this->refresh(lang::get('The project is not found!'),'error');
        if($quarter->getGuide()->isEnd()){
            $this->refresh('时间已截止，不允许执行该操作！','error');
        }
        if(input::getInput("post.content")){
            $_statement = $quarter->getStatement();
            $quarter->setStatement(12);//退回
            $quarter->save();
            addHistory($quarter->getItemId(),'报表退回！其原因是：<br />'.input::getInput("post.content"),'quarter');
            $this->refresh('已退回','success');
        }
        //原因表单
        $form = Form::load('quarter/office/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'平台名称：'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($quarter->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因：'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改！'))
            ->addItem(Form::hidden('id',$quarter->getItemId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    public function doAcceptSelected()
    {
        if(input::getInput("post.select_id"))
            $ids = input::getInput("post.select_id");
        else
            $ids[] = input::getInput("get.id");
        $ids = array_filter($ids);
        if(empty($ids)){
            $this->error("请勾选要审核的报表");
        }
        for($i=0,$n=count($ids);$i<$n;$i++){
            $quarter = sf::getModel("Quarters")->selectByItemId($ids[$i]);
            if($quarter->isNew()) continue;
            $quarter->setStatement(10);
            $quarter->save();
            addHistory($quarter->getItemId(),'报表已审核','quarter');
        }
        $this->success('已审核'.count($ids).'个报表！',getFromUrl());
    }

    public function doBackSelected()
    {
        $ids = input::mix('id');
        $ids = explode(',',$ids);
        $ids = array_filter($ids);
        if(empty($ids)){
            $this->error("请勾选要退回的报表");
        }
        if(input::getInput("post.content")){
            for($i=0,$n=count($ids);$i<$n;$i++){
                $quarter = sf::getModel("Quarters")->selectByItemId($ids[$i]);
                if($quarter->isNew()) continue;
                if($quarter->getGuide()->isEnd()){
                    $this->refresh('时间已截止，不允许执行该操作！','error');
                }
                $quarter->setStatement(12);//退回
                $quarter->save();
                addHistory($quarter->getItemId(),'报表退回！其原因是：<br />'.input::getInput("post.content"),'quarter');
            }
            $this->refresh('已退回'.count($ids).'个报表！','success');
        }
        //原因表单
        $form = Form::load('quarter/office/doBackSelected')
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因：'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改！'))
            ->addItem(Form::hidden('id',input::mix('id')))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");

    }

    public function doDelete()
    {
        $quarter = sf::getModel("Quarters")->selectByItemId(input::mix("id"));
        if($quarter->isNew()) $this->refresh(lang::get('The project is not found!'),'error');
        if($quarter->getGuide()->isEnd()){
            $this->refresh('时间已截止，不允许执行该操作！','error');
        }
        if(input::getInput("post.content")){
            $quarter->delete();
            addHistory($quarter->getItemId(),'报表删除！其原因是：<br />'.input::getInput("post.content"),'quarter');
            $this->refresh('已删除','success');
        }
        //原因表单
        $form = Form::load('quarter/office/doDelete')
            ->addItem(Form::Label('平台名称：'.$quarter->getSubject())
                ->setAttribute('class','col-md-12')
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'删除原因'])->setWidth(12)->setAttribute('class','no-margin'))
            ->addItem(Form::hidden('id',$quarter->getItemId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }



    public function submit_stat()
    {
        $guide = sf::getModel('QuarterGuides',input::getMix('guideid'));
        if($guide->isNew()) $this->page_debug('找不到该报表类型');
        $types = get_select_data('platform_type');
        view::set('types',$types);
        view::set('guide',$guide);
        view::apply("inc_body",'quarter/office/submit_stat');
        view::display('page');
    }

    public function stat()
    {
        $pagers = sf::getModel('QuarterGuides')->getPager("enabled = 1","order by id desc");
        view::set('pagers',$pagers);
        view::apply("inc_body",'quarter/office/stat');
        view::display('page');
    }

    public function stat_type()
    {
        $guide = sf::getModel('QuarterGuides',input::getMix('guideid'));
        if($guide->isNew()) $this->page_debug('找不到该报表类型');
        //保存导出条件
        $addSql = 'ORDER BY updated_at DESC ';
        $addWhere = " `guide_id` = '".$guide->getId()."'";
        //保存标记
        $_SESSION['hash'] = $this->hash;
        $_SESSION['quarters']['baseSql'] = base64_encode($addWhere);
        //打印
        $_SESSION['quarters']['sqlStr'] = base64_encode($addWhere);
        $_SESSION['quarters']['orderStr'] = base64_encode($addSql);

        $types = get_select_data('platform_type');
        $tpl = 'quarter/office/stat_type';
        view::set('guide',$guide);
        view::set('types',$types);
        view::apply("inc_body",$tpl);
        view::display('page');
    }

    public function stat_property()
    {
        $guide = sf::getModel('QuarterGuides',input::getMix('guideid'));
        if($guide->isNew()) $this->page_debug('找不到该报表类型');
        $propertys = get_select_data('property');
        $tpl = 'quarter/office/stat_property';
        view::set('guide',$guide);
        view::set('propertys',$propertys);
        view::apply("inc_body",$tpl);
        view::display('page');
    }

    public function stat_area()
    {
        $departments = sf::getModel('Departments')->selectAll("`type` = 9","order by id asc");
        $tpl = 'quarter/office/stat_area';
        if(input::getMix('guideid')>=15){
            $tpl = 'quarter/office/stat_area_2020';
        }
        if(input::getMix('guideid')>=18){
            $tpl = 'quarter/office/stat_area_202002';
        }
        view::set('departments',$departments);
        view::set('guideid',input::getMix('guideid'));
        view::set('isHightech',intval(input::getMix('is_hightech')));
        view::set('isService',intval(input::getMix('is_service')));
        view::apply("inc_body",$tpl);
        view::display('page');
    }

    public function stat_industry()
    {
        $guide = sf::getModel('QuarterGuides',input::getMix('guideid'));
        if($guide->isNew()) $this->page_debug('找不到该报表类型');
        $industrys = get_select_data('industry');
        $tpl = 'quarter/office/stat_industry';
        view::set('industrys',$industrys);
        view::set('guide',$guide);
        view::apply("inc_body",$tpl);
        view::display('page');
    }


}