<?php
namespace App\Controller\baseinfo;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class company extends BaseController
{
    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $this->gird('baseinfo/company/wait_list',"`corporation_id` = '".input::session('roleuserid')."' and statement=2");
    }

    public function submit_list()
    {
        $this->gird('baseinfo/company/submit_list',"`corporation_id` = '".input::session('roleuserid')."' and statement>=5");
    }

    public function back_list()
    {
        $this->gird('baseinfo/company/back_list',"`corporation_id` = '".input::session('roleuserid')."' and statement IN (3,6)");
    }

    public function gird($tpl = 'baseinfo/company/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        $form_vars = array('subject','corporation_name');
        view::set("pagers",sf::getModel('Platforms')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput('mix.id'));
        //如果项目不存在
        if($platform->isNew()) $this->page_debug(lang::get('The project is not found!'));
        $platform->setStatement(5);
        $platform->save();
        sf::getModel("historys")->addHistory($platform->getPlatformId(),'依托单位已审核','platform');//保存历史记录
        $this->page_debug('已审核',getFromUrl());
    }


    /**
     * 驳回
     */
    function doRejected()
    {
        $platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.id"));
        if($platform->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $platform->setStatement(3);
            $platform->save();
            sf::getModel("historys")->addHistory($platform->getPlatformId(),'依托单位退回！'.'<br/>'.input::getInput("post.content"),'platform');
            $this->refresh('已退回！');
        }
        view::set("platform",$platform);
        view::apply("inc_body","baseinfo/company/note");
        view::display("page_blank");
    }


}