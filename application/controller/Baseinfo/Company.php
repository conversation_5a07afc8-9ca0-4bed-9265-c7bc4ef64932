<?php
namespace App\Controller\Baseinfo;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Company extends BaseController
{
    public function index()
    {
        $this->gird('baseinfo/company/index',"`corporation_id` = '".input::session('roleuserid')."'");
    }

    public function wait_list()
    {
        $this->gird('baseinfo/company/wait_list',"`corporation_id` = '".input::session('roleuserid')."' and statement=2");
    }

    public function submit_list()
    {
        $this->gird('baseinfo/company/submit_list',"`corporation_id` = '".input::session('roleuserid')."' and statement>=5");
    }

    public function back_list()
    {
        $this->gird('baseinfo/company/back_list',"`corporation_id` = '".input::session('roleuserid')."' and statement IN (3,6)");
    }

    public function gird($tpl = 'baseinfo/company/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        $form_vars = array('subject','corporation_name');
        view::set("pagers",sf::getModel('Platforms')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    /**
     * 受理
     */
    public function doAccept()
    {
        $platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.id"));
        if($platform->isNew()) $this->error('找不到该平台！');
        if(input::getInput("post.content")){
            $platform->setStatement(9);
            $platform->save();
            sf::getModel("Historys")->addHistory($platform->getPlatformId(),'依托单位已审核！<br/>'.input::getInput("post.content"),'platform');
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
        }
        view::set("platform",$platform);
        view::set("msg","审核通过！");
        view::apply("inc_body","baseinfo/company/note_submit");
        view::display("page_blank");
    }


    /**
     * 驳回
     */
    function doRejected()
    {
        $platform = sf::getModel("Platforms")->selectByPlatformId(input::getInput("mix.id"));
        if($platform->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput("post.content")){
            $platform->setStatement(3);
            $platform->save();
            sf::getModel("historys")->addHistory($platform->getPlatformId(),'依托单位退回！'.'<br/>'.input::getInput("post.content"),'platform');
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/已退回！';</script>");
        }
        view::set("platform",$platform);
        view::apply("inc_body","baseinfo/company/note");
        view::display("page_blank");
    }


}