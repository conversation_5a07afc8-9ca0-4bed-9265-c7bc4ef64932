<?php
namespace App\Controller\Register;
use App\Controller\BaseController;
use App\Models\BaseModel;
use App\Models\User;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use Sofast\Core\Config;

class Company extends BaseController
{
	public $auth = array('step1','step2','valid');
	
	function index()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()) $this->page_debug('请先登录或按照流程注册！');
		if($user->isCompany()){
			$_SESSION['userlevel'] 	=  3;
			$this->page_debug('你已经激活了单位管理员角色，请直接完善你的资料即可！',site_url("unit/profile/edit"));
		}
		$company = sf::getModel("Corporations")->selectByUserId('');
		if(input::getInput("post"))
		{
			$this->checkCsrf();
            if(input::session("SafetyCode") != input::post("safe_code")) $this->error("安全校验码不正确",getFromUrl());
            if(strlen(input::post("subject")) < 5) $this->error("请正确填写单位名称!",getFromUrl());
            if(strlen(input::post("department_id")) < 36) $this->error("请正确选择主管部门!",getFromUrl());
            if(strlen(input::post("principal")) < 2) $this->error("法人姓名不正确!",getFromUrl());
			if($company->ishas(input::getInput("post.code")))
				$this->page_debug('社会信用代码（组织机构代码）已经注册！',getFromUrl());
			//判断主管部门
			$department = sf::getModel("Departments")->selectByUserId(input::post("department_id"));
			if($department->isNew()) $this->page_debug("主管部门不存在!",getFromUrl());

            //注册单位
            $company->setCode(input::post("code"));
            $company->setSubject(input::post("subject"));
            $company->setDepartmentId($department->getUserId());
            $company->setProperty(input::post("property"));
            $company->setType(input::post("type"));
            $company->setLevel(input::post("level"));
            $company->setPrincipal(input::post("principal"));
            //保存联系人信息
            $company->setAreaCode(input::post("area_code"));
            $company->setLinkman($user->getUserUsername());
            $company->setMobile($user->getUserMobile());
            $company->setPhone($user->getUserPhone());
            $company->setLinkmanEmail($user->getUserEmail());
            $company->setPostalcode(input::post("postalcode"));
            $company->setLinkmanFax(input::post("linkman_fax"));
            $company->setAddress(input::post("address"));
            $company->setBankName(input::post("bank_name"));
            $company->setBankId(input::post("bank_id"));
            $company->setBedcount(input::getInput("post.bedcount"));
            $company->setPersoncount(input::getInput("post.personcount"));
            $company->setTechcount(input::getInput("post.techcount"));
            $company->setManagecount(input::getInput("post.managecount"));
            $company->setFloorage(input::getInput("post.floorage"));
            $company->setManagerUserId($user->getUserId());
            $company->setIsLock(9);	//注册完成
            $company->save();
            //关联角色
            $user->setRole(3,$company->getUserId());

			$_SESSION['userlevel'] 	=  3;
			$_SESSION['roleuserid'] =  $company->getUserId();
			$this->page_debug('激活成功，但还需要继续完善资料...',site_url("unit/profile/base"));
		}
		
		view::set("company",$company);
		view::apply("inc_body","Register/Company/Index");
		view::display("Register/Page");
	}
	
	function step1()
	{
		if(input::post())
		{
			$_SESSION['register']['data'] = input::post();
			$this->checkCsrf();
			if(input::session("SafetyCode") != input::post("safe_code")) $this->error("安全校验码不正确",getFromUrl());
			if(!isCreditCode(input::post("code"))) $this->error("社会信用代码无效！",getFromUrl());
//            if(Evaluate(input::getInput("post.password")) < 4){
//                $this->error('密码不符合强度要求，密码必须超过8位，至少同时包含字母和数字！',getFromUrl());
//            }
            $_company = sf::getModel('Companys')->selectByCode(input::getMix('code'));
//            if($_company->isNew()){
//                $this->page_debug('单位不在名单范围内！',getFromUrl());
//            }

			$company = sf::getModel("Corporations")->selectByUserId('');
			if($company->ishas(input::post("code")))
				$this->page_debug('社会信用代码已经注册！',getFromUrl());

			//数据验证
			if(strlen(input::post("subject")) < 5) $this->error("请正确填写单位名称!",getFromUrl());
			if(strlen(input::post("department_id")) < 36) $this->error("请正确选择主管部门!",getFromUrl());
			if(strlen(input::post("principal")) < 2) $this->error("法人姓名不正确!",getFromUrl());
			if(!isIdcard(input::post("linkman_idcard"))) $this->error("请正确填写身份证号码！",getFromUrl());
			if(!isMobile(input::post("mobile"))) $this->error("请正确填写手机号码！",getFromUrl());
			if(input::post("email") && !isEmail(input::post("email"))) $this->error("请正确填写电子邮箱！",getFromUrl());
			$department = sf::getModel("Departments")->selectByUserId(input::post("department_id"));
			if($department->isNew()) $this->error("主管部门不存在!",getFromUrl());
			//注册管理员账号
            $password = 'abc'.mt_rand(10000,99999);     //随机设置密码
			if($user = sf::getModel("Users")->create(input::post("linkman"),$password,input::post("linkman_idcard"),input::post("mobile"),input::post("email"),input::post("phone")))
			{
				//注册单位
				$company->setCode(input::post("code"));
				$company->setUnitId($_company->getUnitId());
                if($_company->isNew()){
                    $company->setSubject(input::post("subject"));
                }else{
                    $company->setSubject($_company->getSubject());
                }
				$company->setDepartmentId($department->getUserId());
				$company->setProperty(input::post("property"));
				$company->setType(input::post("type"));
				$company->setLevel(input::post("level"));
				$company->setPrincipal(input::post("principal"));
				//保存地区信息
                if($areaCode = input::post("area_code")){
                    $region = sf::getModel("Region")->selectByCode($areaCode);
                    if($region->isNew()) $this->error("所属地区不存在!",getFromUrl());
                    $company->setArea($region->getFullRegionName());
                    $company->setAreaCode($areaCode);
                    $company->setDistrict($region->getDistrict());
                }
                //保存联系人信息
				$company->setLinkman(input::post("linkman"));
				$company->setMobile(input::post("mobile"));
				$company->setPhone(input::post("phone"));
				$company->setLinkmanEmail(input::post("email"));
				$company->setPostalcode(input::post("postalcode"));
				$company->setLinkmanFax(input::post("linkman_fax"));
				$company->setLinkmanDepartment(input::post("linkman_department"));
				$company->setLinkmanDuty(input::post("linkman_duty"));
				$company->setAddress(input::post("address"));
				$company->setBankName(input::post("bank_name"));
				$company->setBankId(input::post("bank_id"));
                $company->setBedcount(input::getInput("post.bedcount"));
                $company->setPersoncount(input::getInput("post.personcount"));
                $company->setTechcount(input::getInput("post.techcount"));
                $company->setManagecount(input::getInput("post.managecount"));
                $company->setFloorage(input::getInput("post.floorage"));
				$company->setManagerUserId($user->getUserId());
				$company->setIsLock(9);	//注册完成
				$company->save();
				//关联角色
				$user->setRole(3,$company->getUserId());
				//自动登陆
//				$_SESSION['id'] 	=  $user->getId();
//				$_SESSION['userlevel'] 	=  3;
//				$_SESSION['userid'] 	=  $user->getUserId();
//				$_SESSION['username'] 	=  $user->getUserName();
//				$_SESSION['nickname'] 	=  $user->getUserUsername();
//				$_SESSION['roleuserid'] =  $company->getUserId();
                $_SESSION['register']['step'] = 2;
                $this->success('注册成功！',site_url("register/company/step2/id/".$company->getUserId()));
			}
		}
		
		//默认为法人创建账号
		$_SESSION['register']['data']['create_account'] = 0;
		view::set("data",$_SESSION['register']['data']);
		view::apply("inc_body","Register/Company/Step1");
		view::display("Register/Page");
	}
	
	function step2()
	{
	    if($_SESSION['register']['step']!=2){
	        $this->error('请先完成第一步',site_url('register/company/step1'));
        }
	    unset($_SESSION['register']);
        $company = sf::getModel("Corporations")->selectByUserId(input::mix("id"));
        view::set("company",$company);
        view::apply("inc_body","register/company/step2");
        view::display("register/page");
	}
	
	function valid()
	{
		@header('Content-type: application/json');
		$message = '';
		if(input::post("safe_code")){//验证验证码
			if(input::session("SafetyCode") != input::post("safe_code"))
			{
				$valid = false;
				$message = '安全校验码不正确请重新输入';	
			}else $valid = true;	
		}else if(input::post("code")){
			if(!isCreditCode(input::post("code"))){
				$valid = false;
				$message = '输入内容不是有效的社会信用代码,请重新输入。';		
			}else{
                $valid = true;
//                $_company = sf::getModel('Companys')->selectByCode(input::post('code'));
//                if($_company->isNew()){
//                    $message = '单位不在允许注册的名单范围内！';
//                    $valid = false;
//                }
				if(sf::getModel("Corporations")->isHas(input::post("code"))){
					$message = '社会信用代码已经注册，不能重复注册!';
					$valid = false;
				}
			}
		}else if(input::post("mobile")){
			if(!isMobile(input::post("mobile"))){
				$valid = false;
				$message = '手机号码无效，请重新输入';			
			}else if(sf::getModel("Users")->hasByMobile(input::post("mobile"))){
				$message = '该手机号码已经注册，如果忘记密码请使用找回密码功能！';
				$valid = false;
			}else $valid = true;
		}else if(input::post("linkman_idcard")){//验证身份证
			if(!isIdcard(input::post("linkman_idcard"))){
				$valid = false;
				$message = '输入内容不是有效的身份证号码';
			}else if(sf::getModel("Users")->hasByCardId(input::post("linkman_idcard"))){
				$message = '证件号码已经注册不能重复注册，请使用找回密码功能找回账号和密码！';
				$valid = false;
			}else $valid = true;
		}else if(input::post("email")){
			if(!isEmail(input::post("email"))){
				$valid = false;
				$message = '输入内容不是有效的电子邮箱地址';
			}elseif(sf::getModel("Users")->hasByEmail(input::post("email"))){
				$valid = false;
				$message = '该电子邮箱已经注册过了，你可以换一个电子邮箱试一试，如果忘记密码请使用找回密码功能。';	
			}else $valid = true;	
		}
		
		
		exit(json_encode(array(
			'valid'   => $valid,
			'message' => $message
		)));	
	}
	
}
?>