<?php
namespace App\Controller\Register;
use App\Controller\BaseController;
use App\Models\BaseModel;
use App\Models\User;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use Sofast\Core\Config;

class Expert extends BaseController
{
    public $auth = array('step1','step2','valid');

	function index()
	{
        $this->error('已关闭注册！');
		$user = sf::getModel("Users")->selectByUserId(input::session("userid"));
		if($user->isNew()) $this->page_debug('请先登录或按照流程注册！');
		//只能是身份证的才能激活专家
		if(!isIdcard($user->getUserIdcard())){
			$this->page_debug('你的账号不能激活专家角色！原因是：你的证件号码不是身份证号！');	
		}
		if($user->isExpert()){
			$_SESSION['userlevel'] 	=  10;
			$this->page_debug('你已经激活了专家角色，请直接完善你的资料即可！',site_url("expert/profile/edit"));
		}
		$expert = sf::getModel("Experts")->selectByUserId(input::session("roleuserid"));
		if(input::getInput("post"))
		{
			$expert->setUserName($user->getUserUsername());
			$expert->setCardName($user->getUserIdcardType());
			$expert->setCardId($user->getUserIdcard());
			$expert->setUserBirthday(sf::getLib('idcard')->getBirthday($user->getUserIdcard()));
			$expert->setUserSex(sf::getLib('idcard')->getSex($user->getUserIdcard()));
            $expert->setAge(sf::getLib('idcard')->getAge($user->getUserIdcard()));
            $expert->setUserEmail($user->getUserEmail());
            $expert->setUserMobile($user->getUserMobile());
            if(input::getInput('post.title_type')){
                $work = sf::getModel('CategoryWorks',input::post('title_type'));
                $expert->setTitleType($work->getSubject());
            }
            $expert->setTitle(input::post("title"));
            $expert->setMajor(input::post("major"));
            $expert->setAreaCode(input::post("area_code"));
            $expert->setUnitPhone(input::post("phone"));
			$expert->setSubjectNames(input::getInput("post.subject"));
            $expert->setUserDuty(input::post("user_duty"));
            $expert->setWorkDepartment(input::post("work_department"));
            $expert->setIsLock(9);	//注册完成
            $expert->setUserGrade('D');
            $expert->setCorporationId('');
            $expert->setWorkUnit(input::post("corporation_name"));
            if(input::post("corporation_id")){
                $company = sf::getModel("Corporations")->selectByUserId(input::post("corporation_id"));
                if($company->isNew()) $this->error("工作单位不存在，请重新选择!",getFromUrl());
                $expert->setCorporationId(input::post("corporation_id"));
                $expert->setWorkUnit($company->getSubject());
            }
            $expert->save();
			$user->setRole(10,$expert->getUserId());
			$_SESSION['userlevel'] 	=  10;
			$_SESSION['roleuserid'] =  $expert->getUserId();
			$this->page_debug('激活成功，但还需要继续完善资料...',site_url("expert/profile/base"));
		}
		
		view::set("user",$user);
		view::set("expert",$expert);
		view::apply("inc_body","Register/Expert/Index");
		view::display("Register/Page");
	}

    /**
     * 注册第一步
     */
    function step1()
    {
        $this->error('已关闭注册！');
        if(input::post("user_idcard"))
        {
            $_SESSION['register']['data'] = input::post();
            $this->checkCsrf();
            if(input::session("SafetyCode") != input::post("safe_code")) $this->page_debug("安全校验码不正确",getFromUrl());
            //检查是否已经存在
            $expert = sf::getModel("Experts")->selectByIdcard(trim(input::post("user_idcard")));
            if(!$expert->isNew()) $this->error("身份证已经注册！如果忘记密码请使用找回密码功能！",getFromUrl());
            //数据校验
            if(strlen(input::post("user_name")) < 2) $this->error("请正确填写姓名！",getFromUrl());
            if(!isIdcard(input::post("user_idcard"))) $this->error("请正确填写身份证号码！",getFromUrl());
            if(!isMobile(input::post("user_mobile"))) $this->error("请正确填写手机号码！",getFromUrl());
            if(input::post("user_email") && !isEmail(input::post("user_email"))) $this->error("请正确填写电子邮箱！",getFromUrl());
            $expert->setCorporationId('');
            $expert->setWorkUnit(input::post("corporation_name"));
            if(input::post("corporation_id")){
                $company = sf::getModel("Corporations")->selectByUserId(input::post("corporation_id"));
                if($company->isNew()) $this->page_debug("工作单位不存在，请重新选择!",getFromUrl());
                $expert->setCorporationId(input::post("corporation_id"));
                $expert->setWorkUnit($company->getSubject());
            }
            //注册专家信息
            $expert->setUserName(input::post("user_name"));
            $expert->setCardName('身份证');
            $expert->setCardId(input::post("user_idcard"));
            $expert->setUserBirthday(sf::getLib('idcard')->getBirthday(input::post("user_idcard")));
            $expert->setUserSex(sf::getLib('idcard')->getSex(input::post("user_idcard")));
            $expert->setAge(sf::getLib('idcard')->getAge(input::post("user_idcard")));
            $expert->setUserEmail(input::post("user_email"));
            $expert->setUserMobile(input::post("user_mobile"));
            $expert->setUnitPhone(input::post("phone"));
            $expert->setEducation(input::post("education"));
            if(input::getInput('post.title_type')){
                $work = sf::getModel('CategoryWorks',input::post('title_type'));
                $expert->setTitleType($work->getSubject());
            }
            $expert->setTitle(input::post("title"));
            $expert->setMajor(input::post("major"));
            $expert->setAreaCode(input::post("area_code"));
            $expert->setUnitPhone(input::post("phone"));
            $expert->setSubjectNames(input::post("subject"));
            $expert->setUserDuty(input::post("user_duty"));
            $expert->setWorkDepartment(input::post("work_department"));
            $expert->setIsLock(9);	//注册完成
            $expert->setUserGrade('D');
            $expert->save();

            //创建登陆账号
            $password = 'abc'.mt_rand(10000,99999);     //随机设置密码
            if($user = sf::getModel("Users")->create(input::post("user_name"),$password,input::post("user_idcard"),input::post("user_mobile"),input::post("user_email"),input::post("phone")))
            {
                $user->setRole(10,$expert->getUserId());
                $_SESSION['register']['step'] = 2;
                $this->success('注册成功！',site_url("register/expert/step2/id/".$expert->getUserId()));
            }
            $this->page_debug('创建账号失败！',getFromUrl());
        }

        //默认为法人创建账号
        $_SESSION['register']['data']['create_account'] = 1;
        view::set("data",$_SESSION['register']['data']);
        view::apply("inc_body","Register/Expert/Step1");
        view::display("Register/Page");
    }

    function step2()
    {
        if($_SESSION['register']['step']!=2){
            $this->error('请先完成第一步',site_url('register/expert/step1'));
        }
        unset($_SESSION['register']);
        $expert = sf::getModel("Experts")->selectByUserId(input::mix("id"));
        view::set("expert",$expert);
        view::apply("inc_body","register/Expert/step2");
        view::display("register/page");
    }

	function valid()
	{
		@header('Content-type: application/json');
		$message = '';
		if(input::getInput("post.user_idcard")){//验证身份证
			if(input::getInput("post.user_idcard_type") != '护照号' && !isIdcard(input::getInput("post.user_idcard"))){
				$valid = false;
				$message = '输入内容不是有效的身份证号码';
			}else if(sf::getModel("Users")->hasByCardId(input::getInput("post.user_idcard"))){
				$message = '证件号码已经注册不能重复注册，请使用找回密码功能找回账号和密码！';
				$valid = false;
			}else $valid = true;
		}else if(input::getInput("post.user_name")){//登陆名
			if(sf::getModel("Users")->hasByUserName(input::getInput("post.user_name"))){
				$valid = false;
				$message = '登陆账号已经被占用，请更换一个登陆账号试试';	
			}else $valid = true;	
		}else if(input::getInput("post.user_email")){
			if(!isEmail(input::getInput("post.user_email"))){
				$valid = false;
				$message = '输入内容不是有效的电子邮箱地址';
			}elseif(sf::getModel("Users")->hasByEmail(input::getInput("post.user_email"))){
				$valid = false;
				$message = '该电子邮箱已经注册过了，你可以换一个电子邮箱试一试，如果忘记密码请使用找回密码功能。';	
			}else $valid = true;	
		}else if(input::getInput("post.user_mobile")){
			if(!isMobile(input::getInput("post.user_mobile"))){
				$valid = false;
				$message = '手机号码无效，请重新输入';			
			}else if(sf::getModel("Users")->hasByMobile(input::getInput("post.user_mobile"))){
				$message = '该手机号码已经注册，如果忘记密码请使用找回密码功能！';
				$valid = false;
			}else $valid = true;
		}
		
		exit(json_encode(array(
			'valid'   => $valid,
			'message' => $message
		)));	
	}
	
}
?>