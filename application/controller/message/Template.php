<?php
namespace App\Controller\Message;
use App\Controller\BaseController;
use Sofast\Core\Sf;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Config;

class Template extends BaseController
{	
	/**
	 * 短信综合查询
	 */
	function index()
	{
		$this->grid("1","index");
	}
	
	/**
	 * 编辑短信
	 */
	function edit()
	{
		$template = sf::getModel("MessageTemplates",input::getInput("mix.id"));
		if(input::getInput("post.subject"))
		{
			$template->setSubject(input::getInput("post.subject"));
			$template->setType(input::getInput("post.type"));
			$template->setMessage(input::getInput("post.message"));
			$template->setNote(input::getInput("post.note"));
			$template->setUserId(input::getInput("post.role") ? 'ALL' : input::getInput("session.userid"));
			$template->setUserName(input::getInput("session.nickname"));
			$template->setUpdatedAt(date("Y-m-d H:i:s"));
			$template->save();
			exit("<script>parent.location.reload();</script>");
		}
		view::set("template",$template);
		view::apply("inc_body","message/template/edit");
		view::display("page");
	}
	
	/**
	 * 删除短信
	 */
	function delete()
	{
		$template = sf::getModel("MessageTemplates",input::getInput("mix.id"));
		if($template->isNew() || $template->getUserId() != input::getInput("session.userid"))
			$this->page_debug(lang::get("You do not have permission to visit!"),getFromUrl()); 
		$template->delete();
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}
	
	/**
	 * 删除短信
	 */
	function grid($addWhere='1',$tpl='index')
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//处理搜索
		input::getInput("post.search") && $addWhere .= " AND `".input::getInput("post.field")."` LIKE '%".trim(input::getInput("post.search"))."%' ";
		input::getInput("post.type") && $addWhere .= " AND `type` LIKE '%".trim(input::getInput("post.type"))."%' ";
		//分级处理
		!in_array(input::getInput("session.username"),config::get('super')) && $addWhere .= " AND (user_id = 'ALL' OR user_id = '".input::getInput("session.userid")."') ";
		//取得带翻页的数据集
		view::set("pager",sf::getModel("MessageTemplates")->getPager($addWhere ,$addSql ,30));
		view::apply("inc_body","message/template/".$tpl);
		view::display("page");
	}
	
}