<?php
namespace App\controller\user;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Stage extends BaseController
{

    /**
     * 新增中期报告
     * @return void
     */
    function create()
    {
        $this->jump(site_url('engine/stager/create'));
    }
    /**
     * 填写中的中期报告
     * @return void
     */
    function index()
    {
        $this->mygrid("user/stage/index","user_id = '".input::getInput("session.roleuserid")."' and worker_id > 0 and `statement` IN ('-1','0','1','3','6','12') and project_id in (select project_id from projects where stage_open = 1)");
    }

    /**
     * 上报的
     */
    public function submit_list()
    {
        $this->mygrid("user/stage/submit_list","user_id = '".input::getInput("session.roleuserid")."' AND `statement` IN (2,5,9,10)  and worker_id > 0");
    }

    /**
     * 已审核的
     */
    public function accept_list()
    {
        $this->mygrid("user/stage/accept_list","user_id = '".input::getInput("session.roleuserid")."' AND `statement` = 10 and worker_id > 0");
    }

    /**
     * 历史项目上传任务书
     */
    public function history_list()
    {
        $this->mygrid("user/task/history_list","(user_id = '".input::getInput("session.roleuserid")."' or `linkman_id` = '".input::getInput("session.roleuserid")."') AND `statement` = 29 AND `state_for_plan_book` < 2 AND is_subsidy = 0 and radicate_year < 2010 and radicate_year > 2000 ");
    }

    /**
     * 上报项目
     */
    function doSubmit()
    {
        $stage = sf::getModel("Stages")->selectByStageId(input::getInput("mix.id"));
        if(input::session('roleuserid') != $stage->getUserId() || $stage->isNew())
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        $msg = $stage->validate();
        if(input::post("id"))
        {
            if(count($msg)) $this->page_debug("请按照提示修改完成后再次上报。",getFromUrl());
            $stage->setStatement(2);
            $stage->setDeclareAt(date('Y-m-d H:i:s'));
            $stage->save();
            $stage->setConfigs("file.apply",'');//清空生成的
            sf::getModel("Historys")->addHistory($stage->getStageId(),'中期评估上报','stage');
            $this->success(lang::get("Has been submit!"),site_url("user/stage/index"));
        }

        $data['msg'] = $msg;
        $data['stage'] = $stage;
        view::set($data);
        view::apply("inc_body","user/stage/submit_do");
        view::display("page_main");
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function mygrid($tpl = 'user/stage/index',$addWhere = '1',$showMax=25,$page='page_main')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".input::getInput("mix.department_id")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";

        //显示页面
        $form_vars = array('search','declare_year','department_id','statement','field');
        view::set("pager",sf::getModel("Stages")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }



}