<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
use App\Models\Personnel as PersonnelModel;
use App\Models\UserGroup;
use App\Models\UserRole;
class user extends BaseController
{

    /**
	 * 数据列表
	 */
	function index()
	{
		$user = sf::getModel("Managers");
		$addWhere = $addSql = '';
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//$addWhere .= "`user_group_id` NOT IN ('4','13','14','15') ";
		
		$addWhere .= '1';
		input::getInput("post.search") && $addWhere .= " AND ".trim(input::getInput("post.field"))." like '%".input::getInput("post.search")."%' ";
		input::getInput("post.office_id") && $addWhere .= " AND office_id = '".input::getInput("post.office_id")."' ";
		if(input::session('userlevel')!=1){
            $addWhere .= " AND id > 1 ";
        }
		view::set("pager",$user->getPager($addWhere ,$addSql ,20));
		view::apply("inc_body","admin/user/index");
		view::display("page_main");
	}

	/**
	 * 数据列表
	 */
	// function index()
	// {
	// 	$user = sf::getModel("managers");
	// 	$addWhere = $addSql = '';
	// 	$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
	// 	$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
	// 	$addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
	// 	$addWhere .= "`user_group_id` NOT IN ('4','13','14','15') ";

	// 	input::getInput("post.search") && $addWhere .= "AND ".trim(input::getInput("post.field"))." like '%".input::getInput("post.search")."%' ";
	// 	input::getInput("post.office_id") && $addWhere .= "AND office_id = '".input::getInput("post.office_id")."' ";

	// 	view::set("pager",$user->getPager($addWhere ,$addSql ,20));
	// 	view::apply("inc_body","admin/user/index");
	// 	view::display("page");
	// }
	
	/**
	 * 数据编辑
	 */
	function edit()
	{
		$user = sf::getModel("Personnels")->selectByUserId(input::getInput("mix.id"));
		$manager = sf::getModel("Managers")->selectByUserId(input::getInput("mix.id"));
		if(input::getInput("post.user_username"))
		{
			//判断是否重复
			if($user->hasUser(input::getInput("post.user_name")))
				$this->page_debug(lang::get('The user is exist!'),getFromUrl());
			//账号信息
			input::getInput("post.user_name") && $user->getUser()->setUserName(input::getInput("post.user_name"));
			input::getInput("post.user_name") && $manager->setUserName(input::getInput("post.user_name"));
			input::getInput("post.user_password") && $user->getUser()->setUserPassword(input::getInput("post.user_password"));
			$user->getUser()->setIsLock(input::getInput("post.is_lock") ? 4 : 0);

			//资料/账号信息
			input::getInput("post.user_username") && $user->getUser()->setUserUsername(input::getInput("post.user_username"));
			input::getInput("post.user_username") && $manager->setUserUsername(input::getInput("post.user_username"));
			input::getInput("post.user_email") && $user->getUser()->setUserEmail(input::getInput("post.user_email"));
			input::getInput("post.user_email") && $manager->setUserEmail(input::getInput("post.user_email"));
			input::getInput("post.user_mobile") && $user->getUser()->setUserMobile(input::getInput("post.user_mobile"));
			input::getInput("post.user_mobile") && $manager->setUserMobile(input::getInput("post.user_mobile"));
			input::getInput("post.user_idcard") && $user->getUser()->setUserIdcard(input::getInput("post.user_idcard"));

			input::getInput("post.user_username") && $user->setName(input::getInput("post.user_username"));
			input::getInput("post.user_email") && $user->setEmail(input::getInput("post.user_email"));
			input::getInput("post.user_mobile") && $user->setMobilephone(input::getInput("post.user_mobile"));
			input::getInput("post.user_idcard") && $user->setIdcard(input::getInput("post.user_idcard"));
			$user->setCreatedAt(date('Y-m-d H:i:s'));
			//资料信息
			input::getInput("post.note") && $user->setNote(input::getInput("post.note"));
			input::getInput("post.office_id") && $user->setOfficeId(input::getInput("post.office_id"));
			input::getInput("post.office_id") && $manager->setOfficeId(input::getInput("post.office_id"));
			input::getInput("post.group_id") && $user->setGroupId(input::getInput("post.group_id"));
			$user->setUpdatedAt(date("Y-m-d H:i:s"));
			$user->save();
			$manager->save();

			//设置角色
			$user->setPermission(input::getInput("post.user_group_ids"));

			$this->page_debug('保存成功！',getFromUrl(site_url("home/left"),site_url("admin/user/index")));
		}
		$groups = sf::getModel("UserGroups")->selectAll();
		$data["groups"] = $groups;
		$data["user"] = $user;
		$data['pager']  = sf::getModel("UserGroups")->selectAll("id NOT IN ('4','13','14','15')","ORDER BY id ASC");
		view::set($data);
		view::apply("inc_body","admin/user/edit");
		view::display("page_main");
	}

    /**
     * 设置权限
     */
    function auths()
    {
        $user = sf::getModel("Managers")->selectByUserId(input::getInput("mix.id"));
        if(input::getInput("post"))
        {
            $auths = input::post('auths');
            $user->setOfficeId(input::post('office_id'));
            $auths['menu'] = (array)$user->getAuths('menu');
            if($menus = input::post('menu')){
                //设置菜单权限
                foreach ($menus as $id=>$v){
//                    $userMenu = sf::getModel('UserMenus',$id);
//                    if($userMenu->isNew()) continue;
                    $userMenuArr = (array)$user->getAuths('menu');
                    if($v==1){
                        if(!in_array($id,$userMenuArr)) $userIdArr[] = $id;
                    }else{
                        if(in_array($id,$userMenuArr)) {
                            $k = array_search($id,$userMenuArr);
                            unset($userMenuArr[$k]);
                        }
                    }
                    $userIdArr = array_filter($userIdArr);
                    $auths['menu'] = $userIdArr;
                }
            }
            $user->setAuths($auths);
            $user->save();
            $this->refresh();
        }
        $groups = sf::getModel("UserGroups")->selectAll();
        $topmenus = sf::getModel("UserMenus")->selectAll("`level` = 1");
        $data["groups"] = $groups;
        $data["topmenus"] = $topmenus;
        $data["user"] = $user;
        $data['pager']  = sf::getModel("UserGroups")->selectAll("id NOT IN ('4','13','14','15')","ORDER BY id ASC");
        view::set($data);
        view::apply("inc_body","admin/user/auths");
        view::display("page_blank");
    }
	
	/**
	 * 删除数据
	 */
	function delete()
	{
		if(input::getInput("post.select_id")){
			$ids = implode("','",input::getInput("post.select_id"));
		}else $ids = input::getInput("get.id"); 
		
		sf::getModel("Personnels")->remove("`user_id` in('".$ids."')");
		sf::getModel("Managers")->remove("`user_id` in('".$ids."')");
		sf::getModel("Users")->remove("`user_id` in('".$ids."')");
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}
	
	function group_list()
	{
		$user_group = sf::getModel("UserGroups");
		$addWhere = $addSql = '';
		
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		
		view::set("pager",$user_group->getPager($addWhere ,$addSql ,20));
		view::apply("inc_body","admin/user/group_list");
		view::display("page_main");
	}
	
	function group_edit()
	{
		$user_group = sf::getModel("UserGroups",input::getInput("get.id") ? input::getInput("get.id") : input::getInput("post.id"));
		if(input::getInput("post.user_group_name")){
			$user_group->setUserGroupName(input::getInput("post.user_group_name"));
			$user_group->setMark(input::getInput("post.mark"));
			if($user_group->save()) 
				$this->page_debug(lang::get("Has been saved!"),getFromUrl(site_url("home/left"),site_url("admin/user/group_list")));
		}
		
		$data['group'] = $user_group;
		view::set($data);
		view::apply("inc_body","admin/user/group_edit");
		view::display("page_main");
	}
	
	function group_delete()
	{
		if(input::getInput("post.select_id")){
			$ids = implode("','",input::getInput("post.select_id"));
		}else $ids = input::getInput("get.id"); 
		sf::getModel("UserGroups")->remove("`id` in('".$ids."')");
		$this->page_debug(lang::get("Has been deteted!"),getFromUrl());
	}

    function coupling()
    {
        $msg = [];
        $manager = sf::getModel("Managers")->selectByUserId(input::post("userid"));
        if(!$manager->isNew()){
            $data['msg'] = '该人员已是管理员';
            echo json_encode($data);exit();
        }
        $user = sf::getModel("Users")->selectByUserId(input::post("userid"));
        if($user->isNew()){
            $data['msg'] = '关联账号不存在';
            echo json_encode($data);exit();
        }
        $manager->setUserGroupId(6);
        $manager->setOfficeId(435);
        $manager->setUserName($user->getUserName());
        $manager->setUserUsername($user->getUserUsername());
        $manager->setUserMobile($user->getUserMobile());
        $manager->setCreatedAt(date('Y-m-d H:i:s'));
        $manager->setUpdatedAt(date('Y-m-d H:i:s'));
        $manager->save();
        if($manager->coupling($user->getUserId()))
        {
            sf::getModel("historys")->addHistory($user->getUserId(),"被设置成科技创新部管理员！",'users',1);
            $data['msg'] = '添加成功！';
            echo json_encode($data);exit();
        }
        $data['msg'] = '添加失败！';
        echo json_encode($data);exit();
    }

    function decoupling()
    {
        if(input::getInput("post.content")){
            $user = sf::getModel("Users")->selectByUserId(input::post("userid"));
            if($user->isNew()) exit("<script>alert('该账号不存在!');parent.location.reload();</script>");
            $manager = sf::getModel("Managers")->selectByUserId(input::post("userid"));
            if($manager->isNew()) exit("<script>alert('该管理员不存在!');parent.location.reload();</script>");

            //解除角色
            sf::getModel("UserRoles")->remove("user_role_id = '" . $manager->getUserId() . "' and role_id = '6' AND user_id = '" . $user->getUserId() . "' ");
            //删除管理员
            $manager->delete();
            sf::getModel("historys")->addHistory($user->getUserId(),"解除科技创新部管理员角色，原因是：<br />".input::getInput("post.content"),'users',1);
            exit("<script>parent.location.reload();</script>");
        }
        view::set("userid",input::getInput("mix.userid"));
        view::apply("inc_body","admin/user/decoupling");
        view::display("page_blank");
    }

    /**
     * 设置默认联系人
     *
     */
    public function setManager()
    {
        $manager = sf::getModel("Managers")->selectByUserId(input::getMix("userid"));
        if($manager->isNew()) $this->error('该管理员不存在！',getFromUrl());
        $user = sf::getModel("Users")->selectByUserId(input::getMix("userid"));
        if($user->isNew()) $this->error('关联账号不存在',getFromUrl());
        sf::getLib('Db')->exec("update managers set is_manager = 0 where id != 7");
        $manager->setIsManager(1);
        $manager->save();
        sf::getModel("historys")->addHistory($user->getUserId(),"设置为默认管理员",'users',1);
        $this->success('设置成功！',getFromUrl());
    }
}
