<?php
namespace App\controller\office;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
class Quarter extends BaseController
{		
	
	/**
	 * 上报的项目列表
	 */
	public function index()
	{
		$addWhere = "1 ";
		$this->grid('office/quarter/index',$addWhere);
	}

    /**
     * 未填写的列表
     * @return void
     */
    public function nowrite_list()
    {
        $year = input::getMix('declare_year')?:date('Y');
        $season = input::getMix('declare_season')?:ceil((date('n'))/3)-1;
        $season = (int)$season;
        $addWhere = "user_id in (select corporation_id from projects where statement = 29 and level = '国家级') and not EXISTS (select * from quarters where company_id = corporations.user_id and declare_year = '{$year}' and declare_season = '{$season}' and statement in (9,10))";
        view::set("year",$year);
        view::set("season",$season);
        $this->unit_gird($addWhere,'office/quarter/nowrite_list');
    }
	
	/**
	 * 等待审核的项目列表
	 */
	public function wait_list()
	{
        $backUrl = site_url('office/quarter/wait_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput('mix.declare_year')){
            $backUrl.='/declare_year/'.input::getInput('mix.declare_year');
        }
        if(input::getInput('mix.declare_season')){
            $backUrl.='/declare_season/'.input::getInput('mix.declare_season');
        }

        $showMax = 16;
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';

        $addwhere = "statement = 9";

        $this->grid('office/quarter/wait_list',$addwhere,$showMax);
	}
	
	/**
	 * 上报的项目列表
	 */
	public function accept_list()
	{
		$addWhere = 'statement = 10 ';
		$this->grid('office/quarter/accept_list',$addWhere);
	}
	
	/**
	 * 驳回的项目列表
	 */
	public function back_list()
	{
		$addWhere = 'statement = 12 ';
		$this->grid('office/quarter/back_list',$addWhere);
	}

    /**
     * 形审合格的项目列表
     */
    public function pass_list()
    {
        $backUrl = site_url('office/quarter/pass_list/ordermode/asc');
        $showMax = 16;
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        if(input::getInput("mix.declare_season"))
            $addWhere .= " AND `declare_season` = '".input::getInput("mix.declare_season")."' ";
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';

        $addwhere = "statement = 9 and declare_year = '".config::get('current_declare_year',date('Y'))."'";

        $this->grid('office/quarter/pass_list',$addwhere,$showMax);
    }

    /**
     * 需退回的项目列表
     */
    public function needback_list()
    {
        $backUrl = site_url('office/quarter/needback_list/ordermode/asc');
        if(input::getInput('mix.search') && input::getInput('mix.field')){
            $backUrl.='/search/'.input::getInput('mix.search').'/field/'.input::getInput('mix.field');
        }
        $showMax = 16;
        if(input::getInput('mix.showmax')){
            $showMax = intval(input::getInput('mix.showmax'));
        }
        $_SESSION['backurl'] = $backUrl;

        $_GET['orderfield'] = 'declare_at';
        $_GET['ordermode'] = 'asc';

        $addwhere = "statement = 9 and declare_year = '".config::get('current_declare_year',date('Y'))."'";

        $this->grid('office/quarter/pass_list',$addwhere,$showMax);
    }
	
	/**
	 * 受理项目
	 */
	function doAccept()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		for($i=0,$n=count($ids);$i<$n;$i++){
			$quarter = sf::getModel("Quarters")->selectByQuarterId($ids[$i]);
			//如果项目不存在
			if($quarter->isNew()) continue;
            $quarter->setStatement(10);
            $quarter->save();

			sf::getModel("historys")->addHistory($quarter->getQuarterId(),lang::get("Has been accepted!"),'quarter');//保存历史记录
		}
		$this->success('审核成功！',getFromUrl());
	}
	/**
	 * 审核项目
	 */
	function doAcceptOnlyOne()
	{
        $quarter = sf::getModel("Quarters")->selectByQuarterId(input::getInput("mix.id"));
        if($quarter->isNew()){
            $this->error('找不到该项目！');
        }
        if(input::getInput("post.content")){
            $quarter->setStatement(10);
            $quarter->save();
            $msg = lang::get("Has been accepted!");
            sf::getModel("Historys")->addHistory($quarter->getQuarterId(),$msg.'<br/>'.input::getInput("post.content"),'quarter');
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
        }
        view::set("quarter",$quarter);
        view::set("msg","同意受理！");
        view::apply("inc_body","office/quarter/note_submit");
        view::display("page_blank");
	}
	/**
	 * 驳回项目
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		for($i=0,$n=count($ids);$i<$n;$i++){
			$quarter = sf::getModel("Quarters")->selectByQuarterId($ids[$i]);
            $quarter->setStatement(12);
			$quarter->save();
			sf::getModel("historys")->addHistory($quarter->getQuarterId(),lang::get("Has been rejected!"),'quarter');
		}
		$this->page_debug(lang::get("Has been rejected!"),getFromUrl());
	}

    function doRejectedOnlyOne()
    {
        $quarter = sf::getModel("Quarters")->selectByQuarterId(input::getInput("mix.id"));
        if($quarter->isNew()) $this->error('没有找到该项目',getFromUrl());
        if(input::getInput("post.content")){
            $quarter->setStatement(12);
            $quarter->save();
            sf::getModel("historys")->addHistory($quarter->getQuarterId(),lang::get("Has been rejected!").'<br/>'.input::getInput("post.content"),'quarter');
            $backUrl = $_SESSION['backurl'] ?: site_url('office/quarter/wait_list/ordermode/asc');
            if($_SESSION['backurl']) unset($_SESSION['backurl']);
            exit("<script>top.location.href='{$backUrl}/_save/yes/_msg/退回成功！';</script>");
        }

        //原因表单
        $reason = '退回修改';
        $form = Form::load('office/quarter/doRejectedOnlyOne')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($quarter->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue($reason))
            ->addItem(Form::hidden('id',$quarter->getQuarterId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    /**
     * 集中处理项目的搜索信息
     *
     */
    public function grid($tpl = 'gather/quarter/wait_list',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".trim(input::getInput("mix.statement"))."' ";
        input::getInput("mix.department_id") && $addWhere .= " AND `department_id` = '".trim(input::getInput("mix.department_id"))."' ";
        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        if(input::getInput("mix.declare_season"))
            $addWhere .= " AND `declare_season` = '".input::getInput("mix.declare_season")."' ";


        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['quarters']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['quarters']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['quarters']['orderStr'] = base64_encode($addSql);
        }
//		dd($addWhere);
        //显示页面
        $form_vars = array('search','declare_year','declare_season','department_id','department_id','statement');

        view::set("pager",sf::getModel('Quarters')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }

    public function export()
    {
        $addWhere = base64_decode(input::getInput("session.quarters.sqlStr"));
        $addSql = base64_decode(input::getInput("session.quarters.orderStr"));

        $months = sf::getModel('Quarters')->selectAll($addWhere,$addSql);
        $bodys = [];
        $i=0;
        $monthLoopIndex = 0;
        $projectLoopIndex = 0;
        $mergeCells = [];      //要合并的单元格
        while ($month = $months->getObject()){
            $projects = $month->projects();
            if($projects->getTotal()==0){
                $bodys[$i][] = $monthLoopIndex+1;
                $bodys[$i][] = $month->getDeclareYear();
                $bodys[$i][] = $month->getDeclareSeason();
                $bodys[$i][] = $month->getCompanyName();
                $bodys[$i][] = $month->getDepartmentName();
                $bodys[$i][] = $month->getUserName();
                $bodys[$i][] = $month->getUserMobile();
                $bodys[$i][] = '';
                $bodys[$i][] = '';
                $bodys[$i][] = '';
                $bodys[$i][] = '';
                $bodys[$i][] = '';
                $bodys[$i][] = '';
                $bodys[$i][] = '';
                $bodys[$i][] = '';
                $bodys[$i][] = strip_tags($month->getState());
                $i++;
                continue;
            }

            $mergeCells['A_'.$monthLoopIndex][0] = 'A'.($i+2);           //序号
            $mergeCells['B_'.$monthLoopIndex][0] = 'B'.($i+2);           //填报年度
            $mergeCells['C_'.$monthLoopIndex][0] = 'C'.($i+2);           //填报季度
            $mergeCells['D_'.$monthLoopIndex][0] = 'D'.($i+2);           //医疗机构名称
            $mergeCells['E_'.$monthLoopIndex][0] = 'E'.($i+2);           //主管部门
            $mergeCells['F_'.$monthLoopIndex][0] = 'F'.($i+2);           //填报人
            $mergeCells['G_'.$monthLoopIndex][0] = 'G'.($i+2);           //联系电话
            $mergeCells['P_'.$monthLoopIndex][0] = 'P'.($i+2);           //审核状态

            while($project = $projects->getObject()){
                $mergeCells['H_'.$projectLoopIndex][0] = 'H'.($i+2);           //专科项目名称
                $mergeCells['N_'.$projectLoopIndex][0] = 'N'.($i+2);           //项目实施进度
                $mergeCells['O_'.$projectLoopIndex][0] = 'O'.($i+2);           //备注
                $capital = $project->getCapital();
                foreach ($capital as $k=>$money){
                    $bodys[$i][] = $monthLoopIndex+1;
                    $bodys[$i][] = $month->getDeclareYear();
                    $bodys[$i][] = $month->getDeclareSeason();
                    $bodys[$i][] = $month->getCompanyName();
                    $bodys[$i][] = $month->getDepartmentName();
                    $bodys[$i][] = $month->getUserName();
                    $bodys[$i][] = $month->getUserMobile();
                    $bodys[$i][] = $project->getSubject();
                    $bodys[$i][] = $project->getSourceName($k);
                    $bodys[$i][] = $money['radicate_money'];
                    $bodys[$i][] = $money['fund_ready'];
                    $bodys[$i][] = $money['used_money'];
                    $bodys[$i][] = $money['used_percent'];
                    $bodys[$i][] = $project->getSchedule();
                    $bodys[$i][] = $project->getNote();
                    $bodys[$i][] = strip_tags($month->getState());
                    $i++;
                }
                $mergeCells['H_'.$projectLoopIndex][1] = 'H'.($i+1);
                $mergeCells['N_'.$projectLoopIndex][1] = 'N'.($i+1);
                $mergeCells['O_'.$projectLoopIndex][1] = 'O'.($i+1);
                $projectLoopIndex++;
            }
            $mergeCells['A_'.$monthLoopIndex][1] = 'A'.($i+1);
            $mergeCells['B_'.$monthLoopIndex][1] = 'B'.($i+1);
            $mergeCells['C_'.$monthLoopIndex][1] = 'C'.($i+1);
            $mergeCells['D_'.$monthLoopIndex][1] = 'D'.($i+1);
            $mergeCells['E_'.$monthLoopIndex][1] = 'E'.($i+1);
            $mergeCells['F_'.$monthLoopIndex][1] = 'F'.($i+1);
            $mergeCells['G_'.$monthLoopIndex][1] = 'G'.($i+1);
            $mergeCells['P_'.$monthLoopIndex][1] = 'P'.($i+1);
            $monthLoopIndex++;
        }
        $head = ['序号','填报年度','填报季度','医疗机构名称','主管部门','填报人','联系电话','专科项目名称','资金来源','下达资金','是否到位','使用资金','资金执行率','项目实施进度','备注','审核状态'];
        $name = '季度报告'.date('Y-m-d_H_i_s');
        excel_out($head,$bodys,'季度报告',$name,[],'','',$mergeCells);
    }

}
?>