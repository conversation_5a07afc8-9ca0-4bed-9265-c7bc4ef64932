<?php
namespace App\Controller\Office;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class reward extends BaseController
{	
	/**
	 * 奖励综合搜索
	 * @return [type] [description]
	 */
	function index()
	{
		$this->rewardGrid('office/reward/index','statement > 0');
	}
	/**
	 * 待审核奖励
	 */
	function waitList()
	{
		$this->rewardGrid('office/reward/wait_list',' statement = 8 ');

	}
	/**
	 * 已审核奖励
	 */
	function acceptList()
	{
		$this->rewardGrid('office/reward/accept_list',' statement = 10 ');

	}
	/**
	 * 被退回奖励
	 */
	function backList()
	{
		$this->rewardGrid('office/reward/back_list',' statement = 9 ');

	}	
	/**
	 * 审核奖励
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")){
			$ids = input::getInput("post.select_id");	
		}else{
			$ids[] = input::getInput("get.id");	
		}
		if(!$ids[0])
			$this->page_debug('请至少选择一个！',getFromUrl());	
		for($i=0,$n=count($ids);$i<$n;$i++){
			$reward = sf::getModel("Rewards")->selectByRewardId($ids[$i]);
			if($reward->isNew()) continue;
			//如果存在受理编号就不再重新生成
			if(!$reward->getAcceptId()){
				$accept_id = sf::getModel('types')->getSerialNumber(date('Y'),'JL','44');
				$reward->setAcceptId($accept_id);
			}
			$reward->setStatement(10);
			$reward->save();
			sf::getModel("historys")->addHistory($reward->getrewardId(),"审核成功！编号为：".$reward->getAcceptId(),'reward');//保存历史记录
		}
		// $this->page_debug(lang::get("Has been accepted!")."受理编号为：".$project->getAcceptId(),getFromUrl());
		$this->page_debug('审核成功！',getFromUrl());
	}	
	/**
	 * 审核通过
	 */
	function doSubmitOne()
	{
		$reward = sf::getModel('rewards')->selectByRewardId(input::getInput('mix.id'));
		if($fruit_domain = input::getInput("post.fruit_domain")){
			if($reward->isNew()) $this->error('找不到该奖励');
			if(!$reward->getAcceptId()){
				$accept_id = sf::getModel('types')->getSerialNumber(date('Y'),'JL','44');
				$reward->setAcceptId($accept_id);
			}
			$reward->setFruitDomain($fruit_domain);
			$reward->setTransform(input::getInput("post.transform"));
			$reward->setBelong(input::getInput("post.belong"));
			$reward->setStatement(10);
			$reward->save();
			sf::getModel("historys")->addHistory($reward->getrewardId(),'受理科技奖励！','reward');
			exit("<script>parent.location.reload();</script>");
		}
		view::set("reward",$reward);
		view::apply("inc_body","office/reward/note_submit");
		view::display("page_blank");
	}
	/**
	 * 驳回
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")){
			$ids = input::getInput("post.select_id");
		}else{
			$this->page_debug('请至少选择一个！',getFromUrl());	
		}
		for($i=0,$n=count($ids);$i<$n;$i++){
			$reward = sf::getModel("Rewards")->selectByRewardId($ids[$i]);
			$reward->setStatement(9);
			$reward->save();
			sf::getModel("historys")->addHistory($reward->getrewardId(),'退回科技奖励！<br/>'.input::getInput("post.content"),'reward');
		}
		$this->page_debug('退回成功！',getFromUrl());	
	}	
	/**
	 * 驳回
	 */
	function doRejectedOnlyOne()
	{
		$reward = sf::getModel('rewards')->selectByRewardId(input::getInput('mix.id'));
		if(input::getInput("post.content")){
			$reward->setStatement(9);
			$reward->save();
			sf::getModel("historys")->addHistory($reward->getrewardId(),'退回科技奖励！<br/>'.input::getInput("post.content"),'reward');
			// $this->page_debug('退回成功！',getFromUrl());
			exit("<script>parent.location.reload();</script>");
		}
		view::set("reward",$reward);
		view::apply("inc_body","office/reward/note");
		view::display("page_blank");
	}			
	public function rewardGrid($tpl = 'office/reward/index',$addWhere = '1',$showMax=25,$page='page_main')
	{
        //处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
		input::getInput("mix.level") && $addWhere .= " AND level = '".input::getInput("mix.level")."'";
		input::getInput("mix.grade") && $addWhere .= " AND grade = '".input::getInput("mix.grade")."'";
        if(input::getMix('start_at') && input::getMix('end_at')){
            $addWhere .= " AND (`date` between '".input::getMix("start_at")."' and '".input::getMix("end_at")."')";
        }
        if(input::getInput("mix.first_id")){
            $addWhere .= " AND `first_id` = '".input::getInput("mix.first_id")."'";
        }elseif(strstr($addWhere,'first_id')===false){
            $addWhere .= " AND `first_id` = '9B581D83-B2BA-7337-0678-A875B156B111'";
        }
        if(input::getInput("mix.second_id") && input::getInput("mix.second_id")==input::getInput("mix.first_id")){
            $addWhere .= " AND `second_id` = '0'";
        }elseif(input::getInput("mix.second_id")){
            $addWhere .= " AND `second_id` = '".input::getInput("mix.second_id")."'";
        }
        if(input::getInput("mix.third_id") && input::getInput("mix.third_id")==input::getInput("mix.second_id")){
            $addWhere .= " AND `third_id` = '0'";
        }elseif(input::getInput("mix.third_id")){
            $addWhere .= " AND `third_id` = '".input::getInput("mix.third_id")."'";
        }
        input::getInput("mix.fourth_id") && $addWhere .= " AND `fourth_id` = '".input::getInput("mix.fourth_id")."'";
		//将搜索条件保存以备打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['rewards']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['rewards']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['rewards']['orderStr'] = base64_encode($addSql);
		}else{
			$_SESSION['queryOptions'] = '';
			$_SESSION['url_str'];
			$addWhere = base64_decode($_SESSION['rewards']['sqlStr']);
		}
		$form_vars = array('field','search','subject','user_id','reward_id','start_at','end_at','first_id','second_id','third_id','fourth_id','statement','level','grade');
		view::set("pager",sf::getModel('rewards')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);
	}
}