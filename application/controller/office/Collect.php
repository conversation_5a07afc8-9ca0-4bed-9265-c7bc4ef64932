<?php
namespace App\Controller\Office;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
/**
 * 汇总分类
 */
class collect extends BaseController
{		
	/**
	 * 评审情况汇总
	 */
	function appraise()
	{
		$addWhere = '';
		$data['year'] = input::getInput("post.year") ? input::getInput("post.year") : config::get('current_declare_year',date("Y"));
		$start_at = input::getInput("post.start_at") ? input::getInput("post.start_at") : $data['year']."-01-01 00:00:00";
		$end_at = input::getInput("post.endt_at") ? input::getInput("post.end_at") : $data['year']."-12-31 00:00:00";
		$addWhere = "";
		//其他扩展条件
		input::getInput("post.cat_id") && $addWhere .= "AND cat_id = '".input::getInput("post.cat_id")."' ";
		input::getInput("post.type_id") && $addWhere .= "AND type_id = '".input::getInput("post.type_id")."' ";
		input::getInput("post.office_id") && $addWhere .= "AND office_id = '".input::getInput("post.office_id")."' ";
		input::getInput("post.department_id") && $addWhere .= "AND department_id = '".input::getInput("post.department_id")."' ";
					
		$db = sf::getLib("db");
		$query = $db->query("SELECT is_wait FROM projects WHERE group_id IN (SELECT id FROM project_group WHERE group_year = '".$data['year']."' AND unix_timestamp(created_at) > unix_timestamp('".$start_at."') AND unix_timestamp(created_at) < unix_timestamp('".$end_at."')) ".$addWhere);
		while($row = $db->fetch_array($query))
		{
			$data['t'] += 1;
			if($row['is_wait'] == 1) $data['in'] += 1;
			elseif($row['is_wait'] == 2) $data['out'] += 1;
			else $data['w'] += 1;
		}
		
		view::set($data);
		view::apply("inc_body","office/collect/appraise");
		view::display("page");
	}
	
	/**
	 * 评审情况汇总
	 */
	function show()
	{
		$addWhere = '';
		$data['year'] = input::getInput("post.year") ? input::getInput("post.year") : config::get('current_declare_year',date("Y"));
		$start_at = input::getInput("post.start_at") ? input::getInput("post.start_at") : $data['year']."-01-01 00:00:00";
		$end_at = input::getInput("post.endt_at") ? input::getInput("post.end_at") : $data['year']."-12-31 00:00:00";
		$addWhere = "";
		//其他扩展条件
		input::getInput("post.cat_id") && $addWhere .= "AND cat_id = '".input::getInput("post.cat_id")."' ";
		input::getInput("post.type_id") && $addWhere .= "AND type_id = '".input::getInput("post.type_id")."' ";
		input::getInput("post.office_id") && $addWhere .= "AND office_id = '".input::getInput("post.office_id")."' ";
		input::getInput("post.department_id") && $addWhere .= "AND department_id = '".input::getInput("post.department_id")."' ";
					
		$db = sf::getLib("db");
		$query = $db->query("SELECT count(*) AS NUM,(case when `score` >= 90 then '90-100' when `score` >= 80 AND `score` < 90 then '80-90' when `score` >= 70 AND `score` < 80 then '70-80' when `score` >= 60 AND `score` < 70 then '60-70' when `score` >= 0 AND `score` < 60 then '1-60' else '待评' end) as LEV FROM projects WHERE group_id IN (SELECT id FROM project_group WHERE group_year = '".$data['year']."' AND unix_timestamp(created_at) > unix_timestamp('".$start_at."') AND unix_timestamp(created_at) < unix_timestamp('".$end_at."')) ".$addWhere." GROUP BY LEV ORDER BY LEV DESC");
		$data = $row = $db->result_array($query);
		
		view::set('data',$data);
		view::apply("inc_body","office/collect/show");
		view::display("page");
	}
	
}
?>