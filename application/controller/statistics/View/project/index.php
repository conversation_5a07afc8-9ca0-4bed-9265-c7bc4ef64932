<div class="content">
    <h2 class="content-heading">
        <i class="si si-briefcase mr-1"></i> 请选择专科类型
    </h2>
    <div class="row">
        <?php
        $subjects = getSubjectList('code');
        foreach($subjects as $code=>$subject):
            $rule = sf::getModel('Rules')->selectByCode($code);
            if($rule->isNew()) continue;
            ?>
            <div class="col-md-6 col-xl-3" onclick="window.location.href='<?=site_url('statistics/project/chart?code='.$code)?>'" style="cursor: pointer">
                <div class="block block-rounded text-center" onclick="loadingPage(this);">
                    <div class="block-content block-content-full bg-info">
                        <div class="my-3">
                            <img src="<?=site_path('assets/img/'.$code.'.png')?>">
                        </div>
                    </div>
                    <div class="block-content block-content-full block-content-sm bg-body-light">
                        <div class="font-w600">
                            <?=$subject;?>
                        </div>
                    </div>
                    <div class="block-content block-content-full">
                        <a class="btn btn-sm btn-light" href="javascript:void(0)"><i class="fa fa-edit text-muted mr-1"></i> 查看</a>
                    </div>
                </div>
            </div>
        <?php endforeach;?>
    </div>
</div><!-- END Page Content -->

<script>
    function loadingPage(me)
    {
        $(me).addClass('block-mode-loading-refresh')
        $(me).addClass('block-mode-loading')
    }
</script>