<?php
namespace App\controller\statistics;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Diagnosis extends BaseController
{
    private $view = NULL;
    private $declareYear = 'ALL';
    private $mark = 'diagnosis';
    private $cache = NULL;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/View/');
        $this->cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
        if(input::getMix('declare_year')) $this->declareYear = input::getMix('declare_year');
    }

    public function index()
    {
        if(input::getMix('update')=='yes'){
            $datas = $this->doStat();
        }else{
            $datas = $this->getStat();
        }
        $this->view->set('datas',$datas);
        $this->view->set('declareYear',$this->declareYear);
        $this->view->apply("inc_body","diagnosis/index");
        $this->view->display("page");
    }

    private function getStat()
    {
        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$this->declareYear);
        if($statistic->isNew()){
            return $this->doStat();
        }
        return $statistic->getData();
    }

    private function doStat()
    {
        $db = sf::getLib('db');
        $declareYear = $this->declareYear;
        $addWhere = "is_test=0 and statement IN (29,30)";
        if($declareYear!='ALL'){
            $addWhere .= " and `declare_year` = '{$declareYear}'";
        }
        //专科类别
        $datas['diagnosis'] = [];  //专科类别列表
        $query = $db->query("select diagnosis_code,any_value(diagnosis_subject) diagnosis_subject,count(diagnosis_code) c from projects where {$addWhere} and level in ('国家级','省级') GROUP BY diagnosis_code order by diagnosis_code asc");
        while($row = $db->fetch_array($query)) {
            $datas['diagnosis'][$row['diagnosis_code']] = [
                'subject'=>$row['diagnosis_subject'],
                'nation_count'=>0,
                'province_count'=>0,
                'nation_gjw_count'=>0,
                'nation_sjdw_count'=>0,
                'nation_szdw_count'=>0,
                'province_gjw_count'=>0,
                'province_sjdw_count'=>0,
                'province_szdw_count'=>0,
            ];
        }

        foreach ($datas['diagnosis'] as $code=>&$row){
            if(in_array($code,['05.01','05.02'])){
                continue;
            }
            if(substr($code,0,2)=='12' && $code!='12.01'){
                continue;
            }
            if(substr($code,0,2)=='07' && $code!='07.00'){
                continue;
            }
            if($code=='12.01'){
                $row['subject'] = '口腔科';
            }
            $diagnosisWhere = $addWhere;
            if($code=='05.00'){
                //妇产科：妇科、产科均计入妇产科，不单独列出数据，但生殖健康与不孕症专业单独计算。
                $diagnosisWhere.=" and diagnosis_code IN ('05.01','05.02','05.00')";
            }elseif($code=='12.01'){
                //口腔科：二级代码为12.01-12.08的专科全部按照“口腔科”（代码12）计算。
                $diagnosisWhere.=" and diagnosis_code like '12.%'";
            }elseif($code=='07.00'){
                //口腔科：二级代码为07.01-07.05的专科全部按照“儿科”（代码07）计算。
                $diagnosisWhere.=" and diagnosis_code like '07.%'";
            }else{
                $diagnosisWhere.=" and diagnosis_code = '{$code}'";
            }
            $datas['diagnosis'][$code]['nation_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '国家级'");
            $datas['diagnosis'][$code]['nation_gjw_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '国家级' and corporation_id in (select user_id from corporations where property= '国家委在川')");
            $datas['diagnosis'][$code]['nation_sjdw_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '国家级' and department_id = 'F094FD55-F735-425A-AD25-729AE4673D6B' and corporation_id not in (select user_id from corporations where property = '国家委在川')");
            $datas['diagnosis'][$code]['nation_szdw_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '国家级' and department_id in (select user_id from departments where id<23)");
            $datas['diagnosis'][$code]['province_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '省级'");
            $datas['diagnosis'][$code]['province_gjw_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '省级' and corporation_id in (select user_id from corporations where property= '国家委在川')");
            $datas['diagnosis'][$code]['province_sjdw_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '省级' and department_id = 'F094FD55-F735-425A-AD25-729AE4673D6B' and corporation_id not in (select user_id from corporations where property = '国家委在川')");
            $datas['diagnosis'][$code]['province_szdw_count'] = $db->result_first("select count(*) c from projects where {$diagnosisWhere} and level = '省级' and department_id in (select user_id from departments where id<23)");
        }

        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$declareYear);
        $statistic->setData($datas);
        $statistic->save();
        return $datas;
    }

    public function chart(){
        $declareYear = $this->declareYear;
        $xAxisData = [];     //X轴
        $seriesData = [];                  //Y轴
        $seriesData['nation'] = [];  //国重项目数量
        $seriesData['province'] = [];  //省重项目数量
        $db = sf::getLib("db");
        $addWhere = "is_test=0 and statement IN (29,30)";
        if($declareYear!='ALL'){
            $addWhere .= " and `declare_year` = '{$declareYear}'";
        }
        //专科类别
        $datas['diagnosis'] = [];  //专科类别列表
        $query = $db->query("select diagnosis_code,any_value(diagnosis_subject) diagnosis_subject,count(diagnosis_code) c from projects where {$addWhere} and level in ('国家级','省级') GROUP BY diagnosis_code order by diagnosis_code asc");
        while($row = $db->fetch_array($query)) {
            $datas['diagnosis'][$row['diagnosis_code']] = [
                'subject'=>$row['diagnosis_subject'],
                'nation_count'=>0,
                'province_count'=>0,
            ];
        }

        foreach ($datas['diagnosis'] as $code=>$row){
            if(in_array($code,['05.01','05.02'])){
                continue;
            }
            if(substr($code,0,2)=='12' && $code!='12.01'){
                continue;
            }
            if(substr($code,0,2)=='07' && $code!='07.00'){
                continue;
            }
            if($code=='12.01'){
                $row['subject'] = '口腔科';
            }
            $xAxisData[] = $row['subject'];
            $departmentWhere = $addWhere;
            if($code=='05.00'){
                //妇产科：妇科、产科均计入妇产科，不单独列出数据，但生殖健康与不孕症专业单独计算。
                $departmentWhere.=" and diagnosis_code IN ('05.01','05.02','05.00')";
            }elseif($code=='12.01'){
                //口腔科：二级代码为12.01-12.08的专科全部按照“口腔科”（代码12）计算。
                $departmentWhere.=" and diagnosis_code like '12.%'";
            }elseif($code=='07.00'){
                //口腔科：二级代码为07.01-07.05的专科全部按照“儿科”（代码07）计算。
                $departmentWhere.=" and diagnosis_code like '07.%'";
            }else{
                $departmentWhere.=" and diagnosis_code = '{$code}'";
            }
            $radicateCount['nation']= $db->result_first("select count(*) c from projects where ".$departmentWhere." and level = '国家级'");

            $seriesData['nation'][] = $radicateCount['nation'];
            $radicateCount['province']= $db->result_first("select count(*) c from projects where ".$departmentWhere." and level = '省级'");
            $seriesData['province'][] = $radicateCount['province'];
            $seriesData['total'][] = $radicateCount['nation']+$radicateCount['province'];
        }

        //图表标题
        $option['title']['text'] = $declareYear!='ALL' ? $declareYear.'年四川省国家与省级临床重点专科项目专业类别分布统计图' : '四川省国家与省级临床重点专科项目专业类别分布统计图';
        $option['title']['x'] = 'center';
//        $option['title']['link'] = site_url('statistics/project/singlechart?code='.$subjectCode.'&action='.__FUNCTION__.'&corporation_id='.$companyId);
        //工具栏
        $option['toolbox']['show'] = true;
        $option['toolbox']['feature']['saveAsImage']['show'] = true;
        //提示框
        $option['tooltip']['trigger'] = 'axis';
        $option['tooltip']['axisPointer']['type'] = 'shadow';
        //数据集
//        $option['dataset'] = $dataset;
        //图例
        $option['legend']['show'] = true;
//        $option['legend']['orient'] = 'vertical';
//        $option['legend']['right'] = 0;
        $option['legend']['top'] = 30;
//        $option['legend']['data'] =  $xAxisData;
        //X轴，类目轴（category）。默认情况下，类目轴对应到 dataset 第一列
        $option['xAxis']['type'] = 'category';
        $option['xAxis']['data'] = $xAxisData;
        //X轴，字体倾斜度数
        if(count($option['xAxis']['data'])<30){
            $option['xAxis']['axisLabel']['rotate'] = 40;
        }

        $option['xAxis']['axisLabel']['show'] = true;
        //X轴，字体样式
        $option['xAxis']['axisLabel']['textStyle']['fontSize'] = 11;
        //Y轴，数值轴
        $option['yAxis']['type'] = 'value';
        //Y轴名称
        $option['yAxis']['name'] = '数量（个）';
        $option['yAxis']['min'] = 0;
//        $option['yAxis']['max'] = 1035;
        $option['yAxis']['axisLabel']['formatter'] = '{value}';
        $option['yAxis']['nameLocation'] = 'middle';
        //Y轴名称名称与轴线之间的距离
        $option['yAxis']['nameGap'] = 40;
        //在系列（series） 中设置数据
        $option['series'][0]['type'] = 'bar';
        $option['series'][0]['stack'] = 'total';
        //系列名称
        $option['series'][0]['name'] = '国重项目';
        $option['series'][0]['data'] = $seriesData['nation'];
        //显示系列标签
        $option['series'][0]['label']['show'] = true;
        $option['series'][0]['emphasis']['focus'] = 'series';
        //在系列（series） 中设置数据
        $option['series'][1]['type'] = 'bar';
        $option['series'][1]['stack'] = 'total';
        //系列名称
        $option['series'][1]['name'] = '省重项目';
        $option['series'][1]['data'] = $seriesData['province'];
        //显示系列标签
        $option['series'][1]['label']['show'] = true;
        $option['series'][1]['emphasis']['focus'] = 'series';

        //折线
        $option['series'][2]['name'] = '合计';
        $option['series'][2]['data'] = $seriesData['total'];
        $option['series'][2]['label']['show'] = true;
        $option['series'][2]['type'] = 'line';

        //图表离容器底部的距离
        $option['grid']['bottom'] = '30%';

        $data['option'] = $option;

        echo json_encode($data,JSON_UNESCAPED_UNICODE);exit();

    }
}