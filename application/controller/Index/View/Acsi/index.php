<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        满意度调查结果管理
                    </h3>
                    <div class="block-options">
                        <?=Button::setUrl(site_url("index/datasync/zdzk/guide?index_type=acsi"))->setIcon('refresh')->link('同步数据')?>

                        <?=Button::setUrl(site_url("index/excel/tpl/index/type/acsi"))->setIcon('import')->setEndEvent('location.reload()')->window('导入数据')?>
                        <!--
                        <?=Button::setUrl(site_url("index/acsi/edit"))->setIcon('add')->link('新增数据')?>
                        -->
                        <?=btn('link','导出数据',site_url('export/export/index/table/IndexAcsis'),'export')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <table class="table table-hover table-vcenter">
                        <thead>
                        <tr>
                            <th class="text-center" style="width: 100px;">
                                序号
                            </th>
                            <th>
                                <?=getColumnStr('指标年度','index_year')?>
                            </th>
                            <th>
                                <?=getColumnStr('申报单位','company_id')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('指标名称','index_code')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('指标值','data')?>
                            </th>
                            <th style="width: 160px;">
                                填报项目数
                            </th>
                            <th class="text-center" style="width: 170px;">
                                操作
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($index = $pagers->getObject()):?>
                            <tr>
                                <td class="text-center">
                                    <?=$pagers->getIndex()?>
                                </td>
                                <td>
                                    <?=$index->getIndexYear()?>
                                </td>
                                <td>
                                    <?=$index->getCompanyName()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$index->getIndexName()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$index->getData()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$index->getProjectCount()>0 ? '<a href="'.site_url('office/search/index?field=corporation_name&search='.$index->getCompanyName()).'&declare_year='.config::get('current_declare_year').'">'.$index->getProjectCount().'</a>' : '0'?>
                                </td>
                                <td class="text-center">
                                    <!--
                                    <?=Button::setName('编辑')->setUrl(site_url("index/acsi/edit/id/".$index->getId()))->setIcon('edit')->link()?>
                                    -->
                                    <?=Button::setName('删除')->setUrl(site_url("index/acsi/remove/id/".$index->getId()))->setIcon('delete')->setClass('btn-alt-danger')->delete()?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
