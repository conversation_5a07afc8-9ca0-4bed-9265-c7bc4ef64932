<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        全省平均数据管理
                    </h3>
                    <div class="block-options">
                        <?=Button::setUrl(site_url("index/datasync/province/guide"))->setIcon('refresh')->link('同步数据')?>
                        <?=Button::setUrl(site_url("data/province/index"))->setIcon('list')->link('同步后的数据')?>
                        <?=Button::setUrl(site_url("index/excel/tpl/index/type/province"))->setIcon('import')->setEndEvent('location.reload()')->window('导入数据')?>
                        <?=Button::setUrl(site_url("index/province/edit"))->setIcon('add')->link('新增数据')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <table class="table table-hover table-vcenter">
                        <thead>
                        <tr>
                            <th class="text-center" style="width: 100px;">
                                序号
                            </th>
                            <th>
                                <?=getColumnStr('指标年度','index_year')?>
                            </th>
                            <th>
                                <?=getColumnStr('专科','subject_code')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('指标名称','index_code')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('指标值','data')?>
                            </th>
                            <th class="text-center" style="width: 170px;">
                                操作
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($index = $pagers->getObject()):?>
                            <tr>
                                <td class="text-center">
                                    <?=$pagers->getIndex()?>
                                </td>
                                <td class="font-w600">
                                    <?=$index->getIndexYear()?>
                                </td>
                                <td class="font-w600">
                                    <?=$index->getSubjectName()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$index->getIndexName()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$index->getData()?>
                                </td>
                                <td class="text-center">
                                    <?=Button::setName('编辑')->setUrl(site_url("index/province/edit/id/".$index->getId()))->setIcon('edit')->link()?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
