<style>
    .control-label{
        line-height: 35px;
        padding-right: 0;
    }
</style>
<script language="javascript" type="text/javascript">
    function unit(json){
        $('#corporation_id').val(json.id);
        $('#corporation_name').val(json.subject);
        closeWindow();
    }
</script>
<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        专科客观分数计算
                    </h3>
                    <div class="block-options">

                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <form action="" method="post" name="search" id="search">
                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">单位：</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <input type="text" class="form-control" id="corporation_name" name="corporation_name" value="" readonly>
                                    <input type="hidden" name="corporation_id" id="corporation_id" value="" />
                                </div>
                                <div class="col-md-3 col-sm-3 col-xs-3">
                                    <button type="button" class="btn btn-sm btn-alt-primary" name="button2" id="button" onclick="return showWindow('选择单位','<?=site_url("common/unit_search")?>',600,400);" style="height: 38px;">选择单位</button>
                                </div>
                            </div>
                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">专科：</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="subject_code" id="subject_code" class="form-control w-auto custom-control-inline">
                                        <option value="">=不限=</option>
                                        <?=getSelectFromArray(getSubjectList('mark'),input::getInput('mix.subject_code'),false)?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">申报年度：</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="declare_year" class="form-control w-auto custom-control-inline" style="margin-right: 6px">
                                        <?=getYearList(input::mix("declare_year"),2023)?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row center">
                                <label class="control-label text-right col-xs-12 col-sm-4 col-md-4">项目状态：</label>
                                <div class="col-xs-12 col-sm-5 col-md-5">
                                    <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                                        <option value="">不限</option>
                                        <?=projectStateSelect(input::getInput("mix.statement"))?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row center">
                                <div style="width: 200px;margin: 0 auto;">
                                    <?=Button::setType('button')->setClass('btn-alt-primary query')->setSize('btn-lg')->setIcon('check')->button('开始打分')?>
                                </div>
                            </div>
                            <div class="progress" style="display: none">
                                <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="min-width: 2em;">
                                    0%
                                </div>
                            </div>
                            <div class="messages text-center">
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var t;
    $(function (){
        $(".query").click(function (){
            var url = '<?=site_url('index/sync/scoreInit')?>';
            var data = $('#search').serializeArray();
            var me = $(this);
            me.html('<i class="fa fa-sync fa-spin"></i> 打分中').addClass('disabled').attr('disabled',true);
            showMessage('<p>正在计算中，请勿关闭页面</p>');
            var url = '<?=site_url('index/sync/init')?>';
            $.post(url,data,function (response){
                if(response.code==0){
                    t = setInterval(getPercent,3000);
                }
            },'json');
        });
    });

    function showProgress(percent) {
        $(".progress").show();
        $(".progress-bar").attr('aria-valuenow',percent);
        $(".progress-bar").css('width',percent+'%');
        $(".progress-bar").text(percent+'%');
    }

    function getPercent()
    {
        var url = '<?=site_url('index/sync/getScorePercent')?>';
        $.getJSON(url, function (data) {
            if(data.code==0){
                showProgress(data.percent);
                showMessage(data.msg);
            }
            if(data.code==0 && data.percent==100){
                clearInterval(t);
                showMessage(data.msg);
                $(".progress").hide();
                $(".query").html('<i class="fa fa-check"></i> 开始打分').removeClass('disabled').attr('disabled',false);
            }
        });
    }


    function showMessage(html)
    {
        $(".messages").html(html)
    }
</script>