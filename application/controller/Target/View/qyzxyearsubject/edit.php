<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            编辑<?=getQyzxName($subjectCode)?>考核指标
        </h2>
        <div class="content-options">
            <?=Button::setUrl(site_url('target/qyzxyearsubject/index'))->setIcon('back')->link('返回')?>
        </div>
    </div>

    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <input type="hidden" name="code" value="<?=$subjectCode?>">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="form-group row">
                            <table class="table table-bordered">
                                <tr>
                                    <td width="10%" height="18" align="center">一级指标</td>
                                    <td width="20%" height="18" align="center">二级指标</td>
                                    <td width="40%" height="18" align="center">指标说明</td>
                                    <td width="5%" align="center">必填指标</td>
                                    <td width="5%" align="center">排序</td>
                                </tr>
                                <?php
                                $targets = sf::getModel('TargetsQyzxyear')->selectAll("level = 2 and `type` like '%|review|%'","order by code asc");
                                $rowspan = [];
                                $i=0;
                                while($target = $targets->getObject()){
                                    $targetQyzxSubject = sf::getModel('TargetQyzxyearSubjects')->selectByCodeAndSubjectCode($target->getCode(),$subjectCode);
                                    if($targetQyzxSubject->isNew()){
                                        $targetQyzxSubject->setNote($target->getNote());
                                    }
                                    $indexCode = $target->getIndexCode();
                                    $rowspan[$target->getTopCode()]++;
                                    $datas[$i]['top_code'] = $target->getTopCode();
                                    $datas[$i]['top_subject'] = $target->getTopIndex();
                                    $datas[$i]['code'] = $target->getCode();
                                    $datas[$i]['subject'] = $target->getSubject();
                                    $datas[$i]['note'] = $targetQyzxSubject->getNote();
                                    $datas[$i]['sort'] = $targetQyzxSubject->getSort();
                                    $datas[$i]['is_required'] = $targetQyzxyearSubjects->isQequired($target->getCode(),$subjectCode);
                                    $datas[$i]['is_selected'] = $targetQyzxyearSubjects->isSelected($target->getCode(),$subjectCode);
                                    $i++;
                                }
                                $topCode = 0;
                                $secondCode = 0;
                                foreach($datas as $data):
                                ?>
                                <tr>
                                    <?php
                                    if($data['top_code']!=$topCode):$topCode=$data['top_code'];
                                        ?>
                                        <td align="center" rowspan="<?=$rowspan[$topCode]?>" style="vertical-align: middle"><?=$data['top_subject']?></td>
                                    <?php endif;?>
                                    <td><?=$data['subject']?></td>
                                    <td align="center">
                                        <input class="form-control" type="text" name="note[<?=$data['code']?>]" value="<?=$data['note']?>">
                                    </td>
                                    <td align="center">
                                        <input class="is_req" type="checkbox" name="req[<?=$data['code']?>]" value="<?=$data['code']?>" <?=$data['is_required']?'checked':''?>>
                                    </td>
                                    <td align="center">
                                        <input class="form-control" type="text" name="sort[<?=$data['code']?>]" value="<?=$data['sort']?>">
                                    </td>
                                </tr>
                                <?php endforeach;?>
                            </table>
                        </div>

                        <div class="form-group row">
                            <div class="col-sm-8 ml-auto">
                                <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> 保存</button>
                            </div>
                        </div>
                    </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
