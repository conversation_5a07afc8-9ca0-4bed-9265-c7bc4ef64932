<?php
namespace App\Controller;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class res extends BaseController
{
	public $auth = array('index','css','js');
	
	function index()
	{
		$this->css();
	}
	
	function css()
	{
		$this->buffer_begin();
		header ("content-type: text/css; charset: utf-8");
		header ("cache-control: must-revalidate");
		$offset = 60 * 60 * 24 * 10;
		$expire = "expires: " . gmdate ("D, d M Y H:i:s", time() + $offset) . " GMT";
		header ($expire);
		
		ob_start("compress");
		//包含你的全部css文档
		include_once(WEBROOT.'/images/admin.css');
		include_once(WEBROOT.'/js/easyui/themes/metro/easyui.css');
		include_once(WEBROOT.'/js/easyui/themes/icon.css');
		
		$this->buffer_output();
	}
	
	function js()
	{
		$this->buffer_begin();
		header ("content-type: text/javascript; charset: utf-8");
		header ("cache-control: must-revalidate");
		$offset = 60 * 60 * 24 * 10;
		$expire = "expires: " . gmdate ("D, d M Y H:i:s", time() + $offset) . " GMT";
		header ($expire);
		
		ob_start("compress");
		include_once(WEBROOT.'/js/jquery-1.7.2.min.js');
		include_once(WEBROOT.'/js/easyui/easyloader.js');
		include_once(WEBROOT.'/js/validate/jquery.validate.js');
		include_once(WEBROOT.'/js/DatePicker/WdatePicker.js');
		include_once(WEBROOT.'/js/loadbox.js');
		include_once(WEBROOT.'/js/func.js');
		
		$this->buffer_output();
	}
	
	function buffer_begin()
	{
		if(extension_loaded('zlib')){//检查服务器是否开启了zlib拓展
			ob_start('ob_gzhandler');
		}	
	}
	
	function buffer_output()
	{
		if(extension_loaded('zlib')){
			ob_end_flush();//输出buffer中的内容
		}	
	}
	
}

function compress($buffer) {//去除文件中的注释
	$buffer = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $buffer);
	return $buffer;
}
 
/** 
 * 引入static文件 
 * @param {array|string} 相对路径 
 * @param {string} 当前执行脚本所在的路径__FILE__ 
 * 
 */  
function import_static($files, $path=NULL){  
    // 更改当前脚本的执行路径  
    $old_dir = getcwd();  
    $tmp_dir = (isset($path)) ? dirname($path): dirname(__FILE__);  
    chdir($tmp_dir);  
    // 整理包含文件  
    if (!is_array($files)) {  
        $tmp = array();  
        $tmp[] = $files;  
        $files = $tmp;  
    }  
    // 发送头信息  
    if (isset($files[0])) {  
        if (stripos($files[0], '.js') !== false) {  
            $header_str = 'Content-Type:   text/javascript';  
        } elseif (stripos($files[0], '.css') !== false) {  
            $header_str = 'Content-Type:   text/css';  
        }  
        if (!ob_get_contents()) {  
            header($header_str);  
        }  
    }  
    // 引入包含文件  
    foreach($files as $key=>$value) {  
        require_once($value);  
    }  
    // 改回当前脚本的执行路径  
    chdir($old_dir);  
}  
?>