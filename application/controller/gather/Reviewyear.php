<?php
namespace App\controller\gather;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Reviewyear extends BaseController
{	
	

	function wait_list()
	{
		$this->grid('gather/reviewyear/wait_list',"department_id = '".input::getInput("session.roleuserid")."' AND `statement` = 5 ");
	}
	
	function submit_list()
	{
		$this->grid('gather/reviewyear/submit_list',"department_id = '".input::getInput("session.roleuserid")."' AND `statement` IN (9,10) ");
	}

	function rejected_list()
	{
		$this->grid('gather/reviewyear/rejected_list',"department_id = '".input::getInput("session.roleuserid")."' AND `statement` = 6 ");
	}
	
	
	function index()
	{
		$this->grid('gather/reviewyear/index',"department_id = '".input::getInput("session.roleuserid")."'");
	}

	/**
	 * 审核中期
	 */
	function doAccept()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个项目！',getFromUrl());			
		for($i=0,$n=count((array)$ids);$i<$n;$i++){
			$review = sf::getModel("Reviewyears")->selectByReviewId($ids[$i]);
			if($review->getDepartmentId() != input::getInput("session.roleuserid")) $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			$review->setStatement(9);
			if($review->save()) sf::getModel("historys")->addHistory($review->getReviewId(),"主管部门已审核该年度监测",'reviewyear');
		}
		$this->success("上报成功！",getFromUrl());
	}
	
	/**
	 * 退回任务书——单个
	 */
	function doRejectedOnlyOne()
	{	
		$review = sf::getModel("Reviewyears")->selectByReviewId(input::getInput("mix.id"));
		if(input::getInput("post.content"))
		{
			if($review->getDepartmentId() != input::getInput("session.roleuserid"))
				$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			$review->setStatement(6);
			$review->save();
			sf::getModel("historys")->addHistory($review->getReviewId(),"主管部门已退回该年度监测"."<br />".input::getInput("post.content"),'reviewyear');
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("review",$review);
		view::apply("inc_body","gather/reviewyear/back");
		view::display("page_blank");
	}
	/**
	 * 驳回任务书——多个
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个项目！',getFromUrl());			
		for($i=0,$n=count($ids);$i<$n;$i++){
			$review = sf::getModel("Reviewyears")->selectByReviewId($ids[$i]);
			if($review->getDepartmentId() != input::getInput("session.roleuserid"))
				$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
			$review->setStatement(6);
			$review->save();
			sf::getModel("historys")->addHistory($review->getReviewId(),"主管部门已退回该年度监测",'reviewyear');
		}
		$this->page_debug('退回成功！',getFromUrl());
	}


    /**
     * 集中处理项目的搜索信息
     *
     */
    public function grid($tpl = 'gather/reviewyear/wait_list',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        //处理搜索
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

        if(input::getInput("mix.declare_year"))
            $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";

        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['Reviews']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['Reviews']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['Reviews']['orderStr'] = base64_encode($addSql);
        }
//		dd($addWhere);
        //显示页面
        $form_vars = array('search','declare_year','declare_month','department_id','department_id','statement');

        view::set("pager",sf::getModel('Reviewyears')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        view::apply("inc_body",$tpl);
        view::display($page);
    }
}