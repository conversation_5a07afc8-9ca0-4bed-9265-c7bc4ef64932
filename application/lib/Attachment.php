<?php
namespace App\Lib;
use Sofast\Core\Config;
use Sofast\Core\Sf;
use Sofast\Core\input;
use Sofast\Core\Lang;
use App\Lib\Upload;

class Attachment
{
	private $table = 'Filemanager';//保存文件数据对象
	private $item_id   = NULL;//上传文件依附ID
	private $item_type = 'project';//上传文件依附类型
	private $note = '';//上传文件备注
	private $allow_type = array('jpg','gif','png');//允许上传文件类型
	private $max_file_size = 2048000;//2M
	private $filed_name = 'Filedata';//文本域名称
	
	public function __construct($table='')
	{
		if($table) $this->setTable($table);
		return $this;
	}
	
	public function setTable($table)
	{
		$this->table = $table;
		return $this;
	}
	
	public function setItemId($item_id)
	{
		$this->item_id = $item_id;
		return $this;
	}
	
	public function setItemType($item_type)
	{
		$this->item_type = $item_type;
		return $this;
	}
	
	public function setNote($note)
	{
		$this->note = $note;
		return $this;
	}
	
	public function setAllowType($allow_type)
	{
		$this->allow_type = $allow_type;
		return $this;
	}
	
	public function setMaxFileSize($max_file_size)
	{
		$this->max_file_size = $max_file_size;
		return $this;
	}
	
	public function setFiledName($filed_name)
	{
		$this->filed_name = $filed_name;
		return $this;
	}
	
	/**
	 * 上传附件并记录到数据库中
	 */
	public function upload()
	{
		$json = $_json = array();
		if($_FILES){
			$upload = new upload($this->filed_name,config::get("upload_path","./up_files/"),$this->max_file_size,$this->allow_type);
			if($upload->upload())
			{
				$result = $upload->getSaveFileInfo();
				foreach($result as $files)
				{
					$filemanager = sf::getModel($this->table);
					$filemanager->setFileName($files['name']);
					$filemanager->setFileSavename($files['savename']);
					$filemanager->setFilePath($files['path']);
					$filemanager->setFileSize($files['size']);
					$filemanager->setFileExt($files['type']);
					$filemanager->setFileMinetype($files['minetype']);
					$filemanager->setUserId(input::session('userid'));
					$filemanager->setUserName(input::session('username'));
					$filemanager->setCreatedAt(date("Y-m-d H:i:s"));
					$filemanager->setItemId($this->item_id);
					$filemanager->setItemType($this->item_type);
					$filemanager->setFileNote($this->note);
					$files['id'] = $filemanager->save();	
					$_json[]  = $files;
				}
				$json['has_error'] = false;
				$json['files'] = $_json;
			}else{
				$json['has_error'] = true;
				$json['msg'] = $upload->getError();
			}
		}else{
			$json['has_error'] = true;
			$json['msg'] = '服务器未接收到数据!';	
		}
		return $json;
	}
	
	/**
	 * 从库中移除附件
	 */
	public function remove()
	{
		
	}
}
?>