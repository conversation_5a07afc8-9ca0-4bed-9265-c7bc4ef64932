<?php

namespace App\Models;

class CorporationProductDetail extends BaseModel
{
    protected $table = "corporations_product_detail";
    //protected $fillable = array();
	protected $guarded = array('id');

    public static function getFormCache($user_id='')
    {
        $formData = array();
//        unset($_SESSION['corporations_product_detail'][$user_id]);
        if(!$_SESSION['corporations_product_detail'][$user_id])
        {
            if($user_id)
            {
                $corporation = parent::where('user_id',$user_id)->first();
                $formData = array();
                if(!empty($corporation)){
                    $extra = array();
                    $corporationRow = $corporation->toArray();
                    $corporationRow['unit_property'] = array_search($corporationRow['property'],getConfig(0,'unit_property','array'));
                    $corporationRow['type'] = Department::where('user_id',$corporationRow['department_id'])->value('type');

//                    $corporationRow['department_html'] = '';
//                    dd($corporationRow);
                    $formData = array_merge($formData,$corporationRow,$extra);
                }
//                foreach($formData as $key=>$value)
//                {
//                    if(self::is_serialized($value))
//                        $formData[$key] = unserialize($value);
//                }


                //处理自定义字段
                if($formData['customs'])
                {
                    $formData = array_merge($formData,$formData['customs']);
                    unset($formData['customs']);
                }

                $data[$user_id] = $formData;
                $_SESSION['corporations_product_detail'] = $data;
            }
        }
        else $formData = $_SESSION['corporations_product_detail'][$user_id];
//        dd($formData);
        return $formData;
    }

    public static function setFormCache($user_id='',$data=array())
    {
        unset($_SESSION['corporations_product_detail']);
        $_data = $_SESSION['corporations_product_detail'][$user_id];
        $_data = $_data?$_data:array();
        $_SESSION['corporations_product_detail'][$user_id] = array_merge($_data,$data);

        return $_SESSION['corporations_product_detail'][$user_id];
    }

    public static function is_serialized($data)
    {
        $data = trim( $data );
        if ( 'N;' == $data )
            return true;
        if ( !preg_match( '/^([adObis]):/', $data, $badions ) )
            return false;
        switch ( $badions[1] ) {
            case 'a' :
            case 'O' :
            case 's' :
                if ( preg_match( "/^{$badions[1]}:[0-9]+:.*[;}]\$/s", $data ) )
                    return true;
                break;
            case 'b' :
            case 'i' :
            case 'd' :
                if ( preg_match( "/^{$badions[1]}:[0-9.E-]+;\$/", $data ) )
                    return true;
                break;
        }
        return false;
    }
}
