<?php namespace App\Models;

class Search extends BaseModel
{
    protected $table = "searchs";
    protected $guarded = array('id');
    
    public function users()
    {
        return $this->belongsTo('App\Models\User','user_id','user_id');
    }
    public function declarers()
    {
        return $this->belongsTo('App\Models\Declarer','user_id','user_id');
    }

    public function searchman()
    {
        return $this->belongsTo('App\Models\Personnel','user_id','searchman_id');
    }

    public function checkman()
    {
        return $this->belongsTo('App\Models\Personnel','user_id','checkman_id');
    }
    // 返回查新类型简写字母
    public function word($val)
    {
    	$type = [
    		'A' => '439',             //科研开题查新
    		'B' => '442',             //成果鉴定查新
    		'C' => '440',             //科研立项查新
    		'D' => '464',             //新材料、新产品查新
    		'E' => '443',             //专利查新
    		'F' => '441',             //论文引文检索
            'G' => '449',             //科技奖励查新
    	];
    	return array_search($val, $type);
    }
// 取得查新项目状态
    public function getSearchState()
    {
        switch ($this->state) {
            case '0':
                return '填写中';
                break;
            case '1':
                return '等待单位审核';
                break;
            case '2':
                return '单位审核驳回';
                break;
            case '3':
                return '等待受理';
                break;
            case '4':
                return '等待查新';
                break;
            case '5':
                return '在查新';
                break;
            case '6':
                return '查新驳回';
                break;
            case '7':
                return '待审核';
                break;
            case '8':
                return '审核驳回';
                break;
            case '9':
                return '待缴费';
                break;
            case '10':
                return '已完成';
                break;
            case '11':
                return '退回修改';
                break;
        }
    }
    //生成项目编号
    public function getNumber($year,$type_id)
    {
        $search = parent::where('type_id',$type_id)->where('year',$year)->orderBy('number')->get();
        if(count($search)){
            $prenum = $search->last()->number;
            return substr($prenum,0,5).sprintf('%04d',substr($prenum,-4)+1);
        }else{
            return  $year.$this->word($type_id).'0001';
        }
    }

    /**
     * 检查是否允许上报
     * @return bool
     */
    public function allowSubmit()
    {
        $userid = $this->user_id;
        //当前项目负责人待缴费项目的数量
        $count = parent::where('user_id',$userid)->where('state',9)->count();
        return $count>0 ? false : true;
    }
}
