<style>
    .content p{
        font-size: 20px;
        text-indent: 2em;
    }
</style>
<div class="page-header">
    <div class="row">
        <div class="col-sm-12">
            <div class="page-title-box">
                <div class="btn-group pull-right">
                    <?=backBtn()?>
                </div>
                <h4 class="page-title"><?=$article->getChannelName()?></h4>
            </div>
        </div>
    </div>
</div>
<div class="page-body">
    <div class="row">
        <div class="col-sm-12">
            <div class="search" style="width: 100%">
                <form class="form-inline" method="post" action="">
                <div class="form-group col-lg-12">
                    <label for="keyword">关键词：</label>
                    <input type="text" class="form-control" id="keyword" name="search" placeholder="关键词">
                    &nbsp;&nbsp;&nbsp;
                    <button type="button" class="btn btn-primary mb-2" id="search-btn">搜索</button>
                </div>
                </form>
            </div>
        </div>
        <div class="col-sm-8">
            <div class="card m-b-30 card-body" style="box-shadow:none">
                <h4 class="card-title font-20 mt-0 text-center"><?=$article->getSubject()?></h4>
                <p class="card-text text-center">
                    <small class="text-muted">发布日期：<?=$article->getCreatedAt() ? substr($article->getCreatedAt(),0,10) : substr($article->getUpdatedAt(),0,10)?></small>
                </p>
                <div class="content">
                <?=$article->getContent()?>
                </div>
                <p class="download"><?=$article->getAttachments()?></p>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="card m-b-30 card-body" style="box-shadow:none">
                <h4 class="card-title font-20 mt-0">农业主导产业</h4>
                <div class="content">
                    <a href="<?=site_url('article/front/show/id/419')?>">
                    <img src="<?=site_path('static/home/<USER>')?>" style="width: 100%" alt="">
                    <p class="text-center">水稻产业简介</p>
                    </a>
                    <a href="<?=site_url('article/front/show/id/420')?>">
                    <img src="<?=site_path('static/home/<USER>')?>" style="width: 100%" alt="">
                    <p class="text-center">玉米产业简介</p>
                    </a>
                    <a href="<?=site_url('article/front/show/id/421')?>">
                        <img src="<?=site_path('static/home/<USER>')?>" style="width: 100%" alt="">
                        <p class="text-center">油菜产业简介</p>
                    </a>
                    <a href="<?=site_url('article/front/show/id/422')?>">
                    <img src="<?=site_path('static/home/<USER>')?>" style="width: 100%" alt="">
                    <p class="text-center">茶叶产业简介</p>
                    </a>
                    <a href="<?=site_url('article/front/show/id/423')?>">
                    <img src="<?=site_path('static/home/<USER>')?>" style="width: 100%" alt="">
                    <p class="text-center">核桃产业简介</p>
                    </a>
                    <a href="<?=site_url('article/front/show/id/424')?>">
                    <img src="<?=site_path('static/home/<USER>')?>" style="width: 100%" alt="">
                    <p class="text-center">猕猴桃产业简介</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(function(){
        $("#search-btn").click(function(){
            var txt=$("#keyword").val();
            if($.trim(txt)!=""){
                // $("table tr:not('#theader')").filter(":contains('"+txt+"')").css("background","#e83e8c").css("color","#fff");
                $("body,html").animate({
                    scrollTop:$("table tr td").filter(":contains('"+txt+"')").offset().top-100
                },0);

                $("table").each(function (index,element) {
                    var html = $(element).html();
                    var reg = new RegExp( txt , "g" )
                    var newhtml = html.replace(reg, '<span class="text-danger">' + txt + '</span>');
                    $(element).html(newhtml);
                });

            }else{
                $("table tr:not('#theader')").css("background","#fff");
            }
        });
    })
</script>