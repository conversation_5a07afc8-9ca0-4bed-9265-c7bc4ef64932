<?php
use App\Models\Article;
use App\Models\Channel;
?>
<div class="main">
  <div class="contents no-padding ">
  <?php include('search.php');?>

  <div class="col-md-7 col-md-offset-2 col-sm-12 col-xs-12 question-list box-shade">
    <div class="title"><i class="fa fa-list fa-2x orange"></i> <span>内容列表</span></div>
    <div class="content">
      <ul class="no-margin no-padding">
      <?php if($articles->count()==0):?>
        <li class="grey"><i class="fa fa-caret-right blue"></i> 没有找到任何数据</li>
      <?php endif;?>
      <?php foreach($articles as $article):?>
        <li><i class="fa fa-angle-right blue"></i> <a href="<?=site_url('article/getShow/cid/'.$cid.'/aid/'.$article->id)?>"><?=$article->subject?></a>
          <small class="pull-right grey"><?=$article->getUpdatedAt('Y.m.d')?></small></li>
      <?php endforeach;?>
      </ul>
    </div>
    
    <div class="text-center"><?=$articles->render()?></div>
    <div class="clearfix"></div>
  </div>

  <div class="col-md-2 col-sm-12 col-xs-12 box">
    <div class="title"><i class="fa fa-list orange"></i> <span>常见问题</span>
      <a class="btn btn-sm pull-right btn-info" href="<?=site_url('article/getList/cid/306')?>">更多</a></div>
        <div class="content">
          <dl class="no-margin">
            <?php $articles = Channel::find(306)->articles()->where('is_release',1)->orderBy('is_top','DESC')->orderBy('hits','DESC')->orderBy('is_useful','DESC')->take(6)->get();?>
            <?php if($articles->count()==0):?>
              <dd class="grey"><i class="fa fa-caret-right red"></i> 没有找到任何数据</dd>
            <?php endif;?>
            <?php foreach($articles as $article):?>
              <dd title="<?=$article->subject?>"><i class="fa fa-caret-right blue"></i> <a href="<?=site_url('article/getShow/cid/'.$cid.'/aid/'.$article->id)?>"><?=$article->subject?></a></dd>
            <?php endforeach;?>
          </dl>
        </div>

      <div class="title"><i class="fa fa-list orange"></i> <span>最新问题</span>
      <a class="btn btn-sm pull-right btn-info" href="<?=site_url('article/getList/cid/214')?>">更多</a></div>
        <div class="content">
          <dl class="no-margin">
            <?php $articles = Channel::find(214)->articles()->where('is_release',1)->orderBy('is_top','DESC')->orderBy('updated_at','DESC')->take(6)->get();?>
            <?php if($articles->count()==0):?>
              <dd class="grey"><i class="fa fa-caret-right red"></i> 没有找到任何数据</dd>
            <?php endif;?>
            <?php foreach($articles as $article):?>
              <dd title="<?=$article->subject?>"><i class="fa fa-caret-right blue"></i> <a href="<?=site_url('article/getShow/cid/'.$cid.'/aid/'.$article->id)?>"><?=$article->subject?></a></dd>
            <?php endforeach;?>
          </dl>
        </div>
      </div>
    <div class="clearfix"></div>
  </div>
</div>