<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                平台库
            </h3>
            <div class="block-options">
                <?=Button::seturl(site_url('admin/platform/edit'))->setIcon('add')->link('新增平台')?>
                <?=Button::seturl(site_url('import/platform/index'))->setIcon('import')->link('导入')?>
                <?=Button::seturl(site_url('export/export/index/table/platforms'))->setIcon('export')->link('导出')?>
            </div>
        </div>
        <div class="block-content">
            <div class="page-body">
                <div class="main">
                    <div class="search">
                        <?php include('search_part.php');?>
                    </div>
                    <div class="box">
                        <h6>平台数：<?=getPlatformTotal()?>。
                            <?php
                            $platformTypes = get_select_data('cat');
                            $arr = [];
                            foreach ($platformTypes as $platformType){
                                $arr[] = $platformType.'：'.getPlatformTotal($platformType);
                            }
                            ?>
                            <?=implode('，',$arr)?>
                        </h6>
                        <div class="table-responsive b-0">
                            <table class="table table-hover">
                                <thead class="thead-default">
                                <tr>
                                    <th class="fold_head text-center" onclick="fold.showAll()">详</th>
                                    <th style="width: 250px">平台名称</th>
                                    <th style="width: 200px">平台类型</th>
                                    <th style="width: 150px">第一依托单位</th>
                                    <th>产业领域</th>
                                    <th>登录账号</th>
                                    <th>联系人</th>
                                    <th>联系电话</th>
                                    <th class="text-center" width="200">可用操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($user = $pager->getObject()):?>
                                    <tr>
                                        <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                        <td align="left"><a href="<?=site_url('platform/profile/show/userid/'.$user->getPlatformId())?>"><?=$user->getSubject()?></a></td>
                                        <td align="left"><?=$user->getPlatformType()?></td>
                                        <td align="left"><?=$user->getCorporationName()?></td>
                                        <td align="left"><?=$user->getIndustry()?></td>
                                        <td align="left"><?=$user->getUser(true)->getUserName()?></td>
                                        <td align="left"><?=$user->getLinkmanName()?></td>
                                        <td align="left"><?=$user->getLinkmanMobile()?></td>
                                        <td align="center">
                                            <?=Button::setUrl(site_url('platform/profile/base/userid/'.$user->getPlatformId()))->setIcon('edit')->link('修改')?>
                                            <?=Button::setUrl(site_url('platform/office/setpasswd/userid/'.$user->getPlatformId()))->setIcon('fas fa-redo')->setClass('btn-alt-warning')->link('重置密码')?>
                                            <!--
                                            <?=btn('删除','admin/platform/doDelete/platform_id/'.$user->getPlatformId(),'ti-trash','danger','window','800','600')?>
                                            <a href="javascript:void(0);" onclick="return showWindow('报表','<?=site_url('admin/platform/quarter_list/userid/'.$user->getPlatformId())?>',{area:['80%','80%']});" class="btn btn-primary waves-effect waves-light" role="button">报表(<?=$user->getQuarterCount()?>)</a>
                                            -->
                                        </td>
                                    </tr>
                                    <tr class="fold_body">
                                        <td colspan="12"><?php include('more_part.php')?></td>
                                    </tr>
                                <?php endwhile; ?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="9" align="right"><span class="pager_bar">
                                        <?=$pager->fromto().$pager->navbar(10)?>
                                        </span>
                                    </td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>