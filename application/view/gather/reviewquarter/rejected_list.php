<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                已退回的季度监测
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <?=Button::setName('审核选中项')->setEvent('validateForm.action=\''.site_url("gather/reviewquarter/doAccept").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default">
                        <form id="validateForm" name="validateForm" method="post" action="">
                            <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="50">详</th>
                                    <th><?=getColumnStr('项目名称','subject')?></th>
                                    <th width="150"><?=getColumnStr('考核年度','review_year')?></th>
                                    <th width="150"><?=getColumnStr('考核季度','review_quarter')?></th>
                                    <th><?=getColumnStr('申报单位','corporation_id')?></th>
                                    <th><?=getColumnStr('主管部门','department_id')?></th>
                                    <th width="170" class="text-center">状态</th>
                                    <th width="170" class="text-center">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($review = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$review->getReviewId()?>" /></td>
                                        <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                        <td>
                                            <?=$review->getMark()?><?=link_to("apply/reviewquarter/show/id/".$review->getReviewId(),$review->getSubject(),array('target'=>"_blank"))?>
                                        </td>
                                        <td><?=$review->getReviewYear()?></td>
                                        <td><?=$review->getReviewQuarter()?></td>
                                        <td><?=$review->getCorporationName()?></td>
                                        <td><?=$review->getDepartmentName()?></td>
                                        <td><?=$review->getState()?></td>
                                        <td align="center">
                                            <?=Button::setName('审核')->setUrl(site_url("gather/reviewquarter/doAccept/id/".$review->getReviewId()))->setIcon('check')->link()?>
                                        </td>
                                    </tr>
                                    <tr class="fold_body">
                                        <td colspan="12">
                                            <?php include('more_part.php') ?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="12" align="right"><span class="pager_bar">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

