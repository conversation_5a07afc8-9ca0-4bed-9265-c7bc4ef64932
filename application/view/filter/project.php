<link rel="stylesheet" type="text/css" href="<?=site_path("images/filter.css")?>">
<script type="text/javascript">
$(function(){
	$(".slideTxtBox").slide({effect:'left',autoPlay:false,trigger:'click',easing:'swing',delayTime:'500',pnLoop:false });
	$(".inBox").slide({ titCell:".inHd li",mainCell:".inBd",autoPlay:false });
	
	$("label").click(function(){
		if($(this).find("input").attr("checked")) $(this).addClass('active');
		else $(this).removeClass('active');
	});
	
});

function checkscore()
{
	if(($('#highscore').val()<$('#lowscore').val()) && $('#highscore').val()!='')
	{
		$('#highscore').val('');
		$('#highscore').focus();
		alert("范围不正确！");
	}
}
</script>
<form action="" method="post" name="filter" id="filter">
<div class="slideTxtBox">
    <div class="hd">
        <ul><li>基本信息</li><li>项目类型</li><li>归口部门</li><li>项目状态</li></ul>
    </div>
    <div class="bd">
     <div>
     	<div class="title">申报年份</div>
     	<ul class="ckbox_list li60"><?=getYearSearch('declare_year')?></ul>
        <div class="title">分管科室</div>
     	<ul class="ckbox_list li60"><?=getOfficeSearch('office_id')?></ul>
        <div class="title">评审得分</div>
     	<div class="ckbox_list li60">
		<?=get_radio(array('<60','60-70','70-80','80-90','90-100'),'score','','')?>
              分数范围：
              <input id="lowscore" name="lowscore" maxlength="2" style="width:20px;" />
              -
              <input id="highscore" onblur="checkscore();" name="highscore" maxlength="3" style="width:25px;"/>
        </div>
     </div>
     <div>
     	<ul class="ckbox_list li220"><?=getTypeSearch('type_id')?></ul>
     </div>
     <div class="inBox">
     	<div>
        	<div class="inHd"><ul><li>市州科协办</li><li>省级厅局</li><li>扩权强县</li><li>其他类别</li></ul></div>
        </div>
        <div class="inBd">
        	<div><ul class="ckbox_list li180"><?=getGatherSearch('department_id','','9')?></ul></div>
            <div><ul class="ckbox_list li160"><?=getGatherSearch('department_id','','11,12')?></ul></div>
            <div><ul class="ckbox_list li220"><?=getGatherSearch('department_id','','10')?></ul></div>
            <div><ul class="ckbox_list li180"><?=getGatherSearch('department_id','','13')?></ul></div>
        </div>     	
     </div>
     <div >
     	<div class="title">申报书状态</div>
     	<ul class="ckbox_list li160"><?=getStateSearch('statement')?></ul>
        <div class="title">任务书状态</div>
     	<ul class="ckbox_list li160"><?=getStateSearch('state_for_plan_book')?></ul>
        <div class="title">结题书状态</div>
     	<ul class="ckbox_list li160"><?=getStateSearch('state_for_complete_book')?></ul>
     </div>
     
    </div>
</div>
<div class="finish_div"><input type="submit" value="完成" /></div>
</form>