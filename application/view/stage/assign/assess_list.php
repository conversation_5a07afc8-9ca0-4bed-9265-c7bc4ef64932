<div class="main">
  <div class="tools">
    <?=btn('back')?>
    <p style="clear:both;"></p>
  </div>
  <div class="search">
    <?php include_once('search_part.php') ?>
  </div>
  
  <div class="box">
    <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
        <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
        <th>详</th>
        <th><?=getColumnStr('申报编号','accept_id')?></th>
        <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
        <th><?=getColumnStr('项目负责人','user_name')?></th>
        <th width="10%"><?=getColumnStr('研究对象','type_id')?></th>
        <th width="12%"><?=getColumnStr('申报单位','corporation_id')?></th>
        <th><?=getColumnStr('总投入','total_money')?></th>
        <th>未结题项目</th>
        <!-- <th width="70">操作</th> -->
      </tr>
      <?php if($pager->getTotal()):?>
        <?php while($project = $pager->getObject()):?>
          <tr>
            <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
            <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
            <td align="center"><?=$project->getAcceptId()?></td>
            <td><?=$project->getMark()?><?=link_to("declare/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p></td>
            <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
            <td align="center"><?=$project->getTypeSubject()?></td>
            <td align="left"><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
            <td align="center"><?=$project->getTotalMoney()?>万元</td>
            <td align="center"><?=$project->hasUnCompleteProjectString()?></td>
<!--         <td align="center">
          <?=btn("window",'结束',site_url("stage/assign/doSyndicForProject/id/".$project->getProjectId()),'set')?>
          <?php if(isSuper()):?><?=btn('link','分配',site_url("stage/assign/doAssignExpertForProject/id/".$project->getProjectId()),'plus')?><?php endif;?>
        </td> -->
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="14">
          <?php include('more_part.php') ?>
        </td>
      </tr>
    <?php endwhile;?>
    <tr>
      <td colspan="14" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
    </tr>
  <?php else:?>
    <tr>
      <td colspan="14" class="note">暂无符合条件的项目，请选择上面搜索条件重新搜索定位。</td>
    </tr>
  <?php endif;?>
</table>
</div>
</div>
