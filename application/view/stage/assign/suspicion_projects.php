  <div class="box">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
          <thead>
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th width="80"><?=getColumnStr('申报编号','accept_id')?></th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th width="80"><?=getColumnStr('负责人','user_name')?></th>
      <th width="150"><?=getColumnStr('承担单位','type_id')?></th>
      <th width="120"><?=getColumnStr('主管部门','department_id')?></th>
      </tr>
          </thead>
          <tbody>
      <?php foreach($projects as $projectId):
        $project = sf::getModel('Projects')->selectByProjectId($projectId);
      ?>
      <tr>
      <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
      <td align="center"><?=$project->getAcceptId()?></td>
      <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?></td>
      <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
      <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <td><?=$project->getDepartmentName()?></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="9">
        <?php include('more_part.php')?>
        </td>
        </tr>
        <?php endforeach;?>
          </tbody>
      </table>
  </div>
