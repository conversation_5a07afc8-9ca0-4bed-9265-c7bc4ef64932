<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        评审完毕的项目
                    </h3>
                    <div class="block-options">
                        <?=btn('link','导出数据',site_url('export/export/index'),'export')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <div class="box">
                        <div class="no-padding no-margin panel panel-default" >
                            <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/partition/doPartitionByProject")?>">
                                <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                                    <thead>
                                    <tr>
                                        <th width="30">详</th>
                                        <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                                        <th><?=getColumnStr('项目负责人','user_name')?></th>
                                        <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
                                        <th><?=getColumnStr('评审组','type_group')?></th>
                                        <th class="text-center"><?=getColumnStr('总分','score')?></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php while($project = $pager->getObject()):?>
                                        <tr>
                                            <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
                                            <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">平台级别：<?=$project->getLevel()?><br>申报年度：<?=$project->getDeclareYear()?><br>平台类别：<?=$project->getCatSubject()?></p></td>
                                            <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                                            <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                                            <td><?=$project->getTypeGroup()?></td>
                                            <td class="text-center"><?=$project->getScore()?></td>
                                        </tr>
                                        <tr id="show_<?=$project->getId()?>" style="display:none;">
                                            <td colspan="16">
                                                <?php include('more_part.php') ?>
                                            </td>
                                        </tr>
                                    <?php endwhile;?>
                                    </tbody>
                                    <tfoot>
                                    <tr>
                                        <td colspan="16" align="right">
                                            &nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                                    </tr>
                                    </tfoot>
                                </table>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
