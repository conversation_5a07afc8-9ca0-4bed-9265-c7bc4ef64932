<!doctype html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="UTF-8">
    <title><?=config::get("site_name")?></title>
    <link rel="shortcut icon" href="<?=site_path('assets/media/favicons/favicon.png')?>">
    <link rel="icon" type="image/png" sizes="192x192" href="<?=site_path('assets/media/favicons/favicon-192x192.png')?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?=site_path('assets/media/favicons/apple-touch-icon-180x180.png')?>">
    <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/compress.min.css')?>">
    <link rel="stylesheet" href="<?=site_path('assets/css/login.css')?>" />
    <script src="<?=site_path('assets/js/compress.core.min.js')?>"></script>
    <script src="<?=site_path('assets/js/compress.app.min.js')?>"></script>
    <script src="<?=site_path('assets/js/bootstrapValidator.min.js')?>"></script>
    <style>
        .img-avatar{
            width: 128px;
            height: 128px;
            border-radius:0;
        }
        .role-icon{
            padding: 16px;
        }
        .has-error .form-control {
             border-color: #e04f1a;
         }
        .has-success .form-control {
            border-color: #82b54b;
        }
        .has-error .help-block{
            display: none;
            width: 100%;
            margin-top: 0.5rem;
            font-size: .875rem;
            color: #e04f1a;
        }
        .has-feedback .form-control {
            padding-right: 42.5px;
        }
        .form-control-feedback {
            position: absolute;
            top: 25px;
            right: 0;
            z-index: 2;
            display: block;
            width: 34px;
            height: 34px;
            line-height: 34px;
            text-align: center;
        }
        .form-horizontal .has-feedback .form-control-feedback {
            top: 0;
            right: 12px;
        }
        .has-error .form-control-feedback {
            color: #a94442;
        }
        .has-success .form-control-feedback {
            color: #3c763d;
        }
    </style>
</head>
<body>
<div class="head" style="background-color: #fff">
    <div class="main">
        <div id="logo"><i class="fa fa-stethoscope font-w600" style="font-size: 24px"></i><span class="font-w600" style="line-height: 90px;font-size: 24px;margin-left: 15px;"><?=config::get("site_name")?></span></div>
        <div style="float: right;margin-top: 30px">
            <div class="dropdown d-inline-block">
                <button type="button" class="btn btn-dual" id="page-header-user-dropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-fw fa-user d-sm-none"></i> <span class="d-none d-sm-inline-block"><?=input::getInput('session.nickname')?></span> <i class="fa fa-fw fa-angle-down ml-1 d-none d-sm-inline-block"></i></button>
                <div class="dropdown-menu dropdown-menu-right p-0" aria-labelledby="page-header-user-dropdown">
                    <div class="p-2">
                        <a class="dropdown-item" href="<?= site_url('login/logout') ?>"><i class="far fa-fw fa-arrow-alt-circle-left mr-1"></i>退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="body" style="background:none;">
    <div class="main">
        <div class="row" style="justify-content: center;">
            <div class="col-md-12">
                <div class="content">
                    <div class="block">
                        <div class="block-content">
                            <div class="alert alert-warning alert-dismissable" role="alert">
                                <h3 class="alert-heading font-size-h4 my-2 text-center">
                                    提示
                                </h3>
                                <p class="mb-0 text-center">您的账号尚未激活！还需完善以下资料：</p>
                            </div>

                            <form class="form-horizontal" name="validateForm" id="validateForm" enctype="multipart/form-data" action="" method="post">
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">主管部门名称<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" value="<?=$department->getSubject()?>" readonly title="请填写您的真实姓名">
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">登录账号<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="user_name" name="user_name" value="<?=$user->getUserName()?>" placeholder="请设置登录账号">
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">初始密码<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="user_password" name="user_password" value=""  placeholder="请输入原密码">
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">新密码<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="new_password" name="new_password" value="" placeholder="请设置新的登录密码">
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">管理员姓名<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="personname" name="personname" value="" placeholder="请填写您的真实姓名">
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">身份证号<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="user_idcard" name="user_idcard" value="" placeholder="请填写有效的18位公民身份证号码" required>
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">手机<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="user_mobile" name="user_mobile" value="<?=$user->getUserMobile()?>" placeholder="请填写你常用的手机号码" required>
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">座机</label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="user_phone" name="user_phone" value="<?=$user->getUserPhone()?>" placeholder="请填写你办公室的座机号码，例如：028-65238321">
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">电子邮箱<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="email" class="form-control" id="user_email" name="user_email" value="" placeholder="请填写你常用的电子邮箱" required>
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">所在部门<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5">
                                        <input type="text" class="form-control" id="linkman_department" name="linkman_department" value="" placeholder="请填写你所在的部门名称" required>
                                    </div>
                                </div>
                                <div class="form-group row center">
                                    <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">验证码<span class="text-danger">* </span></label>
                                    <div class="col-xs-12 col-sm-5 col-md-5" style="position: relative">
                                        <input type="text" class="form-control" name="safe_code" placeholder="验证码" style="width: 150px;display: inline-block;">
                                        <img title="点击切换" src="<?=site_url("common/index")?>" alt="" id="captcha" style="width: 130px;cursor: pointer;margin-top: -10px;" onClick="$('#captcha').attr('src','<?=site_url("common/index/s/")?>' + Math.random());">
                                    </div>
                                </div>

                                <div class="form-group text-center">
                                    <?=Button::setType('submit')->setIcon('fa fa-check-circle')->setSize('btn-lg')->button('激活账号')?>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="footer">
    <p><?=config::get('copyright')?></p>
</div>
<script src="<?=site_path('js/lay/layer/layer.js') ?>"></script>
<script src=<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script>
<script type="text/javascript">
    $(document).ready(function() {
        $('#validateForm').bootstrapValidator({
            message: '填写内容无效',
            feedbackIcons: {
                valid: 'fa fa-check',
                invalid: 'fa fa-times',
                validating: 'si si-refresh'
            },
            fields: {
                'user_name': {
                    validators: {
                        notEmpty: {
                            message: '登录账号必须填写'
                        },
                        regexp: {
                            regexp: /^[A-Za-z0-9]+$/i,
                            message: '登录账号只能由字母和数字组成'
                        },
                        remote: {
                            type: 'POST',
                            url: '<?=site_url("user/profile/valid")?>',
                            message: '登录账号无效请重新设置登录账号',
                            delay: 1000
                        },
                    }
                },
                'user_password': {
                    validators: {
                        notEmpty: {
                            message: '初始密码必须填写'
                        },
                        remote: {
                            type: 'POST',
                            url: '<?=site_url("user/profile/valid")?>',
                            message: '登录账号无效请重新设置登录账号',
                            delay: 1000
                        },
                    }
                },
                'new_password': {
                    validators: {
                        notEmpty: {
                            message: '新密码必须填写'
                        },
                        regexp: {
                            regexp: /^(?![0-9]+$)(?![a-zA-Z]+$)[a-zA-Z0-9]{8,50}$/i,
                            message: '密码长度至少8位且同时包含数字和字母'
                        },
                        different: {
                            field: 'user_password',
                            message: '新密码不能与初始密码相同'
                        }
                    }
                },
                'personname': {
                    validators: {
                        notEmpty: {
                            message: '管理员姓名必须填写'
                        }
                    }
                },
                'user_idcard': {
                    validators: {
                        notEmpty: {
                            message: '身份证号码必须填写'
                        },
                        remote: {
                            type: 'POST',
                            url: '<?=site_url("user/profile/valid")?>',
                            message: '身份证号码不正确',
                            delay: 1000
                        }
                    }
                },
                'user_mobile': {
                    validators: {
                        notEmpty: {
                            message: '手机号码必须填写'
                        },
                        remote: {
                            type: 'POST',
                            url: '<?=site_url("user/profile/valid")?>',
                            message: '手机号码不正确',
                            delay: 1000
                        }
                    }
                },
                'user_email': {
                    validators: {
                        notEmpty: {
                            message: '电子邮箱必须填写'
                        },
                        emailAddress: {
                            message: '请填写有效的电子邮箱地址'
                        }
                    }
                },
                'linkman_department': {
                    validators: {
                        notEmpty: {
                            message: '所在部门必须填写'
                        }
                    }
                },
                'safe_code': {
                    validators: {
                        notEmpty: {
                            message: '安全验证码必须填写'
                        },
                        remote: {
                            type: 'POST',
                            url: '<?=site_url("register/account/valid")?>',
                            message: '安全校验码不正确',
                            delay: 1000
                        }
                    }
                }
            }
        });
    });
</script>
</body>
</html>
