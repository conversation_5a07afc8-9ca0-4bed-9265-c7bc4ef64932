<div class="col-xs-12 col-sm-12 col-md-12">
<div class="btn-group btn-group-sm" role="group">
<a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a>
<p style="clear:both"></p>
</div>
<div class="search">
<?php include("search_part.php")?>
</div>
<div class="box">
<table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
  <form id="validateForm" name="validateForm" method="post" action="">
    <tr>
      <th><input name="selectAll" id="selectAll" type="checkbox" /></th>
	  <th>详</th>
       <th width="140"><?=getColumnStr('姓名','user_name')?></th>
      <th width="50"><?=getColumnStr('性别','user_sex')?></th>
      <th width="80"><?=getColumnStr('证件类型','card_name')?></th>
      <th width="150"><?=getColumnStr('证件号','card_id')?></th>
      <th width="60"><?=getColumnStr('学位','user_degree')?></th>
      <th><?=getColumnStr('职称','user_honor')?></th>
      <th><?=getColumnStr('工作单位','work_unit')?></th>
      <th width="60"><?=getColumnStr('等级','user_grade')?></th>
      <th>可用操作</th>
    </tr>
    <?php while($user = $pager->getObject()):?>
    <tr>
      <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$user->getUserId()?>" /></td>
      <td align="center"><label style="cursor:pointer;" onclick="$('#show_<?=$user->getUserId()?>').toggle()"><strong>+</strong></label></td>
      <td><?=$user->getMark()?><?=link_to("expert/profile/show/userid/".$user->getUserId(),$user->getUserName(0,true))?></td>
      <td align="center"><?=$user->getUserSex()?></td>
      <td align="center"><?=$user->getCardName()?></td>
      <td><?=$user->getCardId(0,true)?></td>
      <td align="center"><?=$user->getUserDegree()?></td>
      <td align="center"><?=$user->getUserHonor()?></td>
      <td><?=$user->getWorkUnit()?></td>
      <td align="center"><?=$user->getUserGrade()?></td>
      <td align="center"><a href="<?=site_url("admin/expert/delete/userid/".$user->getUserId())?>" class="btn btn-sm btn-danger"><i class="ace-icon glyphicon glyphicon-remove-circle"></i> 删除</a></td>
    </tr>
      <tr id="show_<?=$user->getUserId()?>" style="display:none;">
        <td colspan="11">
         <?php  include('more_part.php');?>
        </td>
        </tr>
    <?php endwhile; ?>
    <tr>
      <td colspan="11"><span class="pager_bar">
        <?=$pager->fromto().$pager->navbar(10)?>
        </span></td>
    </tr>
  </form>
</table>
</div>
</div>