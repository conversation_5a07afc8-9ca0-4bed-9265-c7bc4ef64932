<link rel="stylesheet" href="<?=site_path('assets/css/bootstrapValidator.min.css')?>" />
<script src="<?=site_path('assets/js/bootstrapValidator.min.js')?>"></script>
<div class="panel-body">
  <form name="validateForm" id="validateForm" method="post" enctype="multipart/form-data" action="" class="form-horizontal">
    <fieldset>
      <div class="form-group row">
        <label for="user_username"  class="col-sm-3 control-label text-right">姓名 <font color="red">*</font></label>
        <div class="col-sm-5">
          <input name="user_username" type="text" id="user_username" value="<?=$user->getUserUsername()?>" class="form-control col-sm-5" />
        </div>
      </div>
      <div class="form-group row">
        <label for="user_name" class="col-sm-3 control-label text-right">登录账号 <font color="red">*</font></label>
        <div class="col-sm-5">
          <input name="user_name" type="text" id="user_name" value="<?=$user->getUserName()?>" class="form-control" />
        </div>
      </div>
      <div class="form-group row">
        <label for="user_idcard_type" class="col-sm-3 control-label text-right">证件类型 <font color="red">*</font></label>
        <div class="col-sm-5">
          <input name="user_idcard_type" type="text" id="user_idcard_type"  value="<?=$user->getUserIdcardType()?>" class="form-control col-sm-5" />
        </div>
      </div>
      <div class="form-group row">
        <label for="user_idcard" class="col-sm-3 control-label text-right">身份证号 <font color="red">*</font></label>
        <div class="col-sm-5">
          <input name="user_idcard" type="text" id="user_idcard"  value="<?=$user->getUserIdcard()?>" class="form-control" />          <p class="help-block"><a href="javascript:void();" onclick="ikown('user_idcard')" class="btn btn-xs btn-danger">知道了</a></p>
        </div>
      </div>
      <div class="form-group row">
        <label for="user_mobile" class="col-sm-3 control-label text-right">手机号码 <font color="red">*</font></label>
        <div class="col-sm-5">
          <input name="user_mobile" type="text" id="user_mobile" value="<?=$user->getUserMobile()?>" class="form-control" />
          <p class="help-block"><a href="javascript:void();" onclick="ikown('user_mobile')" class="btn btn-xs btn-danger">知道了</a></p>
        </div>
      </div>
      <div class="form-group row">
        <label for="user_email" class="col-sm-3 control-label text-right">安全邮箱 <font color="red">*</font></label>
        <div class="col-sm-5">
          <input name="user_email" type="email" id="user_email" value="<?=$user->getUserEmail()?>" class="form-control" />
          <p class="help-block"><a href="javascript:void();" onclick="ikown('user_email')" class="btn btn-xs btn-danger">知道了</a></p>
        </div>
      </div>
      <div class="form-group row">
        <label for="submit" class="col-sm-3 control-label text-right"></label>
        <div class="col-sm-5">
          <button type="submit" class="btn btn-info btn-sm">保存修改</button> <input name="userid" type="hidden" id="userid" value="<?=$user->getUserId()?>" class="form-control" />
        </div>
      </div>
    </fieldset>
  </form>
</div>
<script type="text/javascript">
$(document).ready(function() {
    $('#validateForm').bootstrapValidator({
        message: '填写内容无效',
        feedbackIcons: {
            valid: 'glyphicon glyphicon-ok',
            invalid: 'glyphicon glyphicon-remove',
            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
            'user_username': {
                message: '真实姓名必须填写',
                validators: {
                    notEmpty: {
                        message: '真实姓名不能为空'
                    }
                }
            },
			'user_name': {
                message: '登录账号必须填写',
                validators: {
                    notEmpty: {
                        message: '登录账号必须填写'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("admin/account/valid/userid/".$user->getUserId())?>',
                        message: '登录账号无效请重新设置登录账号',
                        delay: 1000
                    },
                    different: {
                        field: 'user_password',
                        message: '登录名和密码不能一样'
                    }
                }
            },
            'user_email': {
                validators: {
                    notEmpty: {
                        message: '电子邮箱不能为空'
                    },
                    emailAddress: {
                        message: '请填写有效的电子邮箱地址'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("admin/account/valid/userid/".$user->getUserId())?>',
                        message: '电子邮箱已经注册',
                        delay: 2000
                    }
                }
            },
			'user_idcard': {
                validators: {
                    notEmpty: {
                        message: '身份证号码必须填写'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("admin/account/valid/userid/".$user->getUserId())?>',
                        message: '身份证号码不正确',
                        delay: 2000
                    }
                }
            },
			'user_mobile': {
                validators: {
                    notEmpty: {
                        message: '手机号码必须填写'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("admin/account/valid/userid/".$user->getUserId())?>',
                        message: '手机号码不正确',
                        delay: 1000
                    }
                }
            },
			'safe_code': {
                validators: {
                    notEmpty: {
                        message: '安全验证码必须填写'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("admin/account/valid")?>',
                        message: '安全校验码不正确',
                        delay: 1000
                    }
                }
            }
        }
    });
});

function ikown(filed)
{
	 $('#validateForm').data('bootstrapValidator').enableFieldValidators(filed, false);	
}
</script>