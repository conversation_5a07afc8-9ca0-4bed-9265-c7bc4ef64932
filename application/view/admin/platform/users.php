<script type="text/javascript">
function todo(json)
{
	$.post('<?=site_url("admin/corporation/coupling")?>',{cid:"<?=$corporation->getUserId()?>",userid:json.userid,username:json.username},function(json){
		eval("json = '"+json+"'");
		if(json.msg) alert(json.msg);
		document.location.reload();		
	});
}
</script>
<div class="col-xs-12 col-sm-12 col-md-12">
<div class="btn-group btn-group-sm" role="group"> <a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a> <a href="javascript:void(0);" onclick="return showWindow('查看记录','<?=site_url("admin/history/index/id/".$corporation->getUserId())?>',450,300);" class="btn btn-info" role="button">查看记录</a> <a href="javascript:void(0);" onclick="return showWindow('新增账号','<?=site_url("common/account_search")?>',450,300);" class="btn btn-info" role="button">新增账号</a>
  <p style="clear:both;"></p>
  </div>
  <div class="box">
    <form method="post" name="validateForm" id="validateForm" action="">
    <table width="100%" cellpadding="3" cellspacing="1" class="tb_data">
      <caption>
      与之关联的账号
      </caption>
      <?php $users = $corporation->selectUsers();
	  if($users->getTotal()):
	  ?>
      <tr>
        <th width="5%" class="tselect"><input name="selectAll" id="selectAll" type="checkbox" /></th>
        <th width="10%">登录名</th>
        <th width="10%">姓名</th>
        <th width="19%">证件号</th>
        <th width="16%">手机号码</th>
        <th width="21%">电子邮箱</th>
        <th width="19%">操作</th>
      </tr>
      <?php while($user = $users->getObject()):?>
      <tr>
        <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$user->getUserId()?>" /></td>
        <td align="center"><?=$user->getUserName()?></td>
        <td align="center"><?=$user->getUserUsername()?></td>
        <td align="center"><?=$user->getUserIdcard()?></td>
        <td align="center"><?=$user->getUserMobile()?></td>
        <td align="center"><?=$user->getUserEmail()?></td>
        <td align="center"><a href="<?=site_url("admin/account/setpasswd/userid/".$user->getUserId())?>" onclick="return confirm('密码重置后原来的密码立即失效，你确定重置？')" class="btn btn-danger btn-xs">密码</a> <a href="<?=site_url("admin/account/edit/userid/".$user->getUserId())?>" class="btn btn-info btn-xs">修改</a> <a href="<?=site_url("admin/corporation/decoupling/companyid/".$corporation->getUserId()."/userid/".$user->getUserId())?>" class="btn btn-danger btn-xs">解绑</a></td>
      </tr>
      <?php endwhile; ?>
      <?php else:?>
      <tr>
        <td class="note">没有与之关联的账号！</td>
      </tr>
      <?php endif;?>
    </table>
    </form>
  </div>
</div>
