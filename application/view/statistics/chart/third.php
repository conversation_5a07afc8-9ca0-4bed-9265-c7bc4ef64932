<div class="main">
  <div class="btn-group btn-group-sm" role="group"><a href="<?=site_url("statistics/chart/index")?>" class="btn btn-info" role="button"><i class="glyphicon glyphicon-th-list"></i>统计图列表</a></div>
  <div class="box">
  <form name="validateForm" id="validateForm" method="post" action="">
      <table align="center" width="100%" border="0" cellspacing="1" cellpadding="2" class="tb_data table table-hover table-striped" >
        <?php $y = -1; while($serie = $series->getObject()):?>
        <?php $y++;?>
        <?php foreach($data as $val):?> 
        <?php if($val['sid'] == $serie->getId() && $serie->getSeriesType() == 'pie'):?>
        <tr>
        <td colspan="3"><input type="hidden" name="data[<?=$y?>][sid]" class="data"  value="<?=$serie->getId()?>" checked='checked'  />
        <?=$serie->getSubject()?></td>
        </tr>
        <tr>
          <?php $series_source = $chart->getSeriesSource(); ?>
          <th rowspan="3">饼图属性&nbsp;</th>
          <th width="15%">饼图位置</th>
          <td width="68%">X
            <input type="text" name="series_source[center][]" id=""  value="<?=$series_source[0]['center'][0]?>" />
            &nbsp;Y
            <input type="text" name="series_source[center][]" id="" value="<?=$series_source[0]['center'][1]?>" /></td>
        </tr>
        <tr>
          <th>饼图大小</th>
          <td><input type="text" name="series_source[size]" id="" class="required" value="<?=$series_source[0][size]?>" /></td>
        </tr>
        <tr>
          <th>单位</th>
          <td><?php  for($i=1;$i<count($chart->getChartYAxis())+1;$i++):?>
            <input type="radio" name="data[<?=$y?>][yAxis]" class="required" value="<?=$i-1?>" <?php $chart_data = $chart->getDatayAxis(); if($chart_data[$y] == $i) echo "checked='checked'" ;  ?>  />
            Y
            <?=$i?>
        <?php endfor;?></td>
        </tr>
        <?php elseif($val['sid'] == $serie->getId() && $serie->getSeriesType() != 'pie' ):?>
        <tr>
        <td width="17%"><input type="hidden" name="data[<?=$y?>][sid]" class="data"  value="<?=$serie->getId()?>" checked='checked'  />
        <?=$serie->getSubject()?></td>
        <td colspan="2">
        <?php  for($i=1;$i<count($chart->getChartYAxis())+1;$i++):?>
            <input type="radio" name="data[<?=$y?>][yAxis]" class="required" value="<?=$i-1?>" <?php $chart_data = $chart->getDatayAxis(); if($chart_data[$y] == $i) echo "checked='checked'" ;  ?>  />
            Y
            <?=$i?>
        <?php endfor;?>
        </td></tr>
        <?php endif;?>
        <?php endforeach;?>
        <?php endwhile;?>
        <tr><td colspan="3" align="center"><input type="submit" name="submit" value="下一步"  style=" background-color:#D8F5F4; width:80px;" /></td></tr>
      </table>
  </form>
  </div>
</div>
