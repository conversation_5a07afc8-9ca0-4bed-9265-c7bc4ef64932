<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            个人中心/资料维护
        </h2>
        <div>
            <?=$htmlStr?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                    <?php endforeach;?>
                </ul>
                <div class="block-content tab-content">
                    <div class="tab-pane active">
                        <form class="form-horizontal" id="form_7" name="validateForm" action="" method="post">
                            <p>个人工作经历<small class="red">（按时间顺序填写）</small></p>
                            <table width="100%" class="table table-hover">
                                <thead>
                                <tr>
                                    <td width="7%" align="center">起始时间</td>
                                    <td width="7%" align="center">结束时间</td>
                                    <td width="15%" align="center">地点</td>
                                    <td width="15%" align="center">工作单位</td>
                                    <td width="15%" align="center">工作部门</td>
                                    <td width="8%" align="center">职务</td>
                                    <td width="8%" align="center">操作</td>
                                </tr>
                                </thead>
                                <tbody id="paper-content">
                                <?php
                                $works = $user->selectWorks();
                                while($work = $works->getObject()):
                                    ?>
                                    <tr>
                                        <td>
                                            <input type="hidden" name="ids[]" value="<?=$work->getId()?>" /><input name="start_at[]" type="text" class="form-control" value="<?=$work->getStartAt()?>"  data-com="date" data-format="yyyy-mm" />
                                        </td>
                                        <td><input name="end_at[]" type="text" class="form-control" value="<?=$work->getEndAt()?>"  /></td>
                                        <td><input name="place[]" type="text" class="form-control" value="<?=$work->getPlace()?>"  /></td>
                                        <td><input name="unit[]" type="text" class="form-control" value="<?=$work->getUnit()?>"  /></td>
                                        <td><input name="department[]" type="text" class="form-control" value="<?=$work->getDepartment()?>"  /></td>
                                        <td><input name="position[]" type="text" class="form-control" value="<?=$work->getPosition()?>"  /></td>
                                        <td width="8%" align="center">
                                            <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
                                            <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                <tr>
                                    <td>
                                        <input type="hidden" name="ids[]" value="" /><input name="start_at[]" type="text" class="form-control start_at" value=""  data-com="date" data-format="yyyy-mm" data-icon="none" id="start_at" />
                                    </td>
                                    <td><input name="end_at[]" type="text" class="form-control" value=""  data-com="date" data-format="yyyy-mm" data-icon="none" /></td>
                                    <td><input name="place[]" type="text" class="form-control" value=""  /></td>
                                    <td><input name="unit[]" type="text" class="form-control" value=""  /></td>
                                    <td><input name="department[]" type="text" class="form-control" value=""  /></td>
                                    <td><input name="position[]" type="text" class="form-control" value=""  /></td>
                                    <td width="8%" align="center">
                                        <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
                                        <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="form-group row">
                                <div class="col-sm-8 ml-auto">
                                    <?=btn('button','保存资料','submit','save')?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>