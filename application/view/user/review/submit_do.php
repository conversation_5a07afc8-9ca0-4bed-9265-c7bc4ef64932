<div class="content">
    <div class="block">
        <div class="block-content">
            <div class="page-header">
                <div class="btn-group btn-group-sm" role="group"> <a href="javascript:history.back();" class="btn btn-info"><i class="ace-icon fa fa-arrow-left"></i> 返回</a><a href="<?=site_url("apply/review/edit/id/".$review->getReviewId())?>" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-pencil"></i> 编辑</a>
                </div>
            </div>
            <div class="page-body mt-3">
                <div class="alert alert-success" role="alert">
                    <h4>温馨提示：</h4>
                    <ul>
                        <li>1. 任务清单报告一经上报将不可更改；</li>
                        <li>2. 上报后还需承担单位、主管部门进行审核。</li>
                    </ul>
                </div>
                <?php if(count($msg)):?>
                    <div class="alert alert-danger" role="alert">
                        <h4>系统检查到您的任务清单报告有错误，请检查并修改这些错误：</h4>
                        <ul>
                            <?php for($i=0,$n=count($msg);$i<$n;$i++):?>
                                <li>
                                    <?=($i+1)?>
                                    、
                                    <?=$msg[$i]?>
                                </li>
                            <?php endfor;?>
                        </ul>
                    </div>
                <?php endif;?>
                <div style="margin-bottom: 10px">
                    <form name="up" id="up" method="post" action="<?=site_url("user/review/doSubmit/id/".$review->getReviewId())?>">
                        <?php if(count($msg)): ?>
                            <button type="button" class="btn btn-danger" disabled="disabled" ><i class="ace-icon glyphicon glyphicon-ban-circle"></i> 请返回修改后再上报！</button>
                        <?php else:?>
                            <?=Button::setType('submit')->setEvent("return confirm('中期评估报告一经上报将不可更改，您确定上报？')")->setIcon('send')->setClass("btn-alt-primary")->setSize('btn-lg')->button('上报资料');?>
                        <?php endif;?>
                        <input name="id" type="hidden" id="id" value="<?=$review->getReviewId();?>" />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
