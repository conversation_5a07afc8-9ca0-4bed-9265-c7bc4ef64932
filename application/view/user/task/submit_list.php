<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                已上报的任务书
            </h3>
            <div class="block-options">
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("user/task/doSubmit")?>">
                        <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="50">详</th>
                                <th><?=getColumnStr('立项编号','radicate_id')?></th>
                                <th><?=getColumnStr('项目名称','subject')?></th>
                                <th><?=getColumnStr('申报单位','corporation_id')?></th>
                                <th><?=getColumnStr('主管部门','department_id')?></th>
                                <th width="170" class="text-center">审核状态</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($project = $pager->getObject()):?>
                                <tr>
                                    <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                    <td><?=$project->getRadicateId()?></td>
                                    <td>
                                        <?=$project->getMark()?><?=link_to("apply/task/show/id/".$project->getProjectId(),$project->getSubject(),array('target'=>"_blank"))?>
                                    </td>
                                    <td><?=$project->getCorporationName()?></td>
                                    <td><?=$project->getDepartmentName()?></td>
                                    <td><?=$project->getStateForTask()?></td>
                                </tr>
                                <tr class="fold_body">
                                    <td colspan="9">
                                        <?php include('more_part.php')?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="9" align="right">&nbsp;<?=$pager->fromTo().$pager->navbar(10)?></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

