<div class="alert no-padding">
  <table class="table table-sm table-more no-margin table-sm " >
    <tr>
      <th width="15%">项目名称</th>
      <td width="35%"><?=$project->getSubject()?>&nbsp;
        <?php if(in_array(input::getInput("session.userlevel"),array('1'))):?>
            <a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/super/superEdit/id/".$project->getProjectId())?>');"  class="btn btn-danger btn-sm" role="button">编辑项目</a> <a href="javascript:void(0);" onclick="return showWindow('项目备注','<?=site_url("admin/super/doNote/id/".$project->getProjectId())?>',{area:['600px','410px']});" class="btn btn-success btn-sm" role="button">备注</a>
        <?php endif;?>
      </td>
      <th width="15%">申报编号</th>
      <td width="35%"><?=$project->getAcceptId()?></td>
    </tr>
    <tr>
      <th>申报年度</th>
      <td><?=$project->getDeclareYear()?></td>
      <th>主管部门</th>
      <td><?=$project->getDepartmentName()?></td>
    </tr>
    <tr>
      <th>承担单位</th>
      <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <th>项目负责人</th>
      <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?>
        &nbsp;
        <?php if(in_array(input::getInput("session.userlevel"),array('1'))):?>
          <a href="javascript:void(0);" onclick="return showWindow('更换负责人','<?=site_url("common/user_search/itemid/".$project->getProjectId())?>');" class="btn btn-danger btn-sm" role="button">更换</a>
        <?php endif;?>
      </td>
    </tr>
    <tr>
      <th>立项年度</th>
      <td><?=$project->getRadicateYear(true)?>&nbsp;【评审分组：<?=$project->getTypeGroup()?>】</td>
      <th>申报时间</th>
      <td><?=$project->getDeclareAt("Y-m-d")?></td>
    </tr>
    <tr>
      <th>评审得分</th>
      <td><?=$project->getScore()?></td>
      <th>申报书资料</th>
      <td>
        <a href="<?=site_url("apply/project/show/id/".$project->getProjectId())?>" class="btn btn-info btn-sm" role="button">查 看</a>
        <a href="<?=site_url("apply/project/download/id/".$project->getProjectId())?>" target="_blank" class="btn btn-info btn-sm" role="button">下 载</a>
      </td>
    </tr>
    <tr>
      <th>所属分组</th>
      <td><?=$project->getTypeGroup()?></td>
      <th>操作记录</th>
      <td>
        <a href="javascript:void(0);" onclick="return showWindow('<i class=\'ace-icon fa fa-history\'></i> 历史记录','<?=site_url("admin/history/index/id/".$project->getProjectId())?>',{area:['500px','520px']});" class="btn btn-info btn-sm" role="button">查看记录</a>
        <!-- <a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/commend/index/id/".$project->getProjectId())?>',{area:['500px','520px']});" class="btn btn-info btn-sm" role="button">推荐记录</a> -->
        <!-- <a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("office/payfor/index/id/".$project->getProjectId())?>',{area:['500px','520px']});" class="btn btn-info btn-sm" role="button">拨款记录</a> -->
      </td>
    </tr>
    <?php if($project->getStatement() >= 29):?>
      <tr>
        <th colspan="4">以下信息仅立项项目可见 &gt;&gt;&gt;</th>
      </tr>
      <tr>
        <th>立项编号</th>
        <td colspan="3"><?=$project->getRadicateId()?>&nbsp;
          <?php if(in_array(input::getInput("session.userlevel"),array('1','5','6'))):?><a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/super/changeRadicate/id/".$project->getProjectId())?>',450,330);" class="btn btn-danger btn-sm" role="button">编辑</a>
          <?php endif;?>
        </td>
      </tr>
      <tr>
        <th>任务书</th>
        <td><?=$project->getStateForTask()?>
          <?php if($project->getStateForPlanBook() > 0):?>
            <a href="<?=site_url("declare/task/show/id/".$project->getProjectId())?>" class="btn btn-info btn-sm" role="button">查看</a>
            <!-- <a href="<?=site_url("declare/task/output/id/".$project->getProjectId())?>" target="_blank" class="btn btn-info btn-sm" role="button">导出</a> -->
          <?php endif;?>
        </td>
        <th>验收书</th>
        <td><?=$project->getStateForComplete()?>
          <?php if($project->getStateForCompleteBook() > 0):?>
            <a href="<?=site_url("declare/complete/show/id/".$project->getProjectId())?>" class="btn btn-info btn-sm" role="button">查 看</a>
            <!-- <a href="<?=site_url("declare/complete/output/id/".$project->getProjectId())?>" target="_blank" class="btn btn-info btn-sm" role="button">导出</a> -->
          <?php endif;?>
        </td>
      </tr>
    <?php endif;?>
</table>
</div>