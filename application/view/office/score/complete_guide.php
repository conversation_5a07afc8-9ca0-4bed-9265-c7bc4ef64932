<style>
    table.snote td{
        padding: 0.25rem;
        border:none;
        border-bottom: 1px solid #e2e8f2;
    }
</style>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                项目检索
            </h3>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <form action="" method="post" name="search" id="search">
                        <table width="100%" align="center">
                            <tbody id="show_search">
                            <tr>
                                <td>
                                    <label style="float:left;margin-right: 15px;margin-top: 5px;">验收指南:</label>
                                    <div class="cxselect" data-selects="guide_id1,guide_id2,guide_id3"
                                         data-url="<?= site_path('json/guide_completes.json') ?>" data-json-value="v" style="float:left">
                                        <select class="form-control w-auto custom-control-inline guide_id guide_id1" data-value="<?=$guide->getYear()?>"
                                                name="guide_id1"></select>
                                        <select class="form-control w-auto custom-control-inline guide_id guide_id2" name="guide_id2"
                                                data-first-title="请选择" data-value="<?=$guide->getId()?>"></select>
                                        <select class="form-control w-auto custom-control-inline guide_id guide_id3" name="guide_id3"
                                                data-first-title="请选择" data-value="<?=input::getMix('guide_id3')?>"></select>
                                        <input type="hidden" name="guide_id" id="guide_id" value="<?=input::getInput("mix.guide_id")?>">
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="form-group row center stat" style="display: none">
                            <div style="width: 200px;margin: 50px auto;">
                                <?=Button::setType('button')->setClass('btn-alt-primary query')->setSize('btn-lg')->setIcon('check')->button('查看分数')?>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    $(function (){
        $(".guide_id").change(function () {
            $("#guide_id").val($(this).val());

            if($('.guide_id3').val() && $(this).val()){
                $(".stat").show();
            }else{
                $(".stat").hide();
            }
        })

        $(".query").click(function (){
            var guide_id = $("#guide_id").val();
            var url = '<?=site_url('office/score/complete_list')?>'+'?guide_id='+guide_id;
            location.href=url;
        });
    });
</script>
