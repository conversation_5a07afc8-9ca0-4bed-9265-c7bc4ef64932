<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a>
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button">导出数据</a>
   <!-- <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a> -->
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php') ?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
        <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
        <th>详</th>
        <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
        <th><?=getColumnStr('项目负责人','user_name')?></th>
        <th width="10%"><?=getColumnStr('研究对象','type_id')?></th>
        <th width="10%"><?=getColumnStr('申报单位','corporation_id')?></th>
        <th width="10%"><?=getColumnStr('归口部门','department_id')?></th>
        <th><?=getColumnStr('申请经费','declare_money')?></th>
        <th><?=getColumnStr('总投入','total_money')?></th>
        <th>建议资金</th>
        <!-- <th><?=getColumnStr('起始年月','start_at')?></th> -->
        <!-- <th><?=getColumnStr('结束年月','end_at')?></th> -->
        <th><?=getColumnStr('评审得分','score')?></th>
        <th>未结题项目</th>
        <th width="120">操作</th>      
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr>
        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p></td>
        <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
        <td align="center"><?=$project->getTypeSubject()?></td>
        <td align="center"><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
        <td align="center"><?=$project->getDepartmentName()?></td>
        <td align="center"><?=$project->getDeclareMoney()?>万元</td>
        <td align="center"><?=$project->getTotalMoney()?>万元</td>
        <td align="center">
          <?php if ($project->getCommendMoney()>$project->getDeclareMoney()): ?>
            <span class="red"><?=$project->getCommendMoney()?>万元</span>
          <?php else: ?>
            <?=$project->getCommendMoney()?>万元
          <?php endif ?>
        </td>
        <!-- <td align="center"><?=$project->getStartAt()?></td> -->
        <!-- <td align="center"><?=$project->getEndAt()?></td> -->
        <td align="center"><?=$project->getScore()?></td>
        <td align="center"><?=$project->hasUnCompleteProjectString()?></td>
        <td align="center">
<!--           <?php if($project->getStatement(true) == 25):?>
            <a href="javascript:void(0);"  onclick="return showWindow('退回项目','<?=site_url("office/project/doBack/id/".$project->getProjectId())?>',400,300);" title="将项目退回给项目负责人" class="btn btn-xs btn-info"><i class="glyphicon glyphicon-chevron-left"></i>退回</a>
          <?php else:?>
            <a href="javascript:void(0);" onclick="return false;" title="项目已经立项不能回退" class="btn btn-xs btn-info">锁定</a> 
          <?php endif;?> 
          <?php if($project->getTaskOpen()):?>
            <a href="javascript:void(0);"  onclick="return showWindow('取消预立项','<?=site_url("office/project/closeTask/id/".$project->getProjectId())?>',400,300);return false;" title="关闭提前填写任务书功能" class="btn btn-xs btn-info">不立项</a>
          <?php else:?>
            <a href="javascript:void(0);"  onclick="return showWindow('项目预立项','<?=site_url("office/project/doTask/id/".$project->getProjectId())?>',400,300);" title="开启提前填写任务书功能" class="btn btn-xs btn-info">预立项</a>
          <?php endif;?> 
          <?php if($project->getBudgetOpen()):?>
            <a href="javascript:void(0);"  onclick="return showWindow('取消预算填写','<?=site_url("office/project/closeBudget/id/".$project->getProjectId())?>',400,300);return false;" title="关闭项目负责人填写项目预算书功能" class="btn btn-xs btn-info">不预算</a>
          <?php else:?>
            <a href="javascript:void(0);"  onclick="return showWindow('开启项目预算填写功能','<?=site_url("office/project/doBudget/id/".$project->getProjectId())?>',550,320);" title="让项目负责人填写项目预算书" class="btn btn-xs btn-info">填预算</a>
          <?php endif;?>
          <?php if(!$project->isLock()):?>
            <a href="javascript:void(0);"  onclick="return showWindow('修改经费','<?=site_url("office/project/doCommend/id/".$project->getProjectId())?>',550,320);" title="修改错误的经费" class="btn btn-xs btn-info">改经费</a>
          <?php else:?> 
            <s class="btn btn-default btn-xs" role="button"><i class="glyphicon glyphicon-cog"></i>改经费</s>
          <?php endif;?> -->
          <a href="<?=site_url("office/project/doBackWait/id/".$project->getProjectId())?>" onclick="return confirm('确定将此项目返回至待建议吗？');" class="submit btn btn-xs btn-info" >返回待建议</a>
          <a href="javascript:void(0);"  onclick="return showWindow('退回项目','<?=site_url("office/project/doBack/id/".$project->getProjectId())?>',400,300);" title="将项目退回给项目负责人" class="btn btn-xs btn-danger">退回</a>
        </td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="17">
        <?php include('more_part.php') ?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="17" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>