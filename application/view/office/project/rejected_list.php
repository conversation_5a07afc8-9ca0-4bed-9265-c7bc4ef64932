<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a>
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button">导出数据</a>
   <!-- <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a> -->
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php') ?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
        <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
        <th>详</th>
        <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
        <th><?=getColumnStr('项目负责人','user_name')?></th>
        <th width="10%"><?=getColumnStr('研究对象','type_id')?></th>
        <th width="12%"><?=getColumnStr('申报单位','corporation_id')?></th>
        <th width="12%"><?=getColumnStr('归口部门','department_id')?></th>
        <th><?=getColumnStr('申请经费','declare_money')?></th>
        <th><?=getColumnStr('总投入','total_money')?></th>
        <!-- <th><?=getColumnStr('起始年月','start_at')?></th> -->
        <!-- <th><?=getColumnStr('结束年月','end_at')?></th> -->
        <th>未结题项目</th>      
        <th width="150">操作</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr>
        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p></td>
        <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
        <td align="center"><?=$project->getTypeSubject()?></td>
        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
        <td><?=$project->getDepartmentName()?></td>
        <td align="center"><?=$project->getDeclareMoney()?>万元</td>
        <td align="center"><?=$project->getTotalMoney()?>万元</td>
        <!-- <td align="center"><?=$project->getStartAt()?></td> -->
        <!-- <td align="center"><?=$project->getEndAt()?></td> -->
        <td align="center"><?=$project->hasUnCompleteProjectString()?></td>        
        <td align="center">
          <a href="javascript:void(0);" onclick="return showWindow('项目建议支持','<?=site_url("office/project/operator/commend/1/id/".$project->getProjectId())?>',500,360);return false;" class="btn btn-xs btn-info submit" role="button">建议支持</a>
          <a href="<?=site_url("office/project/doBackWait/id/".$project->getProjectId())?>" onclick="return confirm('确定将此项目返回至待建议吗？');" class="submit btn btn-xs btn-danger" >返回待建议</a>
        </td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="17">
        <?php include('more_part.php') ?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="17" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>