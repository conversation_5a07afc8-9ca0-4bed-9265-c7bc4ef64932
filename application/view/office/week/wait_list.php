<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                待受理的周报
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <?=Button::setName('受理选中项')->setEvent('validateForm.action=\''.site_url("office/week/doAccept").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
                <?=Button::setName('退回选中项')->setEvent('validateForm.action=\''.site_url("office/week/doRejected").'\';$(\'#validateForm\').submit()')->setIcon('undo')->setClass('btn-alt-danger')->button()?>
                <?=btn('link','导出数据',site_url('office/week/export'),'export')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include_once('search_part.php') ?>
                </div>
              <div class="box">
               <div class="no-padding no-margin panel panel-default table-responsive" >
                   <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/week/doAccept")?>">
                <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                    <thead>
                    <tr>
                      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                      <th width="30">详</th>
                      <th width="25%"><?=getColumnStr('名称','subject')?></th>
                        <th><?=getColumnStr('建设单位','company_id')?></th>
                        <th><?=getColumnStr('主管部门','department_id')?></th>
                        <th><?=getColumnStr('填报人','user_id')?></th>
                      <th class="text-center" width="230">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($week = $pager->getObject()):?>
                      <tr>
                        <td><input name="select_id[]" type="checkbox" value="<?=$week->getWeekId()?>" /></td>
                          <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                        <td><?=link_to("apply/week/show/id/".$week->getWeekId(),$week->getSubject())?></td>
                        <td><?=link_to("unit/profile/show/userid/".$week->getCompanyId(),$week->getCompanyName())?></td>
                        <td><?=$week->getDepartmentName()?></td>
                        <td><?=$week->getUserName()?></td>
                        <td align="center">
                            <?=Button::setName('审核')->setUrl(site_url("apply/week/show/id/".$week->getWeekId()))->setIcon('find')->link()?>
                            <?=Button::setName('受理')->setUrl(site_url("office/week/doAcceptOnlyOne/id/".$week->getWeekId()))->setClass('btn-alt-success')->setIcon('check')->setWidth('800px')->setHeight('600px')->window()?>
                            <?=Button::setName('退回')->setUrl(site_url("office/week/doRejectedOnlyOne/id/".$week->getWeekId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                        </td>
                      </tr>
                        <tr class="fold_body">
                        <td colspan="16"><?php include('more_part.php') ?>
                        </td>
                      </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                    <tr>
                      <td colspan="16" align="right">&nbsp;<span class="pager_bar">
                        <?=$pager->fromto().$pager->navbar(10)?>
                      </span></td>
                    </tr>
                    </tfoot>
                </table>
               </form>
              </div>
            </div>
            </div>
        </div>
    </div>
</div>
