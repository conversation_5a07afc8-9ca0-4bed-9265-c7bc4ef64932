<form action="" method="post" name="search" id="search">
    <table width="100%" align="center">
        <tbody id="show_search">
        <tr>
            <td>关键词：
                <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                <label><input name="field" type="radio" value="subject" checked="checked" />项目名称&nbsp;</label>
                <label><input type="radio" name="field" value="radicate_id" />项目编号&nbsp;</label>
                <label><input type="radio" name="field" value="company_name" />申报单位&nbsp;</label>
                <label><input type="radio" name="field" value="user_name" />项目负责人&nbsp;</label>
                <?=btn('button','搜索','submit','find')?>
            </td>
        </tr>
        <tr>
            <td>
                <select name="subject_code" id="subject_code" class="form-control w-auto custom-control-inline">
                    <option value="">=专科=</option>
                    <?=getSelectFromArray(getZdzkList('code'),input::getInput('mix.subject_code'),false)?>
                </select>
                <select name="index_code" id="index_code" class="form-control w-auto custom-control-inline">
                    <option value="">=指标=</option>
                    <?php
                    $subjectCode = input::getMix('subject_code');
                    ?>
                    <?=getSelectFromArray(getRuleItems($subjectCode),input::getMix("index_code"),false)?>
                </select>
                <select name="stage_year" id="stage_year" class="form-control w-auto custom-control-inline">
                    <option value="">=评估年度=</option>
                    <?=getYearList(input::getInput("mix.stage_year"),2023)?>
                </select>
                <select name="type_current_group" id="type_current_group" class="form-control w-auto custom-control-inline">
                    <option value="">=评估批次=</option>
                    <?=getSelectFromArray(array('1'=>'第一批','2'=>'第二批'),input::getInput("mix.type_current_group"),false)?>
                </select>
                <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                    <option value="">=立项年度=</option>
                    <?=getYearList(input::getInput("mix.declare_year"))?>
                </select>
                <select name="district" id="district" class="form-control w-auto custom-control-inline">
                    <option value="">=所属片区=</option>
                    <?=getSelectFromArray(getDistrictList(),input::getMix("district"))?>
                </select>
                <select name="department_id" id="department_id" class="form-control w-auto custom-control-inline">
                    <option value="">=主管部门=</option>
                    <?=getDepartmentList(input::getMix('department_id'))?>
                </select>
                <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                    <option value="">=项目状态=</option>
                    <?=projectStateSelect(input::getMix("statement"))?>
                </select>
            </td>
        </tr>
        <tr>
            <td>
                <label style="float:left;margin-right: 15px;margin-top: 5px;">中期指南:</label>
                <div class="cxselect" data-selects="guide_id1,guide_id2,guide_id3"
                     data-url="<?= site_path('json/guide_stages.json') ?>" data-json-value="v" style="float:left">
                    <select class="form-control w-auto custom-control-inline guide_id1" data-value="<?=input::getMix('guide_id1')?>"
                            name="guide_id1"></select>
                    <select class="form-control w-auto custom-control-inline guide_id guide_id2" name="guide_id2"
                            data-first-title="请选择" data-value="<?=input::getMix('guide_id2')?>"></select>
                    <select class="form-control w-auto custom-control-inline guide_id guide_id3" name="guide_id3"
                            data-first-title="请选择" data-value="<?=input::getMix('guide_id3')?>"></select>
                    <input type="hidden" name="guide_id" id="guide_id" value="<?=input::getInput("mix.guide_id")?>">
                </div>
            </td>
        </tr>
        </tbody>
    </table>
</form>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    $(function (){
        $("#subject_code").change(function (){
            var subject_code = $(this).val();
            var url = '<?=site_url('ajax/getRuleItemsOptions')?>';
            $.getJSON(url,{subject_code:subject_code},function (data){
                var html = '<option value="">=指标=</option>';
                html+=data.html;
                $("#index_code").html(html);
            });
        });
        $(".guide_id").change(function () {
            $("#guide_id").val($(this).val());
        });
    });
</script>