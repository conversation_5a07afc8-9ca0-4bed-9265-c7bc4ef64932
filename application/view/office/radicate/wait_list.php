<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                待立项的项目
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <?=Button::setName('立项')->setEvent('validateForm.action=\''.site_url("office/radicate/doAccept").'\';$(\'#validateForm\').submit()')->setIcon('check')->setClass('btn-alt-success')->button()?>
                <?=Button::setName('不立项')->setEvent('validateForm.action=\''.site_url("office/radicate/doRejected").'\';$(\'#validateForm\').submit()')->setIcon('undo')->setClass('btn-alt-danger')->button()?>
                <?=btn('link','导出数据',site_url('export/export/index'),'export')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                  <?php include_once('search_part.php') ?>
                </div>
                <div class="box">
                 <div class="no-padding no-margin panel panel-default" >
                  <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/radicate/doRejected")?>">
                    <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                        <thead>
                      <tr>
                        <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                        <th>详</th>
                        <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                        <th><?=getColumnStr('负责人','user_name')?></th>
                        <th width="12%"><?=getColumnStr('申报单位','corporation_id')?></th>
                        <th width="12%"><?=getColumnStr('主管部门','department_id')?></th>
                        <th><?=getColumnStr('申报年度','declare_year')?></th>
                        <th><?=getColumnStr('总分','score')?></th>
                        <th width="200" class="text-center">操作</th>
                      </tr>
                        </thead>
                        <tbody>
                      <?php while($project = $pager->getObject()):?>
                        <tr>
                            <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                          <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
                          <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?>
                              <p class="snote">平台级别：<?=$project->getLevel()?><br>申报年度：<?=$project->getDeclareYear()."(".$project->getTypeCurrentGroup().")"?><br>平台类别：<?=$project->getCatSubject()?></p>
                          </td>
                          <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                          <td><?=$project->getCorporationName()?></td>
                          <td><?=$project->getDepartmentName()?></td>
                          <td><?=$project->getDeclareYear()?></td>
                          <td><?=$project->getScore()?></td>
                          <td align="center">
                              <?=Button::setUrl(site_url("office/radicate/doAcceptOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-success')->setTitle('项目立项操作')->setIcon('check')->setWidth('800px')->setHeight('600px')->window('立项')?>
                              <?=Button::setUrl(site_url("office/radicate/doRejectedOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-danger')->setTitle('项目不予立项')->setIcon('close')->setWidth('800px')->setHeight('600px')->window('不立项')?>
                              <!--
                              <?=Button::setUrl(site_url("office/radicate/doStop/id/".$project->getProjectId()))->setClass('btn-alt-warning')->setTitle('项目终止')->setIcon('fa fa-ban')->setWidth('800px')->setHeight('600px')->window('终止')?>

                              <?=Button::setName('退回')->setUrl(site_url("office/accept/doRejectedOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                              -->
                          </td>
                        </tr>
                        <tr id="show_<?=$project->getId()?>" style="display:none;">
                          <td colspan="18">
                            <?php include('more_part.php') ?>
                          </td>
                        </tr>
                      <?php endwhile;?>
                        </tbody>
                        <tfoot>
                      <tr>
                        <td colspan="18" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                      </tr>
                        </tfoot>
                    </table>
                  </form>
                </div>
              </div>
            </div>
        </div>
    </div>
</div>