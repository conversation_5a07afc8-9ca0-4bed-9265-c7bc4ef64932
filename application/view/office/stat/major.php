<script language="javascript" type="text/javascript" src="<?=site_path("js/datepicker/WdatePicker.js")?>"></script>
<div class="main">
  <div class="btn-group btn-group-sm" role="group">

  <p style="clear:both;"></p>
  </div>

  <div class="box">

    <h2><?=$declare_year?>年项目按专业分类</h2>
  <table class="table table-bordered">
    <tr>
      <th class="text-center">序号</th>
      <th class="text-center">一级学科</th>
      <th class="text-center">二级学科</th>
      <th class="text-center">三级学科</th>
      <th class="text-center">数量</th>
    </tr>
    <?php
    if($type_id) $addwhere .= " and type_id = {$type_id}";
      $i=1;
      $open_subject_id1 = [320];              //需要展开的一级学科
      $open_subject_id2 = [320.27,320.24];   //需要展开的二级学科
//    $open_subject_id1 = [320];   //需要展开的一级学科
//    $open_subject_id2 = [];   //需要展开的二级学科
      while($subject_id1 = $subject_id1s->getObject()){
        if(in_array($subject_id1->getSubjectId1(),$open_subject_id1)){
          $subject_id2s = sf::getModel('Projects')->selectAll("declare_year = '{$declare_year}' and statement = 10 and subject_id1 = ".$subject_id1->getSubjectId1().$addwhere,"ORDER BY subject_id2 asc",0,'SELECT DISTINCT subject_id2,subject_name2 FROM `projects`');
          if($subject_id2s->getTotal()>0){
            //循环二级学科
            while($subject_id2 = $subject_id2s->getObject()){
              if(in_array($subject_id2->getSubjectId2(),$open_subject_id2)) {
                $subject_id3s = sf::getModel('Projects')->selectAll("declare_year = '{$declare_year}' and statement = 10 and subject_id2 = " . $subject_id2->getSubjectId2() . $addwhere, "ORDER BY subject_id asc", 0, 'SELECT DISTINCT subject_id,subject_name FROM `projects`');
                if($subject_id3s->getTotal()>0){
                  //循环三级学科
                  while($subject_id3 = $subject_id3s->getObject()){
                    //查询数量
                    $projects = sf::getModel('Projects')->selectAll("declare_year = '{$declare_year}' and statement = 10 and subject_id = " . $subject_id3->getSubjectId() . $addwhere, "ORDER BY subject_id asc");
                    $count = $projects->getTotal();
                    echo '<tr>';
                    echo "<td>{$i}</td>";
                    echo "<td>".$subject_id1->getSubjectName1().$subject_id1->getSubjectId1()."</td>";
                    echo "<td>".$subject_id2->getSubjectName2().$subject_id2->getSubjectId2()."</td>";
                    echo "<td>".$subject_id3->getSubjectName().$subject_id3->getSubjectId()."</td>";
                    echo "<td>".$count."</td>";
                    echo "</tr>";
                    $i++;
                  }
                }
              }else{
                //查询数量
                $projects = sf::getModel('Projects')->selectAll("declare_year = '{$declare_year}' and statement = 10 and subject_id2 = " . $subject_id2->getSubjectId2() . $addwhere, "ORDER BY subject_id asc");
                $count = $projects->getTotal();
                echo '<tr>';
                echo "<td>{$i}</td>";
                echo "<td>".$subject_id1->getSubjectName1().$subject_id1->getSubjectId1()."</td>";
                echo "<td>".$subject_id2->getSubjectName2().$subject_id2->getSubjectId2()."</td>";
                echo "<td></td>";
                echo "<td>".$count."</td>";
                echo "</tr>";
                $i++;
              }
            }
          }
        }else{
          //查询数量
          $projects = sf::getModel('Projects')->selectAll("declare_year = '{$declare_year}' and statement = 10 and subject_id1 = " . $subject_id1->getSubjectId1() . $addwhere, "ORDER BY subject_id asc");
          $count = $projects->getTotal();
          echo '<tr>';
          echo "<td>{$i}</td>";
          echo "<td>".$subject_id1->getSubjectName1().$subject_id1->getSubjectId1()."</td>";
          echo "<td></td>";
          echo "<td></td>";
          echo "<td>".$count."</td>";
          echo "</tr>";
          $i++;
        }
      }
    //查询数量
    $projects = sf::getModel('Projects')->selectAll("declare_year = '{$declare_year}' and statement = 10".$addwhere, "ORDER BY subject_id asc");
    $count = $projects->getTotal();
      echo '<tr>';
      echo "<td>合计</td>";
      echo "<td></td>";
      echo "<td></td>";
      echo "<td></td>";
      echo "<td>".$count."</td>";
      echo "</tr>";
      $i++;
    ?>

  </table>

  </div>
</div>
