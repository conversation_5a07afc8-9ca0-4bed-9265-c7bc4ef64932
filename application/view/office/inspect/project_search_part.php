<form action="" method="post" name="search" id="search">
    <table width="100%" align="center">
        <tbody id="show_search">
        <tr>
            <td>关键词：
                <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                <label><input name="field" type="radio" value="subject" checked="checked" />项目名称&nbsp;</label>
                <label><input type="radio" name="field" value="radicate_id" />项目编号&nbsp;</label>
                <label><input type="radio" name="field" value="corporation_name" />申报单位&nbsp;</label>
                <label><input type="radio" name="field" value="user_name" />项目负责人&nbsp;</label>
                <label><input type="radio" name="field" value="declare_year" />申报年度&nbsp;</label>
                <?=btn('button','搜索','submit','find')?>
            </td>
        </tr>
        <tr>
            <td>
                <select name="level" id="level" class="form-control w-auto custom-control-inline">
                    <option value="">=项目级别=</option>
                    <?=getSelectFromArray(['国家级','省级','市州级','县级'],input::getInput("mix.level"))?>
                </select>
                <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                    <option value="">=申报年度=</option>
                    <?=getYearList(input::getInput("mix.declare_year"),2019)?>
                </select>
                <select name="district" id="district" class="form-control w-auto custom-control-inline">
                    <option value="">=所属片区=</option>
                    <?=getSelectFromArray(getDistrictList(),input::getInput("mix.district"))?>
                </select>
                <select name="diagnosis_code" id="diagnosis_code" class="form-control w-auto custom-control-inline">
                    <option value="">=专科类别=</option>
                    <?=getSelectFromArray(getDiagnosisList(),input::getMix('diagnosis_code'),false)?>
                </select>
                <select name="department_id" id="department_id" class="form-control w-auto custom-control-inline">
                    <option value="">=主管部门=</option>
                    <?=getDepartmentList(input::getMix('department_id'))?>
                </select>
                <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                    <option value="">=项目状态=</option>
                    <?=projectStateSelect(input::getInput("mix.statement"))?>
                </select>
                <select name="is_short" id="is_short"  class="form-control w-auto custom-control-inline">
                    <option value="">=补短板项目=</option>
                    <?=getSelectFromArray([1=>'否',2=>'是'],input::getInput("mix.is_short"),false)?>
                </select>
                <select name="is_minority" id="is_minority"  class="form-control w-auto custom-control-inline">
                    <option value="">=民族地区项目=</option>
                    <?=getSelectFromArray([1=>'否',2=>'是'],input::getInput("mix.is_minority"),false)?>
                </select>
                <select name="is_test" id="is_test"  class="form-control w-auto custom-control-inline">
                    <option value="">=测试项目=</option>
                    <?=getSelectFromArray([1=>'不含',2=>'包含'],input::getInput("mix.is_test"),false)?>
                </select>
                <select name="inspect_open" id="inspect_open"  class="form-control w-auto custom-control-inline">
                    <option value="">=开启状态=</option>
                    <?=getSelectFromArray([1=>'已开启',2=>'未开启'],input::getInput("mix.inspect_open"),false)?>
                </select>
            </td>
        </tr>
        <tr>
            <td>
                <label style="float:left;margin-right: 15px;margin-top: 5px;">申报指南:</label>
                <div class="cxselect" data-selects="guide_id1,guide_id2,guide_id3"
                     data-url="<?= site_path('json/guides.json') ?>" data-json-value="v" style="float:left">
                    <select class="form-control w-auto custom-control-inline guide_id1" data-value="<?=input::getMix('guide_id1')?>"
                            name="guide_id1"></select>
                    <select class="form-control w-auto custom-control-inline guide_id guide_id2" name="guide_id2"
                            data-first-title="请选择" data-value="<?=input::getMix('guide_id2')?>"></select>
                    <select class="form-control w-auto custom-control-inline guide_id guide_id3" name="guide_id3"
                            data-first-title="请选择" data-value="<?=input::getMix('guide_id3')?>"></select>
                    <input type="hidden" name="guide_id" id="guide_id" value="<?=input::getInput("mix.guide_id")?>">
                </div>
            </td>
        </tr>
        </tbody>
    </table>
</form>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    $(function (){
        $(".guide_id").change(function () {
            $("#guide_id").val($(this).val());
        })
    });
</script>