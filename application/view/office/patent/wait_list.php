<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        待审核的专利列表
                    </h3>
                </div>
                <div class="block-content">
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="javascript:selectAll();" class="btn btn-info">全部选中</a>
                        <!-- <a href="javascript:void(0);" onclick="validateForm.action='<?=site_url("office/patent/doSubmit")?>';$('#validateForm').submit();" class="btn btn-info doit" role="button">审核选中项</a> -->
                        <a href="javascript:void(0);" onclick="validateForm.action='<?=site_url("office/patent/doRejected")?>';$('#validateForm').submit()" class="btn btn-danger undoit" role="button">退回选中项</a>
                        <!-- <a href="<?=site_url("export/export/index/table/patents")?>" class="btn btn-info export" role="button">导出数据</a> -->
                        <p style="clear:both;"></p>
                    </div>
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
                        <div class="no-padding no-margin panel panel-default" >
                            <table align="center" cellpadding="3" cellspacing="1" class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th class="text-center" width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th class="text-center" width="30">详</th>
                                    <th class="text-center" width="25%"><?=getColumnStr('专利名称','subject')?></th>
                                    <th class="text-center"><?=getColumnStr('专利类型','type')?></th>
                                    <th class="text-center"><?=getColumnStr('专利号','sn')?></th>
                                    <th class="text-center" width="5%"><?=getColumnStr('申报人','user_name')?></th>
                                    <th class="text-center"><?=getColumnStr('授权日期','date')?></th>
                                    <th class="text-center" width="15%"><?=getColumnStr('专利权人','zlqr')?></th>
                                    <th class="text-center"><?=getColumnStr('专利终止日期','end_at')?></th>
                                    <th class="text-center">操 作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($patent = $pager->getObject()):?>
                                    <tr>
                                        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$patent->getPatentId()?>" /></td>
                                        <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                        <td width="30%"><a href="<?=site_url('user/patent/show/id/'.$patent->getPatentId())?>"><?=$patent->getSubject()?></a></td>
                                        <td align="center" class=""><?=$patent->getTyp()?></td>
                                        <td align="center" class=""><?=$patent->getSn()?></td>
                                        <td align="center" class=""><?=$patent->getUserName()?></td>
                                        <td align="center" class=""><?=$patent->getDate()?></td>
                                        <td align="center" class=""><?=$patent->getZlqr()?></td>
                                        <td align="center" class=""><?=$patent->getEndAt()?></td>
                                        <td align="center">
                                            <a href="javascript:void(0);"  onclick="return showWindow('审核专利成果','<?=site_url("office/patent/doSubmitOne/id/".$patent->getPatentId())?>',400,300);" title="审核专利成果" class='btn btn-xs btn-info'><i class="glyphicon glyphicon-ok"></i> 审 核</a>
                                            <a href="javascript:void(0);"  onclick="return showWindow('退回','<?=site_url("office/patent/doRejectedOnlyOne/id/".$patent->getPatentId())?>',400,300);" title="将退回给申报人" class='btn btn-xs btn-danger'><i class="glyphicon glyphicon-remove-circle"></i> 退 回</a>
                                        </td>
                                    </tr>
                                    <tr class="fold_body">
                                        <td colspan="16"><?php include('more_part.php') ?></td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="10" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>