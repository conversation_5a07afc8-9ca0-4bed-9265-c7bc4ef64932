<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a>
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button">导出数据</a>
  <a href="<?=site_url("export/statistics/index")?>" class="stat">数据统计</a>
   <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php') ?>
</div>
  <div class="box">

    <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th><?=getColumnStr('申报编号','accept_id')?></th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th width="80"><?=getColumnStr('项目类型','type_id')?></th>
      <th><?=getColumnStr('所属归口部门','department_id')?></th>
      <th><?=getColumnStr('申报年度','declare_year')?></th>
      <th width="60"><?=getColumnStr('项目负责人','user_name')?></th>
      <th><?=getColumnStr('承担单位','corporation_id')?></th>
      <th>未结项目</th>
      <th><?=getColumnStr('项目状态','statement')?></th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
      <td><?=$project->getAcceptId()?></td>
      <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p></td>
      <td align="center"><?=$project->getTypeSubject()?></td>
      <td><?=$project->getDepartmentName()?></td>
      <td align="center"><?=$project->getDeclareYear()."(".$project->getTypeCurrentGroup().")"?></td>
      <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
      <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <td align="center"><?=$project->hasUnCompleteProjectString()?></td>
      <td><?=$project->getState()?></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="11">
         <?php include('more_part.php') ?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="11" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(6).$pager->pagejump()?></span></td>
        </tr>
      </table>

  </div>
</div>
