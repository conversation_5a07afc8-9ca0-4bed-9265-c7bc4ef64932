<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        已审核的工法列表
                    </h3>
                    <div class="block-options">
                        <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                        <?=Button::setName('退回选中项')->setEvent('validateForm.action=\''.site_url("office/method/doRejected").'\';$(\'#validateForm\').submit()')->setIcon('undo')->setClass('btn-alt-danger')->button()?>
                        <?=btn('link','导出数据',site_url('export/export/index/table/methods'),'export')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
                        <div class="no-padding no-margin panel panel-default" >
                            <table cellpadding="3" cellspacing="1" class="table table-hover">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="30">详</th>
                                    <th width="28%"><?=getColumnStr('工法名称','subject')?></th>
                                    <th><?=getColumnStr('工法类型','type')?></th>
                                    <th width="10%"><?=getColumnStr('工法编号','code')?></th>
                                    <th width="15%"><?=getColumnStr('公布日期','date')?></th>
                                    <th width="100">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($method = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$method->getMethodId()?>" /></td>
                                        <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$method->getId()?>').toggle();">+</label></td>
                                        <td><a href="<?=site_url('user/method/show/id/'.$method->getMethodId())?>"><?=$method->getSubject()?></a></td>
                                        <td><?=$method->getTyp()?></td>
                                        <td><?=$method->getCode()?></td>
                                        <td><?=$method->getDate()?></td>
                                        <td>
                                            <?=Button::setName('退回')->setUrl(site_url("office/method/doRejectedOnlyOne/id/".$method->getMethodId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                                        </td>
                                    </tr>
                                    <tr id="show_<?=$method->getId()?>" style="display:none;">
                                        <td colspan="16"><?php include('more_part.php') ?></td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="10" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>