<form action="" method="post" name="search" id="search">
    <table width="100%" align="center">
        <tbody id="show_search">
        <tr>
            <td>关键词：
                <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                <label><input name="field" type="radio" value="subject" checked="checked" />项目名称&nbsp;</label>
                <label><input type="radio" name="field" value="radicate_id" />项目编号&nbsp;</label>
                <label><input type="radio" name="field" value="corporation_name" />申报单位&nbsp;</label>
                <label><input type="radio" name="field" value="user_name" />项目负责人&nbsp;</label>
                <label><input type="radio" name="field" value="declare_year" />申报年度&nbsp;</label>
                <?=btn('button','搜索','submit','find')?>
            </td>
        </tr>
        <tr>
            <td>
                <?php
                if(input::session('auth.chart_company_name')==='full'):
                ?>
                <select name="chart_company_name" id="chart_company_name" class="form-control w-auto custom-control-inline">
                    <option value="">=医院名称显示方式=</option>
                    <?=getSelectFromArray(['tag'=>'代号','full'=>'全称'],input::session('chart_company_name'),false)?>
                </select>
                <?php endif;?>
                <select name="level" id="level" class="form-control w-auto custom-control-inline">
                    <option value="">=中心级别=</option>
                    <?=getSelectFromArray(['国家级','省级','市州级','县级'],input::getInput("mix.level"))?>
                </select>
                <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                    <option value="">=申报年度=</option>
                    <?=getYearList(input::getInput("mix.declare_year"))?>
                </select>
                <select name="type_current_group" id="type_current_group" class="form-control w-auto custom-control-inline">
                    <option value="">=项目批次=</option>
                    <?=getSelectFromArray(array('1'=>'第一批','2'=>'第二批'),input::getInput("mix.type_current_group"),false)?>
                </select>
                <?php
                if(input::session('auth.chart_company_name')==='full'):
                ?>
                <select name="district" id="district" class="form-control w-auto custom-control-inline">
                    <option value="">=所属片区=</option>
                    <?=getSelectFromArray(getDistrictList(),input::getInput("mix.district"))?>
                </select>
                <select name="department_id" id="department_id" class="form-control w-auto custom-control-inline">
                    <option value="">=主管部门=</option>
                    <?=getDepartmentList(input::getMix('department_id'))?>
                </select>
                <?php endif;?>
                <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                    <option value="">=项目状态=</option>
                    <?=projectStateSelect(input::getInput("mix.statement"))?>
                </select>
            </td>
        </tr>
        </tbody>
    </table>
</form>