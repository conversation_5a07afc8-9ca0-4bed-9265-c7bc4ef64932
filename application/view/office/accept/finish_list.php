<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                已推荐的实验室
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <?=btn('link','导出数据',site_url('export/export/index'),'export')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="search">
                <?php include_once('search_part.php') ?>
              </div>
              <div class="box">
               <div class="no-padding no-margin panel panel-default" >
                   <form id="validateForm" name="validateForm" method="post" action="">
                <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                    <thead>
                    <tr>
                      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                      <th width="30">详</th>
                      <th width="100"><?=getColumnStr('申报编号','accept_id')?></th>
                      <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                      <th><?=getColumnStr('负责人','user_id')?></th>
                      <th><?=getColumnStr('申报单位','corporation_id')?></th>
                      <th><?=getColumnStr('主管部门','department_id')?></th>
                      <th><?=getColumnStr('申报年度','declare_year')?></th>
                      <th class="text-center">状态</th>
                      <th class="text-center" width="200px">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($project = $pager->getObject()):?>
                      <tr>
                        <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                         <td ><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                          <td><?=$project->getAcceptId()?></td>
                        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?></td>
                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                        <td><?=$project->getDepartmentName()?></td>
                        <td><?=$project->getDeclareYear()?></td>
                        <td class="text-center"><?=$project->getState()?></td>
                        <td class="text-center">
                            <?=Button::setUrl(site_url("office/accept/review/id/".$project->getProjectId()))->setIcon('fa fa-paperclip')->window('评审意见')?>
                            <?=Button::setName('退回')->setUrl(site_url("office/accept/doRejectedOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                        </td>
                      </tr>
                        <tr class="fold_body">
                        <td colspan="16">
                          <?php include('more_part.php') ?>
                        </td>
                      </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                    <tr>
                      <td colspan="16" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                    </tr>
                    </tfoot>
                </table>
                   </form>
               </div>
            </div>
            </div>
        </div>
    </div>
</div>