<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:selectAll();" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-ok-circle"></i> 全部选中</a>
  <a href="javascript:void(0);" onclick="validateForm.action='<?=site_url("office/over/doSubmit")?>';$('#validateForm').submit();" class="btn btn-info doit" role="button">通过选中项</a>
   <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include('search_part.php');?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/over/doSubmit")?>">
    <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th width="70"><?=getColumnStr('立项编号','radicate_id')?></th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th width="100"><?=getColumnStr('项目类型','type_id')?></th>
      <th width="60"><?=getColumnStr('项目负责人','user_id')?></th>
      <th><?=getColumnStr('申报单位','corporation_id')?></th>
      <th width="100"><?=getColumnStr('所属归口部门','department_id')?></th>
      <th width="110">可用操作</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
      <td align="center"><?=$project->getRadicateId()?></td>
      <td><?=$project->getMark()?><?=link_to("declare/complete/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
      <td><?=$project->getTypeSubject()?></td>
      <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
      <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <td><?=$project->getDepartmentName()?></td>
      <td align="center"><a href="<?=site_url("office/over/doSubmit/id/".$project->getProjectId())?>" class="btn btn-info btn-sm" role="button">结题</a> <a href="javascript:void(0);" onclick="return showWindow('结题报告需要复议','<?=site_url("office/over/doRejected/id/".$project->getProjectId())?>',400,280);" class="btn btn-danger btn-sm" role="button">复议</a></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="9">
         <?php include('more_part.php')?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="9" align="right"><span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
