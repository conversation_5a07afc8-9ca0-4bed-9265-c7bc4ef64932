<div class="">
    <div class="btn-group btn-group-sm" role="group">
        <a href="<?=site_url('Lab/office/export/statement/20/year/'.input::getMix('year'))?>" class="btn btn-info"><i class="glyphicon glyphicon-export"></i> 导出待修正的申报书</a>
        <p style="clear:both;"></p>
    </div>
    <div class="search">
        <?php include_once('search_part.php') ?>
    </div>
    <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
        <div class="no-padding no-margin panel panel-default" >
            <div class="panel-heading">
                <i class="ace-icon fa fa-list"></i> 待修正的申报书列表
            </div>
            <table align="center" cellpadding="3" cellspacing="1" class="table">
                <thead>
                <tr>
                    <th class="hidden-480 text-center"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                    <th class="text-center">详</th>
                    <th class="text-left">实验室名称</th>
                    <th class="text-left">申报年度</th>
                    <th class="text-left">学科分类</th>
                    <th class="text-left">依托单位</th>
                    <th class="text-left">主管部门</th>
                    <th class="text-center">申报人</th>
                    <th class="text-center">手机</th>
                    <th class="text-center">状态</th>
                    <th class="text-center">操作</th>
                </tr>
                </thead>
                <tbody>
                <?php $i=0;while($pager = $pagers->getObject()):$i++;?>
                    <tr>
                        <td align="center" class="hidden-480"><input name="select_id[]" type="checkbox" value="<?=$pager->getId()?>" /></td>
                        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$pager->getId()?>').toggle()">+</label></td>
                        <td align="left" class="hidden-480"><a target="_blank" href="<?=site_url('Lab/Project/show/id/'.$pager->getProjectId())?>"><?=$pager->getSubject()?></a></td>
                        <td align="left" class="hidden-480"><?=$pager->getDeclareYear()?></td>
                        <td align="left" class="hidden-480"><?=$pager->getGbSubject()?></td>
                        <td align="left" class="hidden-480"><?=$pager->getCorporationName()?></td>
                        <td align="left" class="hidden-480"><?=$pager->getDepartmentName()?></td>
                        <td align="center" class="hidden-480"><?=$pager->getLinkman()?></td>
                        <td align="center" class="hidden-480"><?=$pager->getMobile()?></td>
                        <td align="center" class="hidden-480"><?=$pager->getState()?></td>
                        <td align="center" class="hidden-480">
                            <a href="javascript:void(0);" onclick="return showWindow('评审意见','<?=site_url("Lab/Office/review/id/".$pager->getProjectId())?>',400,250);" class="btn btn-primary btn-sm" role="button"><i class="glyphicon glyphicon-paperclip"></i> 评审意见</a>
                        </td>
                    </tr>
                    <tr id="show_<?=$pager->getId()?>" style="display:none;">
                        <td colspan="10">
                            <?php include('more_part.php')?>
                        </td>
                    </tr>
                <?php endwhile;?>
                </tbody>
                <tfooter>
                    <tr>
                        <td colspan="8" align="right">&nbsp;<?=$pagers->fromTo().$pagers->navbar(10)?></td>
                    </tr>
                </tfooter>
            </table>
        </div>
    </form>
</div>