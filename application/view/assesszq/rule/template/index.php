<style>
    .rule_list{
        padding-left: 15px;
    }
    ul{list-style:none;}
    #about p{
        font-size: 20px;
        text-indent: 2em;
    }
    #page-nav {
         -moz-border-radius: 6px 6px 6px 6px;
         border: 1px solid #DEDFE1;
         float: left;
         margin: 0 0 15px 15px;
         padding: 0 6px;
         width: 250px;
         line-height: 23px;
         position: fixed;
         top: 95px;
         left: 250px;
         background: #fff;
    }
    #page-nav strong {
        border-bottom: 1px dashed #DDDDDD;
        display: block;
        line-height: 30px;
        padding: 0 4px;
    }
    #index-ul {
        margin: 0;
        padding-bottom: 10px;
        max-height: 500px;
        overflow: auto;
    }
    #index-ul .selected{
        color: #e04f1a!important;
    }
    #index-ul .selected{
        color: #e04f1a!important;
    }
    #index-ul ul{
        display: none;
    }
    #index-ul li{
        width: 33%;border:1px solid #ccc;float: left;text-align: center;line-height: 30px;
    }
    #index-ul li a{
        width: 100%;
        display: inline-block;
    }
    #index-ul .selected > a{
        color: #e04f1a!important;
    }
    #index-ul .selected > ul{
        display: block !important;
    }
    .bg-warning a,.bg-success a{
        color: #fff;
    }
    @media(max-width:1200px) {
        #page-nav{
            left: 0;
        }
    }
    table.table.tb_data{
	    word-wrap: break-word;
    	word-break: break-all;
    }
    .ex_tools {
        display: none;
        text-align:center;
        padding:5px 5px 5px 5px;
        border:solid 1px #62A8D1;
        background-color:#fff;
        bottom: 0;
        right:0;
        margin: 0 auto;
        position: fixed;
        /*opacity: .60;
     filter: alpha(opacity=60);*/
        width: 160px;
        z-index: 999;
        _bottom:auto;
        _width: 100px;
        _right: 0;
        _position: absolute;
        _top:expression(eval(document.documentElement.scrollTop+document.documentElement.clientHeight-this.offsetHeight-(parseInt(this.currentStyle.marginTop, 10)||0)-(parseInt(this.currentStyle.marginBottom, 10)||0)));
    }
</style>
<div class="row no-gutters flex-md-10-auto">
    <div class="col-md-4 col-lg-5 col-xl-3 left-area" style="display: none">
        <div id="page-nav" style="display: none">
            <div class="page-nav-title">
                <strong>
                    <span style="float: left">指标目录</span>
                </strong>
                <div class="form-check form-check-inline mt-1" style="float: right">
                    <input type="checkbox" class="form-check-input" id="only-expert" name="only-expert"> <label class="form-check-label" for="only-expert">只看需评指标</label>
                </div>
                <div style="clear: both"></div>
            </div>
            <ul id="index-ul"></ul>
            <div class="score-area">
                <div class="row">
                    <div class="col-lg-6 text-center">
                        <span id="expert-score"><?=$grade->getExpertScore()?></span><br>
                        专家评分
                    </div>
                    <div class="col-lg-6 text-center">
                        <span id="objective-score"><?=$grade->getStage()->getSystemScore(true)?></span><br>
                        系统评分
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12 bg-body-dark right-area">
        <div class="content">
            <div class="block">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="block block-rounded">
                            <ul class="nav nav-tabs nav-tabs-block">
                                <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#about"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>评审说明</a></li>
                                <?php  if(in_array($grade->getStage()->getCatId(),[235])):?>
                                <li class="nav-item"><a class="nav-link nav-link-plan" data-toggle="tab" href="#plan"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>建设方案</a></li>
                                <?php endif;?>
                                <li class="nav-item"><a class="nav-link nav-link-assess" data-toggle="tab" href="#rule"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>评审表</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="block block-content tab-content">
                    <div class="tab-pane active" id="about" role="tabpanel">
                        <?php
                            if(in_array($grade->getStage()->getCatId(),[174,233])):
                            //省级
                        ?>
                        <p>尊敬的专家：</p>
                        <p>您好！欢迎您参加<?=$grade->getStage()->getCatSubject()?>建设项目中期评估评审工作。评分材料反馈时间为X月X日（周X）24点，敬请您拨冗安排时间，进行项目评议。</p>
                        <p>《<?=$grade->getStage()->getCatSubject()?>建设项目中期评估标准（<?=$grade->getStage()->getSubject()?>）》包括指标<?=$rule->getNumber()?>项，总分合计<?=$rule->getTotal()?>分。其中包括系统打分指标<?=$rule->getNumber('system')?>项（<?=$rule->getScoreSystem()?>分）与专家打分指标<?=$rule->getNumber('expert')?>项（<?=$rule->getScoreExpert()?>分）。系统打分指标为从四川省卫生健康统计数据综合采集与决策支持系统、医院绩效考核系统直接获取指标，无需专家评分；主观指标需专家根据医院申报材料，结合专科声誉，根据评分标准进行评分。</p>
                        <p>感谢您对我们工作的支持与理解！</p>
                        <p style="font-size:18px;text-indent: 0;text-align: center">
                            <?=Button::setUrl('#')->setIcon('edit')->setEvent("$('.nav-link-assess').click();$('#only-expert').click();")->setSize('btn-lg')->link('开始评审')?>
                        </p>
                        <?php
                            elseif(in_array($grade->getStage()->getCatId(),[225,231])):
                            //市县级
                        ?>
                        <p>尊敬的专家：</p>
                        <p>您好！欢迎您参加<?=$grade->getStage()->getCatSubject()?>建设项目中期评估评审工作。评分材料反馈时间为X月X日（周X）24点，敬请您拨冗安排时间，进行项目评议。</p>
                        <p>《<?=$grade->getStage()->getCatSubject()?>建设项目中期评估标准（<?=str_replace('评分表(市县级)','',$rule->getSubject())?>）》包括指标<?=$rule->getNumber()?>，总分合计<?=$rule->getTotal()?>分。其中包括系统打分指标（<?=$rule->getScoreSystem()?>分）与专家打分指标（<?=$rule->getScoreExpert()?>分）。系统打分指标为从四川省卫生健康统计数据综合采集与决策支持系统、医院绩效考核系统直接获取指标，无需专家评分；专家打分指标需专家根据医院申报材料，结合专科声誉，根据评分标准进行评分。</p>
                        <p>感谢您对我们工作的支持与理解！</p>
                        <p style="font-size:18px;text-indent: 0;text-align: center">
                            <?=Button::setUrl('#')->setIcon('edit')->setEvent("$('.nav-link-assess').click();$('#only-expert').click();")->setSize('btn-lg')->link('开始评审')?>
                        </p>
                        <?php elseif(in_array($grade->getStage()->getCatId(),[235])):
                            //省级区域医疗中心
                        ?>
                        <?php
                        $file = $grade->getStage()->contents('content');
                        ?>
                        <p>尊敬的专家：</p>
                        <p>您好！欢迎您参加四川省<?=$grade->getStage()->getSubject()?>建设项目中期评估评审工作。评分材料反馈时间为X月X日（周X）24点，敬请您拨冗安排时间，进行项目评议。</p>
                                <p>《<?=$grade->getStage()->getCatSubject()?>建设项目中期评估标准》包括指标<?=$rule->getNumber()-2?>项，总分合计<?=$rule->getTotal()?>分。其中包括系统打分指标<?=$rule->getNumber('system')?>项（<?=$rule->getScoreSystem()?>分）与专家打分指标<?=$rule->getNumber('expert')?>项（<?=$rule->getScoreExpert()?>分）。系统打分指标为从四川省卫生健康统计数据综合采集与决策支持系统、医院绩效考核系统直接获取指标，无需专家评分；专家打分指标需专家根据医院申报材料，结合专科提交的“<a href="javascript:;" onclick="$('.nav-link-plan').click();">建设方案</a>”，根据评分标准进行评分。</p>
                        <p>感谢您对我们工作的支持与理解！</p>
                        <p style="font-size:18px;text-indent: 0;text-align: center">
                            <?=Button::setUrl('#')->setIcon('fa fa-paperclip')->setEvent("$('.nav-link-plan').click();")->setClass('btn-alt-info')->setSize('btn-lg')->link('建设方案')?>
                            <?=Button::setUrl('#')->setIcon('edit')->setEvent("$('.nav-link-assess').click();$('#only-expert').click();")->setSize('btn-lg')->link('开始评审')?>
                        </p>
                        <?php elseif(in_array($grade->getStage()->getCatId(),[230])):
                            //国家级临床重点专科
                        ?>
                        <?php
                        $file = $grade->getStage()->contents('content');
                        ?>
                        <p>尊敬的专家：</p>
                        <p>您好！欢迎您参加四川省国家临床重点专科建设项目遴选评审工作。评分材料反馈时间为11月29日（周三）16点，敬请您拨冗安排时间，进行项目评议。</p>
                                <p>《四川省<?=$grade->getStage()->getCatSubject()?>建设项目遴选评估标准（<?=str_replace('评分表(国家级)','',$rule->getSubject())?>）》包括遴选指标<?=$rule->getNumber()?>项，总分合计<?=$rule->getTotal()?>分。遴选指标包括系统打分指标<?=$rule->getNumber('system')?>项（<?=$rule->getScoreSystem()?>分）与专家打分指标<?=$rule->getNumber('expert')?>项（<?=$rule->getScoreExpert()?>分）。系统打分指标为从四川省卫生健康统计数据综合采集与决策支持系统、医院绩效考核系统直接获取指标，无需专家评分；专家打分指标需专家根据医院申报材料和评分标准进行评分。</p>
                        <p>感谢您对我们工作的支持与理解！</p>
                        <p style="font-size:18px;text-indent: 0;text-align: center">
                            <?=Button::setUrl('#')->setIcon('edit')->setEvent("$('.nav-link-assess').click();$('#only-expert').click();")->setSize('btn-lg')->link('开始评审')?>
                        </p>
                        <?php else:?>
                        <p style="font-size:18px;text-indent: 0;text-align: center">
                            <p>尊敬的专家：</p>
                            <p>您好！欢迎您参加<?=$grade->getStage()->getCatSubject()?>建设项目遴选评审工作。评分材料反馈时间为11月23日（周一）24点，敬请您拨冗安排时间，进行项目评议。</p>
                            <?=Button::setUrl('#')->setIcon('edit')->setEvent("$('.nav-link-assess').click();$('#only-expert').click();")->setSize('btn-lg')->link('开始评审')?>
                        </p>
                        <?php endif;?>

                        <div class="clearfix"></div>
                    </div>
                    <div class="tab-pane" id="plan" role="tabpanel">内容加载中...</div>
                    <div class="tab-pane" id="rule" role="tabpanel">
                        <h1 style="font-size:18px; font-weight:bold; font-family:'黑体'; text-align:center;"> 对“
                            <a target="_blank" href="<?=site_url('apply/stage/show/id/'.$grade->getStage()->getStageId())?>"><?=$grade->getStage()->getSubject()?>（<?=$grade->getStage()->getLevel()?>）</a>
                            ”项目进行评审 </h1>
                        <h2 style="font-size:18px; font-weight:bold; font-family:'黑体'; text-align:center;">
                            项目单位：<?=$grade->getStage()->getCorporationName()?>
                        </h2>
                        <h2 style="font-size:16px; font-weight:bold; font-family:'黑体'; text-align:center;">
                            （<?=$grade->getStage()->getSubject()?>评分表）
                        </h2>
                        <div class="alert alert-success" role="alert"><b>温馨提示：</b>系统评分指标无需专家评分，专家评分指标需专家根据申报材料，根据评分标准进行评分，系统最后会计算出该项目的最终得分。若您对该项目有其他的意见请在【<b href="javascript:;" onclick="scrollToLocation('评审意见')" data-content="评审意见" style="cursor: pointer">评审意见</b>】栏目中详细列述。</div>
                        <form id="form1" name="form1" method="post" action="">
                            <?=$rule->getBoxHtml($grade->getMark(),$grade->getRole(),$grade->getStage());?>
                            <table width="100%" border="0" cellspacing="1" cellpadding="3" class="tb_data table table-bordered idea-box" data-item="评审意见">
                                <caption>
                                    专家综合评审意见
                                    <input type="radio" name="is_state" id="is_state" value="1" class="required" checked="checked" hidden="true" />
                                </caption>
                                <tr>
                                    <th width="15%" scope="row">专家意见（选填）</th>
                                    <td width="85%"><textarea class="form-control" name="idea" id="idea" rows="5" ><?=$grade->getIdea()?></textarea></td>
                                </tr>
                                <tr>
                                    <td colspan="2" align="center"><input type="submit" name="Submit" class="btn btn-sm btn-info" value="保存评审结果" onclick="return check();" />
                                        <input type="hidden" name="ruleid" value="<?=$rule->getCode()?>" />
                                        <input type="hidden" name="projectid" value="<?=$grade->getStage()->getStageId()?>" />
                                        <input type="hidden" id="gradeid" value="<?=$grade->getId()?>" />
                                        <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
                                </tr>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="ex_tools">
    <?=Button::setUrl('#')->setIcon('edit')->setEvent("$('.nav-link-assess').click();$('#only-expert').click();")->setSize('btn-lg')->link('开始评审')?>
</div>
<script src="<?=site_path('assets/js/pdfobject.min.js')?>"></script>
<script>
    var options = {
        height: "700px",
        page: '1',
        fallbackLink: "<p><a href='[url]'>点击下载</a></p>",
        pdfOpenParams: {
//                view: 'FitV',
            toolbar: '0',
            pagemode: 'thumbs',
            search: 'lorem ipsum'
        }
    };
    PDFObject.embed("<?=site_url('up_files/'.$file['file_path'])?>", "#plan",options);
</script>
<script>
    $(function (){
        $("#subFrame").height($(window).height() - 180);
        //Sldie
        $(".slides .kefu").mouseenter(function(){
            $(this).find(".kefulist").fadeIn();
        });
        $(".slides .kefu").mouseleave(function(){
            $(this).find(".kefulist").fadeOut();
        });

        $(".nav-tabs li.nav-item a").click(function () {
            if($(this).attr("href")=='#rule'){
                $(".slides").show();
            }else{
                $(".slides").hide();
            }
        });
        $(".mark").change(function () {
            $("#expert-score").text('...');
            var url = '<?=site_url('assesszq/rule/getScore')?>';
            var form1=$("#form1").serialize();
            $.post(url,form1,function (data) {
                $("#expert-score").text(data.expert_score);
            },'json');
        });

    });



    function calculateWidth()
    {
        var width = $(".left-area").width();
        $("#page-nav").css('width',width-30);
    }


    function check()
    {
        var result = true;
        $(".mark").each(function(){
            if($(this).closest('.tb_data').attr('style')!='display:none' && $(this).val().length==0){
                showError('您还有未评审的指标')
                $(this).focus();
                result = false;
                return result;
            }
        });

        return result;
    }

    $(function (){
        var html = '';
        $(".tb_data").each(function (){
            if($(this).attr('style')=='display:none') return;
            var className = $(this).hasClass('system') ? 'system bg-success' : 'expert';
            var score = $(this).find('select').val();
            // if($(this).hasClass('idea-box') && $('#idea').val()==''){
            //     score = '';
            // }
            // if($(this).hasClass('idea-box') && $('#idea').val().length>2){
            //     className = 'bg-success';
            // }
            if(score!=undefined && score==''){
                className+=' bg-warning'
            }
            if(score!=undefined && (score>0 || (score=='是(Y)' || score=='否(N)'))){
                className+=' bg-success'
            }
            var content = $(this).data('item');
            if(content!=undefined){
                var indexNo = noSpaceBr(content.replace(/指标/,''));
                if($(this).hasClass('idea-box')){
                    indexNo = 'idea';
                }
                html += '<li class="'+className+' index-'+indexNo+'"><a href="javascript:;" onclick="scrollToLocation(\''+content+'\')" data-content="'+content+'">'+content+'</a></li>';
            }
        });
        if(html){
            $("#index-ul").html(html);
        }

        $(".nav-link").click(function (){
            var navtext = noSpaceBr($(this).text());
            if(navtext=='评审表'){
                $('.ex_tools').hide();
                $("#page-nav").show();
                $(".left-area").show();
                $(".right-area").removeClass('col-lg-12');
                $(".right-area").addClass('col-md-8').addClass('col-lg-7').addClass('col-xl-9');
                calculateWidth();
            }else if(navtext=='建设方案'){
                $('.ex_tools').show();
                $("#page-nav").hide();
                $(".left-area").hide();
                $(".right-area").removeClass('col-md-8').removeClass('col-lg-7').removeClass('col-xl-9');
                $(".right-area").addClass('col-lg-12');
            }else{
                $('.ex_tools').hide();
                $("#page-nav").hide();
                $(".left-area").hide();
                $(".right-area").removeClass('col-md-8').removeClass('col-lg-7').removeClass('col-xl-9');
                $(".right-area").addClass('col-lg-12');
            }
        });

        $("#only-expert").click(function (){
           if($(this).prop('checked')){
               $(".system").hide();
               // $(".CW").hide();
           }else{
               $(".system").show();
               // $(".CW").show();
           }
        });

        $(".mark").change(function (){
            var indexNo = $(this).data('no');
            indexNo = parseInt(indexNo)+1;
            var score = $(this).val();
            if(score!=''){
                $(".index-"+indexNo).removeClass('bg-warning').addClass('bg-success');
            }else{
                $(".index-"+indexNo).removeClass('bg-success').addClass('bg-warning');
            }
        });

        // $("#idea").change(function (){
        //     if($(this).val().length>0){
        //         $(".index-idea").removeClass('bg-warning').addClass('bg-success');
        //     }else{
        //         $(".index-idea").removeClass('bg-success').addClass('bg-warning');
        //     }
        // });
    });

    function noSpaceBr(text){
        text = text.replace(/&nbsp;/ig, "");
        text = text.replace(/<br>/ig, "");
        text = text.trim();
        return text
    }

    function scrollToLocation(target) {
        var mainContainer = $('html');
        if(target!=undefined){
            var pos = $("table[data-item='"+target+"']").offset().top-70;
            mainContainer.scrollTop(pos);
        }
        if(target=='评审意见'){
            $("#idea").focus();
        }
    }



    jQuery(function () { Dashmix.helpers('sparkline'); });
</script>
