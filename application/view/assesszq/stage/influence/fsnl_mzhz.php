<table class="table table-bordered">
    <tr>
        <td align="center" style="vertical-align: middle;">指标</td>
        <td align="center" style="width: 15%">均值</td>
        <?php
        $startYear = $project->getStartYear();
        $endYear = $project->getEndYear();
        for($i=$startYear;$i<=$endYear;$i++):
            ?>
            <td align="center" style="width: 15%"><?=$i?>年</td>
        <?php endfor;?>
        <?php if(!$configs['action']['download']):?>
            <td align="center" style="width: 95px">
                附件
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center" style="vertical-align: middle"><?=$configs['langs']['fsnl.mzhz']?:'门诊患者中市外（省外）患者比例（%）'?></td>
        <td align="center">
            <?=$project->getAvgDataByIndexCode('influence_fsnl_mzhz','','user')?>
        </td>
        <?php
        $startYear = $project->getStartYear();
        $endYear = $project->getEndYear();
        for($i=$startYear;$i<=$endYear;$i++):
            $projectData = $project->getDataByIndexCode('influence_fsnl_mzhz',$i);
            ?>
            <td align="center">
                <?=$projectData->getData()?>
            </td>
        <?php endfor;?>
        <?php if(!$configs['action']['download']):?>
            <td align="center">
                <div class="btn-group-vertical btn-group-sm" role="group">
                    <?php
                    $itemType = 'mzhz';
                    if (!$configs['action']['download'] && $configs['needfile']['fsnl'][$itemType]): ?>
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_influence_fsnl_'.$itemType).')')->setUrl(site_url('engine/stageattachement/upload/index/item_type/apply_influence_fsnl_'.$itemType.'/id/'.$project->getStageId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    <?php endif;?>
                </div>
            </td>
        <?php endif;?>
    </tr>
    <?php
    if($project->isCityCountryLevel()):
        ?>
        <tr>
            <td align="center">全省该专科三甲医院平均水平</td>
            <td align="center"><?=$project->getProvinceAvgData('influence_fsnl_mzhz')?></td>
            <td colspan="3" align="center"></td>
        </tr>
    <?php else:?>
        <tr class="d-none">
            <td align="center">全省该专科平均水平</td>
            <td align="center"><?=$project->getProvinceAvgData('influence_fsnl_mzhz')?></td>
            <td colspan="3" align="center"></td>
        </tr>
        <tr>
            <td align="center">申报单位排名</td>
            <td align="center"><?=Button::setUrl(site_url('index/rank/ranking/index_code/influence_fsnl_mzhz/order_way/desc/id/'.$project->getProjectId()))->window('查看排名')?></td>
            <td colspan="3" align="center"></td>
        </tr>
    <?php endif;?>
</table>