<table class="table table-bordered">
    <thead>
    <tr>
        <td align="center">
            新技术新项目名称
        </td>
        <td align="center">
            开展<br>年度
        </td>
        <td align="center">
            开展<br>例数
        </td>
        <td align="center" style="width: 37%">
            新技术新项目先进性
        </td>
        <td align="center" style="width: 90px">附件</td>
    </tr>
    </thead>
    <tbody>
    <?php
    $techs = $project->newtechs();
    while($tech = $techs->getObject()):
        ?>
        <tr>
            <td>
                <?=$tech->getSubject()?>
            </td>
            <td align="center">
                <?=$tech->getYear()?>
            </td>
            <td align="center">
                <?=$tech->getCase()?>
            </td>
            <td align="center">
                <?=getCheckedStr(['省内先进','国内先进','国际先进','省内领先','国内领先','国际领先'],$tech->getLevel())?>
            </td>
            <td align="center">
                <div class="btn-group-vertical btn-group-sm" role="group">
                    <?php
                    $itemType = 'xjs';
                    ?>
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_innovatezq_common_'.$itemType.'_'.$tech->getId()).')')->setUrl(site_url('engine/stageattachement/upload/index/item_type/apply_innovatezq_common_'.$itemType.'_'.$tech->getId().'/id/'.$project->getStageId().'/is_show/1'))->setClass('btn-alt-success')->window()?>
                </div>
            </td>
        </tr>
    <?php endwhile;?>
    <?php if($techs->getTotal()==0):?>
        <tr>
            <td colspan="5" align="center">无</td>
        </tr>
    <?php endif;?>
    </tbody>
</table>