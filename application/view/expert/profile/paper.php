<script language="javascript" type="text/javascript" src="<?=site_path("js/DatePicker/WdatePicker.js")?>"></script>
<div class="page-header">
  <div class="tools btn-group btn-group-sm" role="group"> 
    <?=$button?>
</div>
</div>
<div class="page-body">
  <ul class="nav nav-tabs" role="tablist">
    <?php foreach($tabs as $tab):?>
    <li role="presentation"<?php if($tab['method'] == $method):?> class="active"<?php endif;?>><a href="<?=$tab['url']?>" aria-controls="<?=$tab['method']?>" role="tab">
      <?=$tab['text']?>
      </a></li>
    <?php endforeach;?>
  </ul>
  <div class="tab-content">
    <div role="tabpanel" class="tab-pane active">
    <form class="form-horizontal" id="validateForm" name="validateForm" action="" method="post">
    <p>个人主要著作/论文<small>（最多10项）</small></p>
    <table width="100%" class="table table-hover">
      <thead>
        <tr>
          <td width="25%" align="center">著作/论文名称</td>
          <td width="6%" align="center">作者排序</td>
          <td width="25%" align="center">期刊/出版社名称</td>
          <td width="10%" align="center">出版/发表日期</td>
          <td width="6%" align="center">期数</td>
          <td width="6%" align="center">期刊级别</td>
          <td colspan="2" align="center">操作</td>
        </tr>
      </thead>
      <tbody id="paper-content">
        <?php
			$papers = $expert->selectPapers();
			while($paper = $papers->getObject()):
		?>
        <tr>
          <td><input type="hidden" name="ids[]" value="<?=$paper->getId()?>" /><input type="text" name="subject[]" class="form-control" value="<?=$paper->getSubject()?>" /></td>
          <td><select name="author_order[]">
                  <option value="">请选择</option>
                  <?=getSelectFromArray(['独著','第一作者','第二作者','其他'],$paper->getAuthorOrder())?>
              </select>
          </td>
          <td><input name="publisher[]" type="text" class="form-control" value="<?=$paper->getPublisher()?>" size="8" /></td>
          <td><input name="pubilsh_time[]" type="text" class="form-control" value="<?=$paper->getPubilshTime()?>" size="8" onclick="WdatePicker({startDate:'1990-01',dateFmt:'yyyy-MM'})" /></td>
          <td><select name="periods[]">
          	<option value="">请选择</option>
           	<?=getSelectFromArray(['第1期','第2期','第3期','第4期','第5期','第6期','第7期','第8期','第9期','第10期','第11期','第12期'],$paper->getPeriods())?>
          </select></td>
          <td><select name="level[]">
          	<option value="">请选择</option>
           	<?=getSelectFromArray(['中文核心','SCI收录','EI收录','ISTP收录','外文期刊'],$paper->getLevel())?>
          </select></td>
          <td width="7%" align="center" class="copy">增加</td>
          <td width="5%" align="center" class="del">删除</td>
        </tr>
        <?php endwhile;?>
        <tr>
          <td><input type="text" name="subject[]" class="form-control" value="" /></td>
          <td><select name="author_order[]">
                  <option value="">请选择</option>
                  <?=getSelectFromArray(['独著','第一作者','第二作者','其他'],'')?>
              </select>
          </td>
          <td><input name="publisher[]" type="text" class="form-control" value="" size="8" /></td>
          <td><input name="pubilsh_time[]" type="text" class="form-control" value="" size="8" onclick="WdatePicker({startDate:'1990-01',dateFmt:'yyyy-MM'})" /></td>
          <td><select name="periods[]">
          	<option value="">请选择</option>
           	<?=getSelectFromArray(['第1期','第2期','第3期','第4期','第5期','第6期','第7期','第8期','第9期','第10期','第11期','第12期'],'')?>
          </select></td>
          <td><select name="level[]">
          	<option value="">请选择</option>
           	<?=getSelectFromArray(['中文核心','SCI收录','EI收录','ISTP收录','外文期刊'],'')?>
          </select></td>
          <td width="7%" align="center" class="copy">增加</td>
          <td width="5%" align="center" class="del">删除</td>
        </tr>
</tbody>
        </table>
        <div class="form-group col-xs-12 col-sm-12 col-md-12">
          <div class="col-xs-12 col-sm-5 col-md-9">
            <button type="submit" class="btn btn-info btn-sm">保存资料</button>
          </div>
        </div>
      </form>
    </div>
    <p style="clear:both"></p>
  </div>
</div>
