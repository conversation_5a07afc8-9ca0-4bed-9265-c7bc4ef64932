<link rel="stylesheet" href="<?=site_path("assets/js/plugins/flatpickr/flatpickr.min.css")?>">
<div class="content">
    <h2 class="content-heading">
        个人中心/资料维护
    </h2>

    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                    <?php endforeach;?>
                </ul>

                <div class="block-content tab-content">
                    <div class="tab-pane active">
                        <form class="mb-5" id="validateForm" name="validateForm" action="" method="post">
                            <div class="form-group ">
                                <div class="col-sm-12 text-center">
                                    <div class="alert alert-warning " role="alert">
                                        <p class="mb-0">
                                            个人主要专利信息<small>（最多10项）</small>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <table class="table table-hover table-vcenter">
                                <thead>
                                <tr>
                                    <th width="12%" class="text-center">专利类型</th>
                                    <th width="16%" class="text-center">申请号/专利号</th>
                                    <th width="24%" class="text-center">专利名称</th>
                                    <th width="16%" class="text-center">发明人 </th>
                                    <th width="12%" class="text-center">申请日期</th>
                                    <th width="20%" class="text-center">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $patents = $expert->selectPatents();
                                    while($patent = $patents->getObject()):
                                    ?>
                                    <tr>
                                        <td class="font-w600">
                                            <input type="hidden" name="ids[]" value="<?=$patent->getId()?>" />
                                            <select class="custom-select" name="type[]">
                                                <option value="">请选择</option>
                                                <?=getSelectFromArray(['5'=>'国际专利','1'=>'国家发明','2'=>'国家实用新型','3'=>'国家外观设计','4'=>'软件著作权'],$patent->getType(),false)?>
                                            </select>
                                        </td>
                                        <td class="text-center" >
                                            <input type="text" name="sn[]" class="form-control" value="<?=$patent->getSn()?>" />
                                        </td>
                                        <td class="text-center" >
                                            <input type="text" name="subject[]" class="form-control" value="<?=$patent->getSubject()?>" />
                                        </td>
                                        <td class="text-center" >
                                            <input type="text" name="fmr[]" class="form-control" value="<?=$patent->getFmr()?>" />
                                        </td>
                                        <td class="text-center" >
                                            <input type="text" name="apply_at[]" class="js-flatpickr form-control bg-white" value="<?=$patent->getApplyAt()?>"/>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-primary copyNew" data-toggle="tooltip" title="Copy">添加</button> &nbsp;
                                                <button type="button" class="btn btn-sm btn-primary delNew" data-toggle="tooltip" title="Delete">删除</button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile;?>

                                <tr>
                                    <td class="font-w600">
                                        <select class="custom-select" name="type[]">
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(['5'=>'国际专利','1'=>'国家发明','2'=>'国家实用新型','3'=>'国家外观设计','4'=>'软件著作权',],'',false)?>
                                        </select>
                                    </td>
                                    <td class="text-center" >
                                        <input type="text" name="sn[]" class="form-control"/>
                                    </td>
                                    <td class="text-center" >
                                        <input type="text" name="subject[]" class="form-control"/>
                                    </td>
                                    <td class="text-center" >
                                        <input type="text" name="fmr[]" class="form-control"/>
                                    </td>
                                    <td class="text-center" >
                                        <input type="text" name="apply_at[]" class="js-flatpickr form-control bg-white"/>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary copyNew" data-toggle="tooltip" title="Copy">添加</button> &nbsp;
                                            <button type="button" class="btn btn-sm btn-primary delNew" data-toggle="tooltip" title="Delete">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                            <div class="form-group row">
                                <div class="col-sm-9 ml-auto">
                                    <button type="submit" class="btn btn-primary">保存资料</button>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="<?=site_path("assets/js/plugins/flatpickr/flatpickr.min.js")?>"></script>
<script>
    jQuery(function () {
        // Dashmix.helpers('flatpickr');
        flatpickrInit();
    });

    function flatpickrInit() {
        $(".js-flatpickr").flatpickr({
            minDate: "1990-01",
            // maxDate: "2020-12-31"
            dateFormat: "Y-m"
        });
    }
</script>