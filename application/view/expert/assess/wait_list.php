<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                等待评审的项目
            </h3>
            <div class="block-options">
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="alert alert-success" role="alert">
                    <b>尊敬的专家：</b>　您好，请您对以下项目进行评审。评审过程中有任何问题请拨打页面下方的电话进行咨询。
                </div>
                <div class="search">
                    <table width="100%" align="center">
                        <tbody id="show_search">
                        <form action="<?=site_url("expert/assess/wait_list")?>" method="post" name="search" id="search">
                            <tr>
                                <td>关键词：
                                    <input id="search" name="search" class="form-control w-auto custom-control-inline" />
                                    <!-- <label><input type="radio" name="field" value="accept_id" checked="checked" />申报编号</label> -->
                                    <label><input name="field" type="radio" value="subject" checked="checked"/>项目名称</label>
                                    <label><input name="field" type="radio" value="type_subject" />研究对象</label>
                                    <?=btn('button','搜索','submit','find')?>
                                </td>
                            </tr>
                        </form>
                        </tbody>
                    </table>
                </div>
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("admin/plan/doAccept")?>">
                        <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th width="30">详</th>
                                <th>项目名称</th>
                                <th>研究对象</th>
                                <th>项目类别</th>
                                <th>申报年度</th>
                                <th class="text-center">申报书</th>
                                <th width="150" class="text-center">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($grade = $pager->getObject()):?>
                                <tr>
                                    <td><input name="select_id[]" type="checkbox" value="<?=$grade->getProject()->getProjectId()?>" /></td>
                                    <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$grade->getProject()->getId()?>').toggle();">+</label></td>
                                    <td><a href="<?=site_url("apply/project/show/id/".$grade->getProject()->getProjectId())?>" target="_blank"><?=$grade->getProject()->getSubject()?></a></td>
                                    <td><?=$grade->getProject()->getTypeSubject()?></td>
                                    <td><?=$grade->getProject()->getProjectTypeSubject()?></td>
                                    <td><?=$grade->getProject()->getDeclareYear()?></td>
                                    <td align="center"><a href="<?=site_url("apply/project/show/id/".$grade->getProject()->getProjectId())?>" class="btn btn-info btn-sm" role="button" target="_blank">查看</a></td>
                                    <td align="center">
                                        <a href="<?=site_url("apply/assess/assess/id/".$grade->getId())?>" class="btn btn-success btn-sm" role="button">评审</a>
                                        <a href="#" onClick="return showWindow('弃权','<?=site_url("expert/project/doNonuser/id/".$grade->getId())?>',{area:['600px','400px']});" class="btn btn-danger btn-sm" role="button">弃权</a>
                                    </td>
                                </tr>
                                <tr id="show_<?=$grade->getProject()->getId()?>" style="display:none;">
                                    <td colspan="9">
                                        <?php include('more_part.php') ?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="9" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

