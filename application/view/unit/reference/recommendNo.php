<style>
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<script type="text/javascript" src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<div class="block block-rounded">
    <div class="page-body">
        <div class="block-content tab-content">
            <div class="tab-pane active" id="tab1" role="tabpanel">
                <div class="clearfix"></div>
                <div class="alert alert-warning alert-dismissable" role="alert">
                    <h3 class="alert-heading font-size-h6 my-2">
                        请对以下项目进行排序
                    </h3>
                </div>
                <form name="form1" class="form-horizontal" id="validateForm" action="" method="post">
                    <div class="header no-margin-top">待推荐的项目</div>
                    <table class="table table-sm">
                        <thead>
                        <tr>
                            <th class="text-center" style="width: 10%">排序</th>
                            <th class="text-center" style="width: 10%">序号</th>
                            <th>项目名称</th>
                            <th>项目负责人</th>
                        </tr>
                        </thead>
                        <tbody id="file-list-<?=$item_type?>" class="file-list-group">
                        <?php
                            while($project = $projects->getObject()):
                        ?>
                            <tr>
                                <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                <td><input class="form-control" style="text-align: center" type="number" min=1 name="no[<?=$project->getProjectId()?>]" value="<?=$projects->getIndex()?>" /></td>
                                <td><?=$project->getSubject()?></td>
                                <td><?=$project->getUserName()?></td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tr>
                    </table>
                    <div class="clearfix"></div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <div style="width:300px;margin:0 auto;text-align:center">
                            <?=Button::setType('button')->setClass('btn-alt-primary btn-download')->setIcon('download')->button('下载推荐函模板')?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="page-footer">
        <!--右下角浮动-->

    </div>
</div>
<script>
    var baseurl = $('#baseUrl').val();

    var filetable = document.getElementById('file-list-<?=$item_type?>');
    var sortable = new Sortable(filetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $(".file-list-group tr").each(function (){
                $(this).find("input[name^=no]").val(++index);
            })
        },

    });

    function updateSort() {
        var url = $("#validateForm").attr('action');
        $.post(url,$("#validateForm").serialize(),function(data){
            if (data.code==0) {
                // showSuccess('排序更新成功');
            } else {
                // showError('排序更新失败');
            }
        },'json');
    }

    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm"></span> 下载中...');
    }

    function handleEvent(evt) {
        if (evt.lengthComputable) {
            var percentComplete = evt.loaded / evt.total;
            percentComplete = (percentComplete * 100).toFixed(2);
            percentComplete = percentComplete + '%';
            $(".btn-download").html('<span class="spinner-border spinner-border-sm"></span> 下载中('+percentComplete+')...');
        }
    }

    function download(file) {
        var request = new XMLHttpRequest();
        request.responseType = 'blob';
        request.open('GET', file);
        request.addEventListener('progress', handleEvent);
        request.addEventListener('load', function () {
            downloadComplete(request.response);
        });
        request.send();
    }

    function save(object, name) {
        var a = document.createElement('a');
        var url = URL.createObjectURL(object);
        a.href = url;
        a.download = name;
        a.click();
    }

    function downloadComplete(file)
    {
        save(file, '项目推荐函.docx');
        $(".btn-download").removeAttr('disabled').html('<i class="fa fa-check"></i> 下载完成');
    }

    $('.btn-download').click(function (){
        //更新排序
        updateSort();
        //下载模板
        var url = '<?=site_url('unit/reference/downloadTpl')?>';
        download(url);
    });
</script>