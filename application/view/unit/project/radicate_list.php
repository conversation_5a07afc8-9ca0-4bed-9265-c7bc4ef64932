<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <!-- <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a> -->
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button">导出数据</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
    <table width="100%" align="center">
  
  <tbody id="show_search">
  <form action="" method="post" name="search" id="search">
    <tr>
      <td>关键词：
        <input id="search" name="search" class="form-control w-auto custom-control-inline" />
        <label><input type="radio" name="field" value="subject" checked="checked" />项目名称</label>
        <label><input type="radio" name="field" value="user_name" />项目负责人</label>
        <label><input type="radio" name="field" value="radicate_id" />立项编号</label>
        <label><input type="radio" name="field" value="radicate_money" />立项经费</label>
        <!-- <label><input type="radio" name="field" value="radicate_year" />立项年度</label> -->
        <!-- <label><input type="radio" name="field" value="type_subject" />研究对象</label> -->
        <!-- <label><input type="radio" name="field" value="guide_field" />指南领域 &nbsp;</label> -->
        <select name="radicate_year" id="radicate_year">
          <option value="">=立项年度=</option>
          <?=getYearList(input::getInput("mix.radicate_year"))?>
        </select>
        <select name="type_subject" id="type_subject">
          <option value="">=研究对象=</option>
          <?=getProjectType(input::getInput("mix.type_subject"))?>
        </select>
<!--         <select name="guide_id" id="guide_id">
          <option value="">=指南领域=</option>
          <?=getGuideField(input::getInput("mix.guide_id"))?>
        </select> -->
        <?=btn('button','搜索','submit','find')?></td>
    </tr>
  </form>
 </tbody> 
</table>

  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
    <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
        <th class="tselect"><input name="selectAll" type="checkbox" id="selectAll"/></th>
        <th class="tmore">详</th>
        <th class="tsubject"><?=getColumnStr('立项编号','radicate_id')?></th>
        <th class="tsubject" width="25%"><?=getColumnStr('项目名称','subject')?></th>
        <th class="tuser"><?=getColumnStr('项目负责人','user_id')?></th>
        <th class="ttype"><?=getColumnStr('研究对象','type_id')?></th>
        <th class="tuser"><?=getColumnStr('立项经费','radicate_money')?></th>
        <th class="tuser"><?=getColumnStr('立项年度','radicate_year')?></th>
        <th class="tuser"><?=getColumnStr('起始年月','start_at')?></th>
        <th class="tuser"><?=getColumnStr('结束年月','end_at')?></th>
        <th class="hidden-480 text-center">应验收项目</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> 
        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
        <td align="center"><?=$project->getRadicateId()?></td>
        <td width="25%"><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?></td>
        <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
        <td align="center"><?=$project->getTypeSubject()?></td>
        <td align="center"><?=$project->getRadicateMoney()?>万元</td>
        <td align="center"><?=$project->getRadicateYear()?></td>
        <td align="center"><?=$project->getStartAt()?></td>
        <td align="center"><?=$project->getEndAt()?></td>
        <td align="center"><?=$project->shouldCheckProject()?></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="12">
         <?php include('more_part.php') ?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="12" align="right">&nbsp;<span class="pager_bar"><?=$pager->total().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
