<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                待审核的项目
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <!--
                <?=Button::setName('审核选中项')->setEvent('validateForm.action=\''.site_url("unit/project/doSubmit").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
                -->
                <?=Button::setName('退回选中项')->setEvent('validateForm.action=\''.site_url("unit/project/doRejected").'\';$(\'#validateForm\').submit()')->setIcon('undo')->setClass('btn-alt-danger')->button()?>
                <?=btn('link','导出数据',site_url('export/export/index'),'export')?>
            </div>
        </div>
        <div class="block-content pt-0">
            <div class="main">
                <div class="search">
                    <?php include_once('search_part.php')?>
                </div>
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="20" class="tselect"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th width="17" class="tmore">详</th>
                                <th width="371" class="tsubject"><?=getColumnStr('项目名称','subject')?></th>
                                <th class="tuser"><?=getColumnStr('负责人','user_id')?></th>
                                <th class="tuser"><?=getColumnStr('依托单位','corporation_id')?></th>
                                <th class="tyear"><?=getColumnStr('申报年度','declare_year')?></th>
                                <th width="230" class="text-center">可用操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($project = $pager->getObject()):?>
                                <tr> <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                                    <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                    <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject(),array('target'=>"_blank"))?></td>
                                    <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                                    <td><?=$project->getCorporationName()?></td>
                                    <td><?=$project->getDeclareYear()."(".$project->getTypeCurrentGroup().")"?></td>
                                    <td align="center">
                                        <?=Button::setName('审核')->setUrl(site_url("apply/project/show/id/".$project->getProjectId()))->setIcon('find')->link()?>
                                        <?=Button::setName('推荐')->setUrl(site_url("unit/project/doSubmitOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-success')->setIcon('check')->setWidth('800px')->setHeight('600px')->window()?>
                                        <?=Button::setName('退回')->setUrl(site_url("unit/project/doback/id/".$project->getProjectId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                                    </td>
                                </tr>
                                <tr class="fold_body">
                                    <td colspan="8">
                                        <?php include('more_part.php')?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="8" align="right">&nbsp;<span class="pager_bar"><?=$pager->total().$pager->navbar(10)?></span></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
