<script language="javascript" type="text/javascript">
    function todo(json)
    {
        var username = json.username;
        var userid = json.userid;
        $.post('<?=site_url("unit/project/changeUserName")?>',{id:json.itemid,username:json.username,userid:json.userid},function(m){
            layer.msg(m);
            top.location.reload();
        });
    }

</script>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                还未关联项目负责人的项目
            </h3>
        </div>
        <div class="block-content pt-0">
            <div class="main">
                <div class="search">
                    <?php include_once('search_part.php')?>
                </div>
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="20"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th><?=getColumnStr('项目名称','subject')?></th>
                                <th><?=getColumnStr('项目级别','level')?></th>
                                <th><?=getColumnStr('立项年度','radicate_year')?></th>
                                <th width="300"><?=getColumnStr('项目负责人','user_name')?></th>
                                <th width="100">项目状态</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($project = $pager->getObject()):?>
                                <tr> <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                                    <td><?=$project->getSubject()?></td>
                                    <td><?=$project->getLevel()?></td>
                                    <td><?=$project->getRadicateYear()?></td>
                                    <td>
                                        <?php
                                        if($project->getUserId()):
                                            ?>
                                            <a href="<?=site_url('user/profile/show/userid/'.$project->getUserId())?>" target="_blank"><?=$project->getUserName()?></a>
                                        <?php else:?>
                                            <?=$project->getUserName()?><?=!$project->getUserId()?'<b class="text-danger">(未关联)</b>':''?>
                                        <?php endif;?>
                                        <?=Button::setUrl(site_url('common/user_search_company/companyid/'.$project->getCorporationId().'/itemid/'.$project->getProjectId()))->setClass(!$project->getUserId()?'btn-alt-warning':'btn-alt-success')->window(!$project->getUserId()?'关联':'更换')?>
                                    </td>
                                    <td><?=$project->getState()?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="9" align="right">&nbsp;<span class="pager_bar"><?=$pager->total().$pager->navbar(10)?></span></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
