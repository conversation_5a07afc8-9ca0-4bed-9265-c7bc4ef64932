<script type="text/javascript">
  function todo(json)
  {
   $.post('<?=site_url("unit/profile/coupling")?>',{userid:json.userid,username:json.username},function(json){
    eval("json = '"+json+"'");
    if(json.msg) alert(json.msg);
    document.location.reload();		
  });
 }
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            申报单位资料
        </h2>
        <div class="content-options">
            <?=$htmlStr?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method)echo 'nav-link active';else echo 'nav-link'; ?>" href="<?=$tab['url']?>"><?=$tab['text']?></a>
                        </li>
                    <?php endforeach;?>
                </ul>
                <div class="tab-content">
                <div role="tabpanel" class="tab-pane active">
                  <table width="100%" class="table table-hover">
                    <thead>
                      <tr>
                        <td width="146" align="center">登录账号</td>
                        <td width="123" align="center">姓名</td>
                        <td width="179" align="center">身份证</td>
                        <td width="160" align="center">手机</td>
                        <td width="199" align="center">邮箱</td>
                        <td width="200" align="center">操作</td>
                      </tr>
                    </thead>
                    <tbody id="paper-content">
                      <?php
                      $users = $company->selectUsers();
                      while($user = $users->getObject()):?>
                      <tr>
                        <td align="center"><?=$user->getUserName()?><?php if($company->isManager($user->getUserId())):?>&nbsp;<span class="badge badge-pill badge-primary">默认</span><?php endif;?></td>
                        <td align="center"><?=$user->getUserUsername()?></td>
                        <td align="center"><?=$user->getUserIdcard()?></td>
                        <td align="center"><?=$user->getUserMobile()?></td>
                        <td align="center"><?=$user->getUserEmail()?></td>
                        <td align="center">
                            <?php if(!$company->isManager($user->getUserId()) && $company->isManager(input::getInput("session.userid"))):?>
                                <?=Button::setUrl(site_url("unit/profile/setManager/userid/".$user->getUserId()))->setIcon('fa fa-user-check')->setClass('btn-alt-info')->link('设为默认')?>
                                <?=Button::setUrl(site_url("unit/profile/decoupling/userid/".$user->getUserId()))->setIcon('fa fa-user-slash')->setClass('btn-alt-danger')->window('解除绑定')?>
                        <?php else:?>
                                <?=Button::setType('button')->setIcon('fa fa-user-check')->setClass('btn-alt-info disabled')->button('设为默认')?>
                                <?=Button::setType('button')->setIcon('fa fa-user-slash')->setClass('btn-alt-danger disabled')->button('解除绑定')?>
                        <?php endif;?>
                        </td>
                      </tr>
                    <?php endwhile;?>
                  </tbody>
                  <?php if($company->isManager(input::getInput("session.userid"))):?>
                    <tfoot>
                      <tr>
                        <td colspan="6" class="text-center">
                            <?=Button::setUrl(site_url("unit/profile/search_account"))->setIcon('fa fa-user-plus')->window('增加管理员')?>
                        </td>
                      </tr>
                    </tfoot>
                  <?php endif;?>
                </table>
                <div class="alert alert-success" role="alert">
                  <h4>温馨提醒：</h4>
                  <ul>
                    <li>（1）单位默认管理员可以增加多个其他的管理员辅助管理单位相关事宜；</li>
                    <li>（2）单位管理员变更时，也需要先增加待变更的管理员并设为默认后，方可解除原管理员账号与该单位的关联；</li>
                    <li>（3）设为默认的管理员可以拥有修改其他管理员的权限，也将变为单位默认的联系人，将接收一切发送给单位的提醒短消息（修改了默认管理员请注意检查联系人信息是否完整）；</li>
                    <li>（4）对那些不需要的管理员可以使用“解除绑定”功能解除其账号与单位的关联；</li>
                    <li>（5）增加管理员的时候只能选择归属于自己单位的项目负责人或评审专家。</li>
                  </ul>
                </div>
                <p style="clear:both"></p>
              </div>
            </div>
            </div>
        </div>
    </div>
</div>
