<script language="javascript" type="text/javascript" src="<?=site_path("js/DatePicker/WdatePicker.js")?>"></script>
<div class="page-header">
 <div class="tools btn-group btn-group-sm" role="group"> <a href="<?=site_url("unit/profile/download/userid/".$expert->getUserId())?>" class="btn btn-primary btn-sm btn-submit"><i class="glyphicon glyphicon-print"></i> 导出打印</a>  <a href="<?=site_url("unit/profile/submit/userid/".$expert->getUserId())?>" class="btn btn-danger btn-sm btn-submit"><i class="glyphicon glyphicon-open"></i> 资料上报</a> </div>
</div>
<div class="page-body">
  <ul class="nav nav-tabs" role="tablist">
    <?php foreach($tabs as $tab):?>
    <li role="presentation"<?php if($tab['method'] == $method):?> class="active"<?php endif;?>><a href="<?=$tab['url']?>" aria-controls="<?=$tab['method']?>" role="tab">
      <?=$tab['text']?>
      </a></li>
    <?php endforeach;?>
  </ul>
  <div class="tab-content">
    <div role="tabpanel" class="tab-pane active">
    <form class="form-horizontal" id="validateForm" name="validateForm" action="" method="post">
    <p>个人主要社会兼职、聘任信息<small>(该内容主要用于评审回避，请如实填写，单位名称请使用全称，最多5项)</small></p>
    <table width="100%" class="table table-hover">
      <thead>
        <tr>
          <td width="10%" align="center">起始时间</td>
          <td width="9%" align="center">截止时间</td>
          <td width="30%" align="center">单位名称</td>
          <td width="39%" align="center">说明</td>
          <td colspan="2" align="center">/</td>
        </tr>
      </thead>
      <tbody id="paper-content">
        <?php
			$employs = $expert->selectEmploys();
			while($employ = $employs->getObject()):
		?>
        <tr>
          <td><input type="hidden" name="ids[]" value="<?=$employ->getId()?>" /><input name="start_at[]" type="text" class="form-control" value="<?=$employ->getStartAt()?>" size="8" onclick="WdatePicker({startDate:'1990-01',dateFmt:'yyyy-MM'})" /></td>
          <td><input name="end_at[]" type="text" class="form-control" value="<?=$employ->getEndAt()?>" size="8" onclick="WdatePicker({startDate:'1990-01',dateFmt:'yyyy-MM'})" /></td>
          <td><input name="subject[]" type="text" class="form-control" value="<?=$employ->getSubject()?>" /></td>
          <td><input name="brief[]" type="text" class="form-control" value="<?=$employ->getBrief()?>" size="8" /></td>
          <td width="6%" align="center" class="copy">增加</td>
          <td width="6%" align="center" class="del">删除</td>
        </tr>
        <?php endwhile;?>
         <tr>
          <td><input name="start_at[]" type="text" class="form-control" value="" size="8" /></td>
          <td><input name="end_at[]" type="text" class="form-control" value="" size="8" /></td>
          <td><input name="subject[]" type="text" class="form-control" value="" /></td>
          <td><input name="brief[]" type="text" class="form-control" value="" size="8" /></td>
          <td width="6%" align="center" class="copy">增加</td>
          <td width="6%" align="center" class="del">删除</td>
        </tr>
</tbody>
        </table>
        <div class="form-group col-xs-12 col-sm-12 col-md-12">
          <div class="col-xs-12 col-sm-5 col-md-9">
            <button type="submit" class="btn btn-info btn-sm">保存资料</button>
          </div>
        </div>
      </form>
    </div>
    <p style="clear:both"></p>
  </div>
</div>
