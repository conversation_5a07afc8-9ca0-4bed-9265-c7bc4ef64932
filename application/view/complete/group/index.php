<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        待分配专家的组
                    </h3>
                    <div class="block-options">
                        <?=Button::setUrl(site_url("complete/group/sendMessageAll"))->window('群发通知')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <form action="" method="post" name="search" id="search">
                        <table width="100%" align="center">
                            <tbody id="show_search">
                                <tr>
                                    <td>
                                        <label>分组编号：
                                            <input name="subject" type="text" id="subject" value="<?=input::getInput("mix.subject")?>" class="form-control w-auto custom-control-inline" />
                                        </label>
                                        <label>分组备注：
                                            <input name="note" type="text" id="note" value="<?=input::getInput("mix.note")?>" class="form-control w-auto custom-control-inline" />
                                        </label>
                                        <select name="declare_year" id="declare_year" class="required form-control w-auto custom-control-inline">
                                            <option value="">=年度=</option>
                                            <?=getYearList(input::getInput("mix.declare_year"))?>
                                        </select>
                                        <?=btn('button','搜索','submit','find')?></td>
                                </tr>
                            </tbody>
                        </table>
                        </form>
                    </div>
                    <?php if($pager->getTotal()):?>
                        <div class="box">
                            <div class="no-padding no-margin panel panel-default" >
                                <form id="validateForm" name="validateForm" method="post" action="<?=site_url("complete/group/merge")?>">
                                    <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
                                        <thead>
                                        <tr>
                                            <th width="50"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                            <th><?=getColumnStr('分组编号','group_subject')?></th>
                                            <th><?=getColumnStr('项目个数','project_count')?></th>
                                            <th>专家个数</th>
                                            <th><?=getColumnStr('分组时间','updated_at')?></th>
                                            <th><?=getColumnStr('分组备注','group_note')?></th>
                                            <th width="300" class="text-center">可用操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php while($group = $pager->getObject()):?>
                                            <tr>
                                                <td><input name="select_id[]" type="checkbox" value="<?=$group->getId()?>" /></td>
                                                <td><?=link_to("complete/group/show/id/".$group->getId(),$group->getGroupSubject())?></td>
                                                <td><?=$group->getProjectCount()?></td>
                                                <td><?=$group->getExpertCount()?></td>
                                                <td><?=$group->getCreatedAt()?></td>
                                                <td><?=$group->getGroupNote()?></td>
                                                <td align="center">
                                                    <?=btn('window','备注',site_url("complete/group/doEdit/id/".$group->getId()),'edit')?><?=btn('link','撤消',site_url("complete/group/doCancel/id/".$group->getId()),'trash','btn-sm','btn-danger','return confirm(\'撤消操作将不可恢复，您确定要撤消吗？\');')?>
                                                    <?=btn('link','分配',site_url("complete/assign/doAssignExpertForGroup/id/".$group->getId()),'plus')?>
                                                    <!-- <?=btn('window','记录',site_url("admin/history/index/id/".$group->getId()),'message')?> -->
                                                </td>
                                            </tr>
                                        <?php endwhile;?>
                                        </tbody>
                                        <tfoot>
                                        <tr>
                                            <td colspan="2" align="left"><button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('合并操作不可恢复，合并后所有的项目将移动到相应的分组，你确定需要合并吗？');"><i class="glyphicon glyphicon-plus"></i> 合并选择的组</button></td>
                                            <td colspan="5" align="right"><span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                                        </tr>
                                        </tfoot>
                                    </table>
                                </form>
                            </div>
                        </div>
                    <?php else:?>
                        <div class="box">
                            <p class="note">暂无符合条件的项目，请选择上面搜索条件重新搜索定位。</p>
                        </div>
                    <?php endif;?>
                </div>
            </div>
        </div>
    </div>
</div>
