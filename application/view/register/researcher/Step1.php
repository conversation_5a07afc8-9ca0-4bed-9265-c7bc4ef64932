<link rel="stylesheet" href="<?=site_path('assets/bootstrap-select/bootstrap-select.min.css')?>" />
<script src="<?=site_path('assets/bootstrap-select/bootstrap-select.min.js')?>"></script>
<script language="javascript" type="text/javascript">
    function unit(json){
        $('#corporation_id').val(json.id);
        $('#corporation_name').val(json.subject);
        closeWindow();
        $('#validateForm').data('bootstrapValidator').updateStatus('corporation_name', 'VALID');
    }
</script>
<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            项目负责人注册
        </h3>
        <div class="block-options">
        </div>
    </div>
    <div class="block-content">
        <form class="form-horizontal" name="validateForm" id="validateForm" enctype="multipart/form-data" action="" method="post">
            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">姓名<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <input type="text" class="form-control" id="user_name" name="user_name" value="<?=$data['user_name']?>" required>
                </div>
            </div>
            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">身份证号<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <input type="text" class="form-control" id="user_idcard" name="user_idcard" value="<?=$data['user_idcard']?>" required>
                </div>
            </div>
            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">手机<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <input type="text" class="form-control" id="user_mobile" name="user_mobile" value="<?=$data['user_mobile']?>" required>
                </div>
            </div>
            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">座机<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <input type="text" class="form-control" id="phone" name="phone" value="<?=$data['phone']?>" required>
                    <p class="help-block">请填写座机电话，电话前请加上区号，如：(028)88888888。</p>
                </div>
            </div>
            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">E-mail<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <input type="text" class="form-control" id="user_email" name="user_email" value="<?=$data['user_email']?>" required>
                    <p class="help-block">请输入电子邮箱。</p>
                </div>
            </div>
            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">所属单位<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <input class="form-control" name="corporation_name" type="text" id="corporation_name" title="请选择单位。" value="<?=$data['corporation_name']?>" readonly="readonly" style="float: left" />
                    <input type="hidden" name="corporation_id" id="corporation_id" value="<?=$data['corporation_id']?>" />
                    <p class="help-block">如果查询不到自己的单位，请确认单位是否注册。</p>
                </div>
                <div class="col-md-4 col-sm-4 col-xs-4">
                    <button type="button" class="btn btn-sm btn-alt-primary" name="button2" id="button" onclick="return showWindow('选择单位','<?=site_url("common/unit_search")?>',600,400);" style="height: 38px;">选择单位</button>
                </div>
            </div>

            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">所属专业<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <select name="subject_code" id="subject_code" class="form-control" required data-live-search="true">
                        <option value="">请选择专科</option>
                        <?=getSelectFromArray(getZdzkList('code'),$data['subject_code'],false)?>
                    </select>
                </div>
            </div>

            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">学历<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5">
                    <select name="education" id="education" class="form-control" required>
                        <option value="">请选择学历</option>
                        <?=getSelectFromArray(get_select_data('education'),$data['education'])?>
                    </select>
                </div>
            </div>
            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">职称<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-9 col-md-9">
                    <select name="title_type" id="title_type" required class="form-control w-auto float-left">
                        <?=$data=[''=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'初级','636'=>'其他']?>
                        <?=getSelectFromArray($data,array_search($data['title_type'],$data)?:'0',false)?>
                    </select>

                    <select name="title" id="title"  class="form-control w-auto">
                        <?=getOptionByParent(array_search($data['title_type'],$data),$data['title'])?>
                    </select>

                </div>
                <div class="clearfix"></div>
            </div>

            <div class="form-group row center">
                <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">验证码<span class="text-danger">* </span></label>
                <div class="col-xs-12 col-sm-5 col-md-5" style="position: relative">
                    <input type="text" class="form-control" name="safe_code" placeholder="验证码" style="width: 150px;display: inline-block;">
                    <img title="点击切换" src="<?=site_url("common/index")?>" alt="" id="captcha" style="width: 130px;cursor: pointer;margin-top: -10px;" onClick="$('#captcha').attr('src','<?=site_url("common/index/s/")?>' + Math.random());">
                </div>
            </div>

            <div class="form-group text-center">
                <?=Button::setType('submit')->setIcon('fa fa-check-circle')->setSize('btn-lg')->button('注册项目负责人')?>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    $('#subject_code').selectpicker({
        size:10
    });

    $('#validateForm').bootstrapValidator({
        message: '填写内容无效',
        feedbackIcons: {
            valid: 'fa fa-check',
            invalid: 'fa fa-times',
            validating: 'si si-refresh'
        },
        fields: {
			'subject_code': {
                validators: {
                    notEmpty: {
                        message: '所属专科必须选择'
                    }
                }
            },
			'education': {
                validators: {
                    notEmpty: {
                        message: '学历必须选择'
                    }
                }
            },
            'user_name': {
                validators: {
                    notEmpty: {
                        message: '姓名必须填写'
                    }
                }
            },
            'phone': {
                validators: {
                    notEmpty: {
                        message: '座机必须填写'
                    }
                }
            },
			'user_idcard': {
                validators: {
                    notEmpty: {
                        message: '身份证号码必须填写'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("register/researcher/valid")?>',
                        message: '身份证号码不正确',
                        delay: 1000
                    }
                }
            },
			'user_mobile': {
                validators: {
                    notEmpty: {
                        message: '手机号码必须填写'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("register/researcher/valid")?>',
                        message: '手机号码不正确',
                        delay: 1000
                    }
                }
            },
			'user_email': {
                validators: {
                    notEmpty: {
                        message: '电子邮箱必须填写'
                    },
                    emailAddress: {
                        message: '请填写有效的电子邮箱地址'
                    }
                }
            },
            'corporation_name': {
                validators: {
                    notEmpty: {
                        message: '所属单位必须选择'
                    }
                }
            },
            'title_type': {
                validators: {
                    notEmpty: {
                        message: '职称必须选择'
                    }
                }
            },
			'safe_code': {
                validators: {
                    notEmpty: {
                        message: '安全验证码必须填写'
                    },
                    remote: {
                        type: 'POST',
                        url: '<?=site_url("register/account/valid")?>',
                        message: '安全校验码不正确',
                        delay: 1000
                    }
                }
            }
        }
    });
});
</script>
<script>
    //选择职称级别
    $(document).on("change","#title_type",function(){
        var id= $(this).val();
        if(id==0){
            $('#title').html('<option value="0">==请选择==</option>');
            return;
        }
        var url = '<?=site_url('common/HonorAjax')?>';
        ajaxData(url,{id:id},'html',function(){},function(data){
            $('#title').html(data);
        });
    });
</script>