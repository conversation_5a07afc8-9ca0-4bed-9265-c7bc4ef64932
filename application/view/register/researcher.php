<script language="javascript" type="text/javascript">
  function unit(json){
    $('#corporation_id').val(json.id);
    $('#corporation_name').val(json.subject);
    closeWindow();
  }
</script>


<div class="bg-image">
    <div class="row no-gutters justify-content-center bg-black-75">
        <div class="hero-static col-sm-8 col-md-8 col-xl-8 d-flex align-items-center p-2 px-sm-0">
            <div class="block block-transparent block-rounded w-100 mb-0 overflow-hidden">
                <div class="block-content block-content-full px-lg-5 px-xl-6 py-4 py-md-5 py-lg-3 bg-white">
                    <div class="mb-2 text-center">

                        <a class="link-fx text-dark font-w700 font-size-h1" href="<?=site_url("/")?>">首页</a>
                        <a class="link-fx text-primary font-w700 font-size-h1" href="<?=site_url("register/account/type")?>">用户注册</a>
                        <p class="text-uppercase font-w700 font-size-sm text-muted">
                            科研人员激活
                        </p>
                    </div>

                    <form class="form-horizontal" name="validateForm" id="validateForm" enctype="multipart/form-data" action="" method="post">

                        <div class="form-group">
                            <div class="input-group">
                                <label for="personname" class="col-form-label col-md-2">
                                    真实姓名<span class="text-danger">* </span>
                                </label>
                                <input type="text" class="form-control" id="personname" name="personname" value="<?=$declarer->getUserName()?>" readonly title="请填写您的真实姓名">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fa fa-user-circle"></i></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="input-group">
                                <label for="user_idcard" class="col-form-label col-md-2">
                                    身份证号<span class="text-danger">* </span>
                                </label>
                                <input type="text" class="form-control" id="user_idcard" name="user_idcard" value="<?=$declarer->getUserIdCard()?>" readonly title="请填写有效的18位公民身份证号码">
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="input-group">
                                <label for="role_id" class="col-form-label col-md-2">
                                    学历<span class="text-danger">* </span>
                                </label>
                                <select class="custom-select" id="user_degree" name="user_degree">
                                    <option value="">请选择</option>
                                    <?=getSelectFromArray(get_select_data('degree'),$declarer->getUserDegree())?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="input-group">
                                <label for="role_id" class="col-form-label col-md-2">
                                    学位<span class="text-danger">* </span>
                                </label>
                                <select class="custom-select" id="education" name="education">
                                    <option value="">请选择</option>
                                    <?=getSelectFromArray(get_select_data('education'),$declarer->getEducation())?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="input-group">
                                <label for="user_name" class="col-form-label col-md-2">
                                    申报单位<span class="text-danger">* </span>
                                </label>
                                <input type="text" class="form-control" id="corporation_name" name="corporation_name" placeholder="如果查询不到申报单位，请确认单位是否注册。" value="<?=$value['corporation_name']?>" readonly="readonly" >
                                <input class="btn btn-sm btn-primary" class="form-control"  type="button" name="button2" id="button" value="选择单位" onclick="return showWindow('选择单位','<?=site_url("common/unit_search")?>',600,400);" />
                                <input type="hidden" name="corporation_id" id="corporation_id" value="<?=$value['corporation_id']?>" />
<!--                                <div class="input-group-append">-->
<!--                                    <span class="input-group-text"><i class="fa fa-user-circle"></i></span>-->
<!--                                </div>-->
                            </div>
                        </div>

                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-hero-primary"><i class="si si-action-redo mr-1"></i>立即激活身份</button>
                        </div>
                    </form><!-- END Sign Up Form -->
                </div>
            </div>
        </div><!-- END Sign Up Block -->
    </div>
</div><!-- END Page Content -->

<script type="text/javascript">
    $(document).ready(function() {
        $('#validateForm').bootstrapValidator({
            message: '填写内容无效',
            feedbackIcons: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                'user_name': {
                    message: '真实姓名必须填写',
                    validators: {
                        notEmpty: {
                            message: '真实姓名不能为空'
                        }
                    }
                },
                'card_id': {
                    validators: {
                        notEmpty: {
                            message: '身份证号码必须填写'
                        }
                    }
                }
            }
        });
    });

</script>