<div class="header blue btn-group-sm">
<a href="javascript:history.back();" class="btn btn-info" role="button">
  <i class="ace-icon fa fa-arrow-left"></i>返回
</a>
<a href="#" class="btn btn-primary" onclick="return showWindow('新增表单','<?=site_url('form/getAdd')?>',{area:['60%','100%'],shadeClose:false})" role="button"><i class="ace-icon fa fa-plus"></i>新增表单</a>

 <!--  <div class="pull-right">
    <a href="<?=site_url('form/getFieldsBlock')?>" class="btn btn-info btn-sm" role="button">
      <i class="ace-icon fa fa-book"></i>字段对照表
    </a>
  </div> -->
</div>

<div class="search">
  <form id="form1" name="form1" method="post" action="">
    <label>搜索关键词语
      <input type="text" name="search" id="search" />
    </label>
    <label>
      <input name="field" type="radio" id="radio" value="subject" checked="checked" />
      表单名称
    </label>
<input type="submit" name="button" id="button" value="提交" class="btn btn-sm btn-primary"/>
  </form>
</div>

<div class="col-xs-12 col-sm-12 col-md-12 no-padding">
<div class="panel panel-default">
<div class="panel-heading">
    <i class="ace-icon fa fa-list"></i> 表单列表
    <div class="pull-right hidden-480 tools">
      <div class="dropdown dropdown-hover inline">
        <a href="#" style="">项目排序<i class="ace-icon fa fa-caret-down"></i></a>
        <ul class="dropdown-menu dropdown-menu-right no-margin">
          <li><?=getColumnStr('ID','id')?></li>
          <li><?=getColumnStr('类型名称','subject')?></li>
        </ul>
      </div>
      |
      <div class="inline fontchange" title="字号大小调节">
        <a class="lighter" href="#" size="16">大</a>
      </div>
      <div class="inline fontchange active" title="字号大小调节">
        <a class="lighter" href="#" size="13">小</a>
      </div>
    </div>
  </div>
<table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
<thead>
    <tr>
      <th class="hidden-480"><input name="selectAll" id="selectAll" type="checkbox" /></th>
      <th class="hidden-480">ID</th>
      <th>名称</th>
      <th class="hidden-480">管理</th>
    </tr>
  </thead>
  <tbody>
    <?php foreach($forms as $form):?>
    <tr>
      <td align="center" class="hidden-480" width="50"><input name="select_id[]"  type="checkbox" value="<?=$form->id?>" /></td>
      <td align="center" class="hidden-480" width="80"><?=$form->id?></td>
      <td><?=$form->subject?>( <a target="_blank" href="<?=site_url('form/getPreview/form_id/'.$form->id)?>" >表单预览</a> )</td>
      <td align="center" class="hidden-480" width="80" style="position:relative;">
        <a href="javascript:void(0);" class="btn-detail-toggle"><i class="ace-icon fa fa-edit"></i> 编辑<i class="ace-icon fa fa-caret-down blue"></i></a>
        <div class="btn-detail-content hide">
          <a href="#" onclick="return showWindow('编辑','<?=site_url('form/getEdit/form_id/'.$form->id)?>',{area:['60%','100%'],shadeClose:false})"><i class="ace-icon fa fa-edit"></i> 编辑</a>
          <a href="#" onclick="return showWindow('表单字段配置-<?=$form->subject?>','<?=site_url('form/getConfigure/form_id/'.$form->id)?>',{area:['100%','100%'],shadeClose:false})"><i class="ace-icon fa fa-cog"></i> 配置</a>
          <a href="#" onclick="return showWindow('拷贝表单','<?=site_url('form/getCopy/form_id/'.$form->id)?>',{area:['60%','100%'],shadeClose:false})"><i class="ace-icon fa fa-copy"></i> 拷贝</a>
        </div>
      </td>
    </tr>
    <?php endforeach; ?>
  </tbody>
  <tfoot>
    <?php if($hasmore):?>
    <tr>
      <td colspan="12" align="center"><button class="btn btn-sm btn-block btn-primary" onclick="return loadMore(this,'<?=site_url('form/getLoadmore')?>',3)">点击加载更多...</button></td>
    </tr>
    <?php endif;?>
  </tfoot>
</table>
</div>
</div>