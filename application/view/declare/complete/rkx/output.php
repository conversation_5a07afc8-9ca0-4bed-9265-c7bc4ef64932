<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>验收书打印</title>
<style type="text/css">
<!--
.STYLE4 {font-size: 22pt;
font-family:"黑体";
}
.STYLE5 {
	font-size: 13.5pt;
	font-family:"黑体";
}
.STYLE6 {font-size: 18pt}
-->
</style>
<style>   
  table{border-collapse:collapse;border:solid   black;}   
  td{border: 1px solid   black}   
  </style>   
<style>
.gh{
	position:absolute;
	left:20px;
	right:20px;
	visibility: visible;
}
.line{
	border-left-width:0px;
	border-right-width:0px;
	border-top-width:0px; 
	border-bottom-width:1px;
}
</style>

<style> 
.p{
text-indent:2em;
font-size:10.5pt;
 line-height:40px;
 } 
</style>
<style> 
.p2{
text-indent:2em;
font-size:12pt;
 line-height:36px;
 } 
</style>
<style> 
.p1{
 line-height:16px;
 font-size:12pt;
 } 
</style>
<style> 
.middle-demo-2{
padding-top: 24px;
padding-bottom: 24px;
} 
</style>
<style>
.def{
	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.5;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def1{
	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:0px;
	border-bottom-color:#FFFFFF;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.abc{border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
font-family: "宋体";
line-height: 1.8; }
.abc1 {
	border-left-width:1px;
	border-top-width:1px;
	border-right-width:1px;
	border-bottom-width:0px;
	border-bottom-color:#CCCCCC;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #000000;
	border-right-color: #000000;
	border-left-color: #000000;
}
.abc2{
	border-left-width:1px;
	border-top-width:0px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #000000;
	border-left-color: #000000;
}
.abc3{
	border-left-width:1px;
	border-top-width:0px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #000000;
	border-left-color: #000000;
}
.STYLE8 {
	color: #000000;
	font-size: 13.5pt;
	font-family:"黑体";
}
.bm{
	text-decoration: underline;
}
.STYLE10 {font-size: 13.5pt}
.STYLE11 {font-size: 12pt}
.abc4 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.abc5 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.btn {
 BORDER-RIGHT: #7b9ebd 1px solid; PADDING-RIGHT: 2px; BORDER-TOP: #7b9ebd 1px solid; PADDING-LEFT: 2px; FONT-SIZE: 12px; FILTER: progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr=#ffffff, EndColorStr=#cecfde); BORDER-LEFT: #7b9ebd 1px solid; CURSOR: hand; COLOR: black; PADDING-TOP: 2px; BORDER-BOTTOM: #7b9ebd 1px solid
}
.abc6 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.abc7 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.STYLE12 {color: #000000}
.abc8 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
font-family: "宋体";
line-height: 1.8; }
</style>
<script language="javascript">
function getObject(id)
{	if(document.getElementById)
	{	return document.getElementById(id);}
	else if(document.all)
	{	return document.all[id];}
	else if(document.layers)
	{	return document.layers[id];}
}
function shuoming(){
	getObject('OutText').innerHTML = '';
	OpenWord();
}
function OpenWord()
{  
	abc.style.border=0;
	ExcelSheet = new ActiveXObject('Word.Application');
	ExcelSheet.Application.Visible = true;
	var mydoc = ExcelSheet.Documents.Add('',0,1);
	myRange = mydoc.Range(0,1);
	var sel = abc.document.body.createTextRange();
	sel.select();
	abc.document.execCommand('Copy');
	sel.moveEnd('character');
	myRange.Paste();
	// location.reload();
	ExcelSheet.ActiveWindow.ActivePane.View.Type=9;
}
</script>
</head>
<body>

<?php if($download == ''):?>
<span id="OutText" align="center">
<div align="left" class="Noprint" id="abc">
<input type="button" class="btn" align="left" value="导出为word文档打印" onclick="shuoming();"  >
(要是WORD导出不成功，<a href="<?=site_url("declare/complete/download/id/".$project->getProjectId())?>">请点击这里下载该word文档</a>)
</div>
<br>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td height="30" align="center"><strong>打印说明</strong></td>
  </tr>
  <tr>
    <td><br />　
    　<br />　
    　为了您能顺利将在线填写的申报书导出为Word文档打印，请在点击“导出为Word文档时行打印，在”按钮前，请注意如下事项：
      <p>　　1、至少使用IE5.5以上的浏览器(Windows 2000以后操作系统(包括Windeow XP)都能正常执行)，本系统打印解决方案目前只支持微软IE浏览器；其次在您的计算机上必须至少装有Office xp或Office 2003办公套件。</p>
      <p>　　2、导出前请确认您的浏览器允许使用ActiveX控件，设置方法如下：IE浏览器-->工具栏-->internet选项-->“安全”页-->点击“自定义级别”按钮-->ActiveX控件和插件-->启用“对没有标记为安全的ActiveX控件进行初使化和脚本运行”。</p>
      <p>　　3、点击“导出为word文档打印”按钮后，系统会自动打开您的Word文字处理软件并在其中显示您的项目。为了尽可能地方便您的阅读和格式编辑，我们强烈建议阁下切换到页面视图模式下（单击“视图"菜单-->在下拉菜单选择"页面"即可)， 即将整个文档调整为传统的word文档显示格式。在您保存所编辑好的文档时，请选择：”文件”菜单-->“另存为”,此时会弹出对话框询问您如何保存您所编辑好的Word文档，此时可为您的项目文档取一个适当的名称(如“我的2008XX类型项目申报书(打印终稿))；然后在其下方的保存类型的选择框中选择“word文档（*.doc）”，点击“保存” 就可将您的项目申报书保存到您所指定的位置(注意：这是必须选择的，否则您所保存的文档将会失去在编辑时所拥有的文档格式！)。</p>
	  <p>　　4、为了统一格式和坚持一致性原则，请各项目负责人在导出word文档后一定不要修改文档的内容(必须保持与管理系统中所填写申报书内容高度一致)，除开封面页的格式其它页面的格式都可以本着美观、大方、易读的原则进行格式上的微调；否则，由此带来的不便由项目负责人自行负责。</p>
	  <p align="right">四川省临床重点专科建设项目管理中心<br /><br /></p>
    </td>
  </tr>
</table>
</span>
<?php endif;?>
<p align="center"><span class="STYLE4">德阳市软科学计划项目<br />验收自评价报告</span></p>
<p align="center">&nbsp;</p>
<table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def1">
<tr>
<td rowspan="9" align="right" class="def1"  width="3%" ></td>
<td width="20%" height="60" align="right"  valign="bottom" class="def1" ><span class="STYLE8">项目编号：</span></td>
<td class="def"  align="left" valign="bottom" width="74%" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$project->getRadicateId()?></span></td>
<td rowspan="9" align="right" class="def1"  width="3%" ></td>
</tr>
<tr>
<td class="def1" align="right" height="60" valign="bottom"><span class="STYLE8">项目名称：</span></td>
<td class="def" align="left" valign="bottom" style="margin-bottom:0px;margin-top:20px;letter-spacing: 0px;"><span class="STYLE11"><?=$project->getSubject()?></span></td>
</tr>
<tr>
<td class="def1" align="right" height="60" valign="bottom"><span class="STYLE8">承担单位：</span></td>
<td class="def" align="left" valign="bottom" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$project->getCorporationName()?>&nbsp;&nbsp;&nbsp;&nbsp;（盖章）</span></td>
</tr>
<tr>
<td class="def1" align="right" height="60" valign="bottom"><span class="STYLE8">负 责 人：</span></td>
<td class="def" align="center" valign="bottom" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">&nbsp;&nbsp;（签字）</span></td>
</tr>
<tr>
<td class="def1" align="right" height="60" valign="bottom" ><span class="STYLE8">联系电话：</span></td>
<td class="def"  valign="bottom" align="left" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$project->getUser()->getUserMobile()?></span></td>
</tr>
<tr>
<td class="def1" align="right" height="60" valign="bottom" ><span class="STYLE8">归口部门：</span></td>
<td class="def"  valign="bottom" align="left" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$project->getDepartmentName()?></span></td>
</tr>
<tr>
<td  height="60" class="def1" align="right" valign="bottom" ><span class="STYLE8">立项经费：</span></td>
<td class="def" align="left" valign="bottom" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$project->getRadicateMoney()?> （万元）</span></td>
</tr>
<tr>
<td  height="60" class="def1" align="right" valign="bottom" ><span class="STYLE8">起止年限：</span></td>
<td class="def" align="left" valign="bottom" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$project->getStartAt()?>至<?=$project->getEndAt()?></span></td>
</tr>
</table>
<p align="center" class="STYLE10">&nbsp;</p>
<p align="center">&nbsp;</p>
<p align="center"><span  class="STYLE5">四川省卫生健康委员会制</span><br><span  class="STYLE5">二0&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;月</span></p>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span>

<p align=center style='margin:10px;font-size:18.0pt; font-weight:bold;'>德阳市软科学研究计划项目验收申请表</p>
<table width=680 border=1 align="center" cellpadding=0 cellspacing=0>
 <tr style='height:30.4pt'>
  <td width=115 height="26" align="center" style='font-size:12.0pt;'>项目名称</td>
  <td width=469 colspan=4>&nbsp;<?=$project->getSubject()?></td>
 </tr>
 <tr style='height:30.35pt'>
  <td width=115 height="27" align="center" style='font-size:12.0pt;'>项目编号</td>
  <td width=469 colspan=4>&nbsp;<?=$project->getRadicateId()?></td>
 </tr>
 <tr style='height:30.35pt'>
  <td width=115 height="29" align="center" style='font-size:12.0pt;'>起始时间</td>
  <td width=204 colspan=2>&nbsp;<?=$project->getStartAt()?>     年</td>
  <td width=96 align="center" style='font-size:12.0pt;'>终止时间</td>
  <td width=169>&nbsp;<?=$project->getEndAt()?> 年 </td>
 </tr>
 <tr style='height:30.4pt'>
  <td width=115 height="30" align="center" style='font-size:12.0pt;'>项目完成单位</td>
  <td width=469 colspan=4>&nbsp;<?=$project->getCorporationName()?></td>
 </tr>
 <tr style='height:31.2pt'>
  <td width=115 rowspan=2 align="center" style='font-size:12.0pt;'>申请验收时间</td>
  <td width=203 rowspan=2>&nbsp;&nbsp;<?=$complete_book->getApplyYear()?>&nbsp;&nbsp;年&nbsp;<?=$complete_book->getApplyMonth()?>&nbsp; 月</td>
  <td width=97 height="28" colspan=2 align="center" style='font-size:12.0pt;'>项目负责人</td>
  <td width=169 align="right">（签字）&nbsp;&nbsp;</td>
 </tr>
 <tr style='height:30.95pt'>
  <td width=97 height="29" colspan=2 align="center" style='font-size:12.0pt;'>联系电话</td>
  <td width=169>&nbsp;<?=$complete_book->getLinkmanTel()?></td>
 </tr>
 <tr style='height:160.7pt'>
  <td width=115 height="123" align="center" style='font-size:12.0pt;'>提供验收的<br>文件清单</td>
  <td width=469 colspan=4 valign=top><?=nl2br($complete_book->getFileList())?></td>
 </tr>
 <tr style='height:116.8pt'>
  <td width=115 height="159" align="center" style='font-size:12.0pt;'>申请验收<br>单位意见</td>
  <td width=469 colspan=4 valign="bottom" align="right">（盖章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
                                    年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
 </tr>
 <tr style='height:116.8pt'>
  <td width=115 height="147" align="center">省科技厅<br>意&nbsp;&nbsp;&nbsp;&nbsp;见</td>
  <td width=469 colspan=4 valign="bottom" align="right">（盖章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
                                    年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
 </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
    <td width="568" height="36"><p align="center"><strong>一、报告正文</strong></p></td>
  </tr>
  <tr>
    <td width="568" height="830" valign="top"><?=$complete_book->getGroupAttitude()?></td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
    <td width="568" height="36"><p align="center"><strong>二、项目研究成果信息简报(1000字以内)</strong></p></td>
  </tr>
  <tr>
    <td width="568" height="830" valign="top"><?=$project->getComplateBook()->getCompleteRkx()->getCgjb()?></td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<?php $outlay = $complete_book->getOutlay();?>
<table width="680" border="1" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td colspan="6" height="38" valign="top"><span align="left"><strong style="font-size:14px;">三、经费决算表。</strong>（<span class="STYLE12">单位：万元</span>） </span> </td>
  </tr>
  <tr style='height:22.6pt'>
    <td colspan="3" align="center"><strong>收&nbsp;&nbsp;&nbsp;入</strong></td>
    <td colspan="3" align="center"><strong>支&nbsp;&nbsp;&nbsp;出</strong></td>
  </tr>
  <tr style='height:30.95pt'>
    <td width="188" align="center">科目</td>
    <td width="69" align="center">预算数<br />
    （万元）</td>
    <td width="70" align="center">实际数<br />
    （万元）</td>
    <td width="208" align="center">科目</td>
    <td width="67" align="center">金额<br />
    （万元）</td>
    <td width="58" align="center">其中省拨资金</td>
  </tr>
  <tr style='height:22.15pt'>
    <td>1、省科技厅拨款</td>
    <td><?=$outlay[0][0]?></td>
    <td><?=$outlay[1][0]?></td>
    <td>1、设备费</td>
    <td><?=$outlay[2][0]?></td>
    <td><?=$outlay[3][0]?></td>
  </tr>
  <tr style='height:23.15pt'>
    <td>2、自筹</td>
    <td><?=$outlay[0][1]?></td>
    <td><?=$outlay[1][1]?></td>
    <td>（1）购置设备费</td>
    <td><?=$outlay[2][1]?></td>
    <td><?=$outlay[3][1]?></td>
  </tr>
  <tr style='height:22.7pt'>
    <td>（1）国家部委拨款</td>
    <td><?=$outlay[0][2]?></td>
    <td><?=$outlay[1][2]?></td>
    <td>（2）设备改造与租赁费</td>
    <td><?=$outlay[2][3]?></td>
    <td><?=$outlay[3][3]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（2）国家其他拨款</td>
    <td><?=$outlay[0][3]?></td>
    <td><?=$outlay[1][3]?></td>
    <td>2、资料费</td>
    <td><?=$outlay[2][4]?></td>
    <td><?=$outlay[3][4]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（3）行业主管部门拨款</td>
    <td><?=$outlay[0][4]?></td>
    <td><?=$outlay[1][4]?></td>
    <td>3、数据采集费</td>
    <td><?=$outlay[2][5]?></td>
    <td><?=$outlay[3][5]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（4）市州县财政拨款</td>
    <td><?=$outlay[0][5]?></td>
    <td><?=$outlay[1][5]?></td>
    <td>4、差旅费</td>
    <td><?=$outlay[2][7]?></td>
    <td><?=$outlay[3][7]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（5）银行贷款</td>
    <td><?=$outlay[0][6]?></td>
    <td><?=$outlay[1][6]?></td>
    <td>5、会议费</td>
    <td><?=$outlay[2][8]?></td>
    <td><?=$outlay[3][8]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（6）单位自有资金</td>
    <td><?=$outlay[0][7]?></td>
    <td><?=$outlay[1][7]?></td>
    <td>6、国际合作与交流费</td>
    <td><?=$outlay[2][9]?></td>
    <td><?=$outlay[3][9]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（7）其他来源</td>
    <td><?=$outlay[0][8]?></td>
    <td><?=$outlay[1][8]?></td>
    <td>7、专家咨询费</td>
    <td><?=$outlay[2][12]?></td>
    <td><?=$outlay[3][12]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>8、劳务费</td>
    <td><?=$outlay[2][11]?></td>
    <td><?=$outlay[3][11]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>9、印刷费</td>
    <td><?=$outlay[2][10]?></td>
    <td><?=$outlay[3][10]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>10、管理费</td>
    <td><?=$outlay[2][13]?></td>
    <td><?=$outlay[3][13]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>11、其他费用</td>
    <td><?=$outlay[2][14]?></td>
    <td><?=$outlay[3][14]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>经费来源合计</td>
    <td><?=$outlay[0][15]?></td>
    <td><?=$outlay[1][15]?></td>
    <td>经费实际支出合计</td>
    <td><?=$outlay[2][15]?></td>
    <td><?=$outlay[3][15]?></td>
  </tr>
  <tr style='height:77.55pt'>
    <td height="96" colspan="6" align="right" valign="bottom"><p style="text-align:left; margin:4px;">项目负责人（签字）：&nbsp;&nbsp;&nbsp;&nbsp;
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;承担单位责任人（签字）：</p>
        <p style="text-align:left;margin:4px;">单位财务负责人（签字）：</p>
      年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br />
      （单位公章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
  </tr>
  <tr style='height:60.85pt'>
    <td height="89" align="center">财务验收意见</td>
    <td colspan="5" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;财务验收专家组长（签字）：&nbsp;<br />      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  </tr>
  <tr style='height:60.85pt'>
    <td height="92" align="center">条件财务处意见</td>
    <td colspan="5" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;条件财务处负责人（签字）：<br />      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />
<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" colspan="7" align="center"><strong>四、主 要 研 究 人 员 名 单</strong></td>
  </tr>
  <tr style='height:25pt'>
  <td width="12%" align="center">姓  名</td>
  <td width="8%" align="center">年龄</td>
  <td width="12%" align="center">文化程度</td>
  <td width="12%" align="center">所学专业</td>
  <td width="16%" align="center">职称职务</td>
  <td width="16%" align="center">工作单位</td>
  <td width="24%" align="center">对成果的创造性贡献</td>
 </tr>
<?php 
$team = $project->getComplateBook()->getCompleteRkx()->getTeam();
for($i=1;$i<13;$i++):?>
	  	<tr>
	  <td class="borders" height="29" align="center"><span class="tableborder"><?=$team[$i][0]?></span></td>
	  <td class="borders" height="29" align="center"><span class="tableborder"><?=$team[$i][1]?></span></td>
	  <td class="borders" height="29" align="center"><span class="tableborder"><?=$team[$i][2]?></span></td>
	  <td class="borders" align="center"><?=$team[$i][3]?></td>
	  <td class="borders" align="center"><span class="tableborder"><?=$team[$i][5]?></span></td>
	  <td class="borders" align="center"><span class="tableborder"><?=$team[$i][6]?></span></td>
	  <td class="borders" align="center"><?=$team[$i][7]?></td>
	  	</tr>  
		<?php endfor;?>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="0" cellspacing="1"  style="border:none" cellpadding="3" width="680"   align="center" class="abc">
   <tr style='height:25.4pt'>
  <td colspan=9 style='font-size:12.0pt; font-weight:bold' align="center">五、德阳市软科学研究计划项目验收信息表</td>
   </tr>
 <tr style='height:24.2pt'>
  <td colspan=2 align="center">项目名称</td>
  <td colspan=7 >&nbsp;
    <?=$project->getSubject()?></td>
 </tr>
 <tr style='height:24.2pt'>
  <td colspan=2 align="center">项目编号</td>
  <td colspan=7>&nbsp;
    <?=$project->getRadicateId()?></td>
 </tr>
 <tr style='height:24.2pt'>
  <td colspan=2 align="center">承担单位</td>
  <td colspan=7>&nbsp;
    <?=$project->getCorporationName()?></td>
 </tr>
 <tr style='height:24.5pt'>
  <td width=32 rowspan=3 align="center">课题<br style="line-height:0.5">    负责人</td>
  <td width=62 align="center">姓&nbsp;&nbsp;&nbsp;名</td>
  <td align="center">&nbsp;
    <?=$project->getUser()->getPersonname()?></td>
  <td width="65" align="center">学历</td>
  <td width="85" align="center">&nbsp;
    <?=$project->getUser()->getUserDegree()?></td>
  <td width="65" align="center">职称</td>
  <td width="77" align="center">&nbsp;
    <?=$project->getUser()->getUserHonor()?></td>
  <td width="64" align="center">联系电话</td>
  <td width="88" align="center">&nbsp;
    <?=$project->getUser()->getUserPhone()?></td>
 </tr>
 <tr style='height:24.5pt'>
  <td width=62 align="center">工作单位</td>
  <td colspan=3>&nbsp;
    <?=$project->getCorporationName()?></td>
  <td colspan=1 align="center">E-mail</td>
  <td colspan=3 align="center">&nbsp;
    <?=$project->getUser()->getUserEmail()?></td>
 </tr>
 <tr style='height:24.5pt'>
  <td align="center">通讯地址</td>
  <td colspan=7 >&nbsp;
    <?=$project->getUser()->getCorporations()->getAddress()?></td>
 </tr>
 <tr style='height:24.5pt'>
  <td colspan=2 align="center">完成情况</td>
  <td colspan=7>&nbsp;<?=$complete_book->getFinishState()?></td>
 </tr>
 <tr style='height:24.6pt'>
  <td colspan=2 align="center">主持验收部门</td>
  <td colspan=7>&nbsp;
    <?=$complete_book->getDepartment()?></td>
 </tr>
 <tr style='height:24.7pt'>
  <td colspan=2 rowspan=2 align="center">验收专家组<br>组&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;长</td>
  <td align="center">姓&nbsp;&nbsp;名</td>
  <td colspan="2" align="center"><?=$complete_book->getHeadName()?></td>
  <td align="center">职&nbsp; 称</td>
  <td colspan="3" align="center"><?=$complete_book->getHeadTitle()?></td>
  </tr>
 <tr style='height:24.7pt'>
  <td align="center">工作单位</td>
  <td colspan="6" align="left"><?=$complete_book->getHeadWorkunit()?></td>
  </tr>
 <tr style='height:24.7pt'>
  <td colspan=2 rowspan=3 align="center">实际参加<br>研究人员</td>
  <td width=78 align="center">总计</td>
  <td colspan=6 align="center">&nbsp;
    <?=$complete_book->getGroupNum(1)?>
    人</td>
 </tr>
 <tr style='height:24.7pt'>
  <td align="center">其中</td>
  <td align="center">高级职称</td>
  <td align="center">&nbsp;
    <?=$complete_book->getGroupNum(2)?>
    人</td>
  <td align="center">中级职称</td>
  <td >&nbsp;
    <?=$complete_book->getGroupNum(3)?>
    人</td>
  <td align="center">初级职称</td>
  <td >&nbsp;
    <?=$complete_book->getGroupNum(4)?>
    人</td>
 </tr>
 <tr style='height:24.7pt'>
  <td align="center">博&nbsp;&nbsp;&nbsp;士</td>
  <td align="center">&nbsp;
    <?=$complete_book->getGroupNum(5)?>
    人</td>
  <td align="center">硕&nbsp;&nbsp;&nbsp;士</td>
  <td >&nbsp;
    <?=$complete_book->getGroupNum(6)?>
    人</td>
  <td colspan="2" align="center">合 作 方</td>
  <td>&nbsp;
    <?=$complete_book->getGroupNum(7)?>
    人</td>
 </tr>
 <tr style='height:24.5pt'>
  <td colspan=2 rowspan=3 align="center">主要成果</td>
  <td colspan=7>研究报告、论文<?=$complete_book->getFruitState(7)?> 篇。其中:国内发表<?=$complete_book->getFruitState(8)?>      篇,在国际上发表<?=$complete_book->getFruitState(9)?> 篇。  </td>
 </tr>
 <tr style='height:24.5pt'>
  <td colspan=7>培养博士后&nbsp;<?=$complete_book->getFruitState(16)?>    &nbsp;名， 培养博士  
    <?=$complete_book->getFruitState(17)?>&nbsp;&nbsp;名，
   培养硕士&nbsp;
   <?=$complete_book->getFruitState(18)?>   &nbsp;&nbsp;名</td>
 </tr>
 <tr style='height:24.5pt'>
  <td colspan=7>获奖&nbsp;
    <?=$complete_book->getFruitState(19)?>    &nbsp;&nbsp;项。&nbsp;&nbsp;&nbsp;其中:省部级&nbsp;
    <?=$complete_book->getFruitState(20)?>    &nbsp;项，国家级&nbsp;
    <?=$complete_book->getFruitState(21)?>    &nbsp;&nbsp;项</td>
 </tr>
 <tr style='height:24.7pt'>
  <td colspan=2 align="center">应用情况</td>
  <td colspan=2>成果被采纳数</td>
  <td colspan=5 align="right"><?=$complete_book->getAppState(1)?>&nbsp;项</td>
 </tr>
 <tr style='height:24.7pt'>
  <td colspan=2 rowspan=3>直接经济效益</td>
  <td colspan=2>新增产值</td>
  <td colspan=5 align="right"><?=$complete_book->getEconomyState(1)?>    &nbsp;万元</td>
 </tr>
 <tr style='height:24.7pt'>
  <td colspan=2>新增利税</td>
  <td colspan=5 align="right"><?=$complete_book->getEconomyState(2)?>    &nbsp;万元</td>
 </tr>
 <tr style='height:24.7pt'>
  <td colspan=2>出口创汇</td>
  <td colspan=5 align="right"><?=$complete_book->getEconomyState(3)?>    &nbsp;万美元</td>
 </tr>
 <tr style='height:80.7pt'>
  <td align="center" colspan=2>验 收<br>意 见</td>
  <td colspan=7 valign="bottom"><br><p style="margin-top:6px; margin-bottom:4px;">&nbsp;&nbsp;&nbsp;&nbsp;□1.已通过验收&nbsp;&nbsp;&nbsp;&nbsp;□2.未通过验收&nbsp;&nbsp;&nbsp;&nbsp;□3.结题</p><br>
    <p style="margin-top:4px; margin-bottom:4px;">&nbsp;&nbsp;&nbsp;&nbsp;四川省卫生健康委员会</p>
	                                                  负责人（签字）：
        <p style="margin-top:0px; margin-bottom:4px;text-align:right;">（公章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;</p></td>
 </tr>
</table>
</body>
</html>