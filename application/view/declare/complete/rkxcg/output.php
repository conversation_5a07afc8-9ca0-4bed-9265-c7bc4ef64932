<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>验收表打印</title>

<style type="text/css">
<!--
.STYLE4 {font-size: 24pt;
font-family:"黑体";
}
.STYLE5 {
	font-size: 13.5pt;
	font-family:"黑体";
}
.STYLE6 {font-size: 18pt}
-->
</style>
<style>   
  table{border-collapse:collapse;border:solid   black;}   
  td{border: 1px solid   black}   
  </style>   
<style>
.gh{
	position:absolute;
	left:20px;
	right:20px;
	visibility: visible;
}
.line{
	border-left-width:0px;
	border-right-width:0px;
	border-top-width:0px; 
	border-bottom-width:1px;
}
</style>

<style> 
.p{
text-indent:2em;
font-size:10.5pt;
 line-height:40px;
 } 
</style>
<style> 
.p2{
text-indent:2em;
font-size:12pt;
 line-height:36px;
 } 
</style>
<style> 
.p1{
 line-height:16px;
 font-size:12pt;
 } 
</style>
<style> 
.middle-demo-2{
padding-top: 24px;
padding-bottom: 24px;
} 
</style>
<style>
.def{
	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.5;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def1{
	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:0px;
	border-bottom-color:#FFFFFF;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.abc{border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
font-family: "宋体";
line-height: 1.8; }
.abc1 {
	border-left-width:1px;
	border-top-width:1px;
	border-right-width:1px;
	border-bottom-width:0px;
	border-bottom-color:#CCCCCC;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #000000;
	border-right-color: #000000;
	border-left-color: #000000;
}
.abc2{
	border-left-width:1px;
	border-top-width:0px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #000000;
	border-left-color: #000000;
}
.abc3{
	border-left-width:1px;
	border-top-width:0px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #000000;
	border-left-color: #000000;
}
.STYLE8 {
	color: #000000;
	font-size: 14pt;
	font-family:"黑体";
}
.bm{
	text-decoration: underline;
}
.STYLE10 {font-size: 13.5pt}
.STYLE11 {font-size: 14pt}
.abc4 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.abc5 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.btn {
 BORDER-RIGHT: #7b9ebd 1px solid; PADDING-RIGHT: 2px; BORDER-TOP: #7b9ebd 1px solid; PADDING-LEFT: 2px; FONT-SIZE: 12px; FILTER: progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr=#ffffff, EndColorStr=#cecfde); BORDER-LEFT: #7b9ebd 1px solid; CURSOR: hand; COLOR: black; PADDING-TOP: 2px; BORDER-BOTTOM: #7b9ebd 1px solid
}
.abc6 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.abc7 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 10.5pt;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.STYLE111 {font-size: 12pt}
.STYLE81 {	color: #000000;
	font-size: 13.5pt;
	font-family:"黑体";
}
.def2 {	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.5;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def11 {	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:0px;
	border-bottom-color:#FFFFFF;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 10.5pt;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
</style>
<script language="javascript">
function getObject(id)
{	if(document.getElementById)
	{	return document.getElementById(id);}
	else if(document.all)
	{	return document.all[id];}
	else if(document.layers)
	{	return document.layers[id];}
}
function shuoming(){
	getObject('OutText').innerHTML = '';
	OpenWord();
}
function OpenWord()
{  
	abc.style.border=0;
	ExcelSheet = new ActiveXObject('Word.Application');
	ExcelSheet.Application.Visible = true;
	var mydoc = ExcelSheet.Documents.Add('',0,1);
	myRange = mydoc.Range(0,1);
	var sel = abc.document.body.createTextRange();
	sel.select();
	abc.document.execCommand('Copy');
	sel.moveEnd('character');
	myRange.Paste();
	// location.reload();
	ExcelSheet.ActiveWindow.ActivePane.View.Type=9;
}
</script>
</head>
<body>
<?php if($download == ''):?>
<span id="OutText" align="center">
<div align="left" class="Noprint" id="abc">
<input type="button" class="btn" align="left" value="导出为word文档打印" onclick="shuoming();"  >
(要是WORD导出不成功，<a href="<?=site_url("declare/completex/download/id/".$project->getProjectId())?>">请点击这里下载该word文档</a>)
</div>
<br>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td height="30" align="center"><strong>打印说明</strong></td>
  </tr>
  <tr>
    <td><br />　
    　<br />　
    　为了您能顺利将在线填写的申报书导出为Word文档打印，请在点击“导出为Word文档时行打印，在”按钮前，请注意如下事项：
      <p>　　1、至少使用IE5.5以上的浏览器(Windows 2000以后操作系统(包括Windeow XP)都能正常执行)，本系统打印解决方案目前只支持微软IE浏览器；其次在您的计算机上必须至少装有Office xp或Office 2003办公套件。</p>
      <p>　　2、导出前请确认您的浏览器允许使用ActiveX控件，设置方法如下：IE浏览器-->工具栏-->internet选项-->“安全”页-->点击“自定义级别”按钮-->ActiveX控件和插件-->启用“对没有标记为安全的ActiveX控件进行初使化和脚本运行”。</p>
      <p>　　3、点击“导出为word文档打印”按钮后，系统会自动打开您的Word文字处理软件并在其中显示您的项目。为了尽可能地方便您的阅读和格式编辑，我们强烈建议阁下切换到页面视图模式下（单击“视图"菜单-->在下拉菜单选择"页面"即可)， 即将整个文档调整为传统的word文档显示格式。在您保存所编辑好的文档时，请选择：”文件”菜单-->“另存为”,此时会弹出对话框询问您如何保存您所编辑好的Word文档，此时可为您的项目文档取一个适当的名称(如“我的2008XX类型项目申报书(打印终稿))；然后在其下方的保存类型的选择框中选择“word文档（*.doc）”，点击“保存” 就可将您的项目申报书保存到您所指定的位置(注意：这是必须选择的，否则您所保存的文档将会失去在编辑时所拥有的文档格式！)。</p>
	  <p>　　4、为了统一格式和坚持一致性原则，请各项目负责人在导出word文档后一定不要修改文档的内容(必须保持与管理系统中所填写申报书内容高度一致)，除开封面页的格式其它页面的格式都可以本着美观、大方、易读的原则进行格式上的微调；否则，由此带来的不便由项目负责人自行负责。</p>
	  <p align="right">四川省临床重点专科建设项目管理中心<br /><br /></p>
    </td>
  </tr>
</table>
</span>
<?php endif;?>
<p align=center style='margin:0cm;font-size:18.0pt; font-weight:bold;'>软科学研究成果评审申请书</p>
<table width=680 border=1 align="center" cellpadding=0 cellspacing=0>
 <tr style='height:30.4pt'>
  <td width=112 align="center" style='font-size:12.0pt;'>成果名称</td>
  <td width=532 colspan=5>&nbsp;<?=$project->getSubject()?></td>
 </tr>
 <tr style='height:84.85pt'>
  <td width=112 align="center" style='font-size:12.0pt;'>项目完成单位</td>
  <td width=532 colspan=5 align="center" valign="middle"><p style="margin-bottom:5px;margin-top:20px;"><?=$project->getCorporationName()?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
    <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;（盖章）</p>
	<p style="margin-bottom:10px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日</p>
    </td>
 </tr>
 <tr style='height:27.2pt'>
  <td width=112 align="center" style='font-size:12.0pt;'>负责人</td>
  <td width=91 align="center">&nbsp;<?=$project->getUserName()?></td>
  <td width=112 align="center" style='font-size:12.0pt;'>申请评审时间</td>
  <td width=105 align="center">&nbsp;<?=$complete_book->getApplyYear()?>&nbsp;&nbsp;年&nbsp;<?=$complete_book->getApplyMonth()?>&nbsp; 月</td>
  <td width=112 align="center" style='font-size:12.0pt;'>申请评审形式</td>
  <td width=112 align="center">&nbsp;<?=$complete_book->getApplyStyle()?></td>
 </tr>
 <tr>
  <td width=112 align="center" style='font-size:12.0pt;' rowspan="3">联系人</td>
  <td width=91 align="center" rowspan="3">&nbsp;<?=$complete_book->getLinkMan()?></td>
  <td width=112 align="center" style='font-size:12.0pt;'>电  话</td>
  <td width=105 align="center">&nbsp;<?=$complete_book->getLinkmanTel()?></td>
  <td width=112 align="center" style='font-size:12.0pt;'>传  真</td>
  <td width=112 align="center">&nbsp;<?=$complete_book->getLinkmanFax()?></td>
 </tr>
 <tr>
  <td width=112 align="center" style='font-size:12.0pt;'>Email</td>
  <td width=329 align="center" colspan="3">&nbsp;<?=$complete_book->getLinkmanEmail()?></td>
 </tr>
 <tr>
  <td width=112 align="center" style='font-size:12.0pt;'>地   址</td>
  <td width=329 align="center" colspan="3">&nbsp;<?=$complete_book->getLinkmanAddr()?></td>
 </tr>
 <tr style='height:200.7pt'>
  <td width=112 align="center" style='font-size:12.0pt;'>提供评审的<br>技术文件</td>
  <td width=532 colspan=5 valign=top><?=nl2br($complete_book->getFileList())?></td>
 </tr>
 <tr style='height:90.8pt'>
  <td width=112 align="center" style='font-size:12.0pt;'>成果完成单位<br>主 管 部 门<br>审 查 意 见</td>
  <td width=532 colspan=5 valign="bottom" align="right">（盖章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
                                    年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;</td>
 </tr>
 <tr style='height:90.55pt'>
  <td width=112 align="center" style='font-size:12.0pt;'>组 织 评 审<br>单 位 意 见</td>
  <td width=532 colspan=5 valign="bottom" align="right">（盖章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
                                    年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;</td>
 </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border=1 align="right" cellpadding=0 cellspacing=0 style='margin-left:194.4pt;'>
 <tr>
  <td width=147 valign=top style='width:110.25pt'>建议密级</td>
  <td width=147 valign=top style='width:68.25pt'>&nbsp;</td>
 </tr>
 <tr>
  <td width=147 valign=top style='width:110.25pt'>批准密级及编号</td>
  <td width=147 valign=top style='width:68.25pt'>&nbsp;</td>
 </tr>
</table>

<p align="center" class="STYLE10"></p>
<p>&nbsp;</p>
<p><br /></p>
<p align="center" style=" clear:both;"><span class="STYLE4">软科学研究成果评审证书</span></p>
<p align="center" style="font-size:14pt;">软  评  字  （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;） &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号</p>
<p align="center" style="line-height:0.5">&nbsp;</p>
<table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def11">
  <tr>
    <td rowspan="6" align="right" class="def11"  width="3%" ></td>
    <td width="24%" height="80" align="right"  valign="bottom" class="def11" ><span class="STYLE81"><span class="STYLE8">成 果 名 称</span>：</span></td>
    <td class="def2"  align="left" valign="bottom" width="70%" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=$project->getSubject()?>
    </span></td>
    <td rowspan="6" align="right" class="def11"  width="3%" ></td>
  </tr>
  <tr>
    <td class="def11" align="right" height="80" valign="bottom"><span class="STYLE81"><span class="STYLE8">成果完成单位</span>：</span></td>
    <td class="def2" align="left" valign="bottom" style="margin-bottom:0px;margin-top:20px;letter-spacing: 0px;"><span class="STYLE11">
      <?=$project->getCorporationName()?>
    </span></td>
  </tr>
  <tr>
    <td class="def11" align="right" height="80" valign="bottom"><span class="STYLE81"><span class="STYLE8">评 审 形 式</span>：</span></td>
    <td class="def2" align="left" valign="bottom" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=$complete_book->getAccreStyle()?>
    </span></td>
  </tr>
  <tr>
    <td class="def11" align="right" height="80" valign="bottom"><span class="STYLE81"><span class="STYLE8">组织评审单位</span>：</span></td>
    <td class="def2" align="left" valign="bottom" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=$complete_book->getDepartment()?>
    </span></td>
  </tr>
  <tr>
    <td class="def11" align="right" height="80" valign="bottom" ><span class="STYLE81"><span class="STYLE8">评 审 日 期</span>：</span></td>
    <td class="def2"  valign="bottom" align="left" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=$complete_book->getAccreDate()?>
    </span></td>
  </tr>
</table>
<p align="center" class="STYLE10">&nbsp;</p>
<p align="center" class="STYLE10">&nbsp;</p>
<p align="center"><span  class="STYLE8">四川省卫生健康委员会</span></p>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<p align="center"><span style="font-family:'仿宋_GB2312'; font-size: 15.8pt;font-weight: bold;">填 写 说 明</span></p>
<ol style="font-family:'仿宋_GB2312'; font-size: 12pt;">
<li><strong>《软科学研究成果评审证书》</strong>：本证书规格一律为标准A4纸，竖装。必须打印或铅印，字体为4号字。本证书为国家科学技术部制定的标准格式，任何部门、单位、个人均不得擅自改变内容、增减证书中栏目。</li>
<li><strong>编号</strong>：指组织评审单位软科学成果管理机构按年度组织评审的顺序编号。(如科学技术部2000年组织评审项目编号为国科软评字[2000]╳╳╳号)。</li>
<li><strong>成果名称</strong>：申请评审时经组织评审单位审查同意使用的成果名称</li>
<li><strong>成果完成单位</strong>：指承担该项目主要研制任务的单位。由二个以上单位共同完成时，按技术合同中研制单位顺序排列。</li>
<li><strong>组织评审单位</strong>：组织此项成果评审的单位.</li>
<li><strong>评审形式</strong>：指该成果评审所采用的评审形式，即会议评审或通信评审.</li>
<li><strong>评审日期</strong>：指该项成果通过专家评审的日期</li>
<li><strong>研究成果简介</strong>：应包括如下内容：<br>(1)任务来源：应写清计划来源、名称及其编号。<br>
  (2)应用领域和研究原理。<br>
  (3)主要结论(写明合同要求的主要研究任务和实际达到的研究目标)。<br>
  (4)成果的创新点、先进性。<br>
  (5)作用意义(直接经济效益和社会意义)。
</li>
<li><strong>主要技术文件目录</strong>：指按照规定由申请评审单位必须递交的主要文件和技术资料.</li>
<li><strong>评审意见</strong>：会议评审是评审委员会形成的评审意见；通信评审是函审专家组正副组长根据函审专家评审意见表汇总形成的意见；评审意见须手写签名.</li>
<li><strong>主要研究人员名单</strong>：由负责人填写.</li>
<li><strong>评审委员会名单</strong>：由负责人填写，打印后评审委员会名单须手写签名；若为通信评审，每一份评审意见均须手写签名，附在评审证书后。</li>
<li><strong>主持评审单位意见</strong>：由受组织评审单位委托，具体主持该项成果评审工作的单位填写，单位领导签字，并加盖公章.</li>
<li><strong>组织评审单位意见</strong>：由负责该项成果评审工作的国家科学技术部，省、自治区、直辖市科技厅（委），国务院有关部门软科学研究管理机构的组织评审单位填写，由主管领导签字，并加盖公章.</li>
</ol>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
    <td width="568" height="36" align="center">一、研    究    成    果    简    介</td>
  </tr>
  <tr>
    <td width="568" height="820" valign="top"><?=$complete_book->getCompleteRkxcg()->getOverall()?></td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" align="center"><strong>二、成     果     应     用     前     景</strong></td>
  </tr>
  <tr>
    <td width="608" valign="top" class="abc" height="830"><?=$complete_book->getCompleteRkxcg()->getYyqj()?></td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" align="center"><strong>三、主 要 技 术 文 件 目 录 及 提 供 单 位</strong></td>
  </tr>
  <tr>
    <td width="608" valign="top" class="abc" height="830"><?=$complete_book->getCompleteRkxcg()->getWjmlTgdw()?></td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" align="center"><strong>四、评       审      意      见</strong></td>
  </tr>
  <tr>
    <td width="608" valign="top" class="abc" height="830"><?=$complete_book->getCompleteRkxcg()->getPsyj()?>
	<p style="margin-top:80pt;text-align:right">评审委员会主任：＿＿＿＿＿    副主任：＿＿＿＿、＿＿＿＿</p>
	<p style="text-align:right">＿＿＿年＿＿＿月＿＿＿日</p>
	</td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" align="center"><strong>五、主   持   评   审   单   位   意   见</strong></td>
  </tr>
  <tr>
    <td width="608" valign="bottom" class="abc" height="400">
	<p style="margin-top:30pt;text-align:right">主管领导签字：＿＿＿＿＿（单位盖章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
	<p style="text-align:right">＿＿＿年＿＿＿月＿＿＿日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
	</td>
  </tr>
  <tr>
  <td width="608" valign="middle" class="abc" height="36" align="center"><strong>六、组   织   评   审   单   位   意   见</strong></td>
  </tr>
  <tr>
    <td width="608" valign="bottom" class="abc" height="400">
	<p style="margin-top:30pt;text-align:right">主管领导签字：＿＿＿＿＿（单位盖章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
	<p style="text-align:right">＿＿＿年＿＿＿月＿＿＿日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
	</td>
  </tr>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0" style="border:none" cellpadding="0" width="680" align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" colspan="6" align="center"><strong>七、软科学成果完成单位情况</strong></td>
  </tr>
  <tr style='height:25pt'>
  <td width="608" align="center">序号</td>
  <td width="21%" align="center">完成单位名称</td>
  <td width="12%" align="center">邮政编码</td>
  <td width="38%" align="center">详细通讯地址</td>
  <td width="12%" align="center">隶属省部</td>
  <td width="12%" align="center">单位属性</td>
 </tr>
  <?php 
  $unit = $project->getComplateBook()->getCompleteRkxcg()->getUnit();
  for($i=1;$i<11;$i++):?>
	  	<tr>
		<td class="borders" height="29" align="center"><?=$i?></td>
	  <td class="borders" height="29" align="center"><?=$unit[$i][0]?></td>
	  <td class="borders" align="center"><?=$unit[$i][1]?></td>
	  <td class="borders" align="center"><?=$unit[$i][2]?></td>
	  <td class="borders" align="center"><?=$unit[$i][3]?></td>
	  <td class="borders" align="center"><?=$unit[$i][4]?></td>
	  	</tr>  
  <?php endfor;?>
</table>
<p align="left">注：１、完成单位名称必须填写全称，不得简化，与单位公章完全一致。<br>
&nbsp;&nbsp;&nbsp;&nbsp;２、详细通信地址要写明省（自治区、直辖市）、市（地区）、县（区）、街道和门牌号码。<br>
&nbsp;&nbsp;&nbsp;&nbsp;３、隶属省部是指本单位和行政关系隶属于哪一个省、自治区、直辖市或国务院部门主管，并将其名称填入表中。如果本单位有地方／部门双重隶属关系，请按主要隶属关系填写。<br>
&nbsp;&nbsp;&nbsp;&nbsp;４、单位属性是指本单位在１.独立科研机构  ２.大专院校  ３.工矿企业  ４.集体或个体企业  ５.其他五类性质中属于哪一类，并在栏中选填１. ２. ３. ４. ５. 即可。</p>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" colspan="7" align="center"><strong>八、主 要 研 究 人 员 名 单</strong></td>
  </tr>
  <tr style='height:25pt'>
  <td width="12%" align="center">姓  名</td>
  <td width="8%" align="center">年龄</td>
  <td width="12%" align="center">文化程度</td>
  <td width="12%" align="center">所学专业</td>
  <td width="16%" align="center">职称职务</td>
  <td width="16%" align="center">工作单位</td>
  <td width="24%" align="center">对成果的创造性贡献</td>
 </tr>
<?php 
$team = $project->getComplateBook()->getCompleteRkxcg()->getTeam();
for($i=1;$i<13;$i++):?>
	  	<tr>
	  <td class="borders" height="29" align="center"><span class="tableborder"><?=$team[$i][0]?></span></td>
	  <td class="borders" height="29" align="center"><span class="tableborder"><?=$team[$i][1]?></span></td>
	  <td class="borders" height="29" align="center"><span class="tableborder"><?=$team[$i][2]?></span></td>
	  <td class="borders" align="center"><?=$team[$i][3]?></td>
	  <td class="borders" align="center"><span class="tableborder"><?=$team[$i][5]?></span></td>
	  <td class="borders" align="center"><span class="tableborder"><?=$team[$i][6]?></span></td>
	  <td class="borders" align="center"><?=$team[$i][7]?></td>
	  	</tr>  
		<?php endfor;?>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<table border="1" cellspacing="0"  style="border:none" cellpadding="0" width="680"   align="center" class="abc">
  <tr>
  <td width="608" valign="middle" class="abc" height="36" colspan="7" align="center"><strong>九、评 审 委 员 会 名 单</strong></td>
  </tr>
  <tr style='height:25pt'>
  <td width="15%" align="center">评审会职务</td>
  <td width="12%" align="center">姓&nbsp;&nbsp;名</td>
  <td width="18%" align="center">工作单位</td>
  <td width="14%" align="center">所学专业</td>
  <td width="14%" align="center">现从事专业</td>
  <td width="15%" align="center">职称<br>（或职务）</td>
  <td width="12%" align="center">签  名</td>
 </tr>
 <?php for($i=1;$i<14;$i++):?>
	  	<tr>
	  <td class="borders" height="29" align="center"><span class="abc"><?=$complete_book->getExpertDuty($i)?></span></td>
	  <td class="borders" height="29" align="center"><span class="abc"><?=$complete_book->getExpertName($i)?></span></td>
	  <td class="borders" align="center"><span class="abc"><?=$complete_book->getExpertTitle($i)?></span></td>
	  <td class="borders" align="center"><span class="abc"><?=$complete_book->getExpertLearnSpeciality($i)?></span></td>
	  <td class="borders" align="center"><span class="abc"><?=$complete_book->getExpertSpeciality($i)?></span></td>
	  <td class="borders" align="center"><span class="abc"><?=$complete_book->getExpertWorkunit($i)?></span></td>
	  <td class="borders" align="center">&nbsp;</td>
	  	</tr>
		<?php endfor;?>
</table>

<br />
<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span><br />

<?php $outlay = $complete_book->getOutlay();?>
<table width="680" border="1" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td colspan="6" height="38" valign="top"><span align="left"><strong style="font-size:14px;">十、经费决算表。</strong>（<span class="STYLE12">单位：万元</span>） </span> </td>
  </tr>
  <tr style='height:22.6pt'>
    <td colspan="3" align="center"><strong>收&nbsp;&nbsp;&nbsp;入</strong></td>
    <td colspan="3" align="center"><strong>支&nbsp;&nbsp;&nbsp;出</strong></td>
  </tr>
  <tr style='height:30.95pt'>
    <td width="188" align="center">科目</td>
    <td width="69" align="center">预算数<br />
    （万元）</td>
    <td width="70" align="center">实际数<br />
    （万元）</td>
    <td width="208" align="center">科目</td>
    <td width="67" align="center">金额<br />
    （万元）</td>
    <td width="58" align="center">其中省拨资金</td>
  </tr>
  <tr style='height:22.15pt'>
    <td>1、省科技厅拨款</td>
    <td><?=$outlay[0][0]?></td>
    <td><?=$outlay[1][0]?></td>
    <td>1、设备费</td>
    <td><?=$outlay[2][0]?></td>
    <td><?=$outlay[3][0]?></td>
  </tr>
  <tr style='height:23.15pt'>
    <td>2、自筹</td>
    <td><?=$outlay[0][1]?></td>
    <td><?=$outlay[1][1]?></td>
    <td>（1）购置设备费</td>
    <td><?=$outlay[2][1]?></td>
    <td><?=$outlay[3][1]?></td>
  </tr>
  <tr style='height:22.7pt'>
    <td>（1）国家部委拨款</td>
    <td><?=$outlay[0][2]?></td>
    <td><?=$outlay[1][2]?></td>
    <td>（2）设备改造与租赁费</td>
    <td><?=$outlay[2][3]?></td>
    <td><?=$outlay[3][3]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（2）国家其他拨款</td>
    <td><?=$outlay[0][3]?></td>
    <td><?=$outlay[1][3]?></td>
    <td>2、资料费</td>
    <td><?=$outlay[2][4]?></td>
    <td><?=$outlay[3][4]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（3）行业主管部门拨款</td>
    <td><?=$outlay[0][4]?></td>
    <td><?=$outlay[1][4]?></td>
    <td>3、数据采集费</td>
    <td><?=$outlay[2][5]?></td>
    <td><?=$outlay[3][5]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（4）市州县财政拨款</td>
    <td><?=$outlay[0][5]?></td>
    <td><?=$outlay[1][5]?></td>
    <td>4、差旅费</td>
    <td><?=$outlay[2][7]?></td>
    <td><?=$outlay[3][7]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（5）银行贷款</td>
    <td><?=$outlay[0][6]?></td>
    <td><?=$outlay[1][6]?></td>
    <td>5、会议费</td>
    <td><?=$outlay[2][8]?></td>
    <td><?=$outlay[3][8]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（6）单位自有资金</td>
    <td><?=$outlay[0][7]?></td>
    <td><?=$outlay[1][7]?></td>
    <td>6、国际合作与交流费</td>
    <td><?=$outlay[2][9]?></td>
    <td><?=$outlay[3][9]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>（7）其他来源</td>
    <td><?=$outlay[0][8]?></td>
    <td><?=$outlay[1][8]?></td>
    <td>7、专家咨询费</td>
    <td><?=$outlay[2][12]?></td>
    <td><?=$outlay[3][12]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>8、劳务费</td>
    <td><?=$outlay[2][11]?></td>
    <td><?=$outlay[3][11]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>9、印刷费</td>
    <td><?=$outlay[2][10]?></td>
    <td><?=$outlay[3][10]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>10、管理费</td>
    <td><?=$outlay[2][13]?></td>
    <td><?=$outlay[3][13]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
    <td>11、其他费用</td>
    <td><?=$outlay[2][14]?></td>
    <td><?=$outlay[3][14]?></td>
  </tr>
  <tr style='height:23.0pt'>
    <td>经费来源合计</td>
    <td><?=$outlay[0][15]?></td>
    <td><?=$outlay[1][15]?></td>
    <td>经费实际支出合计</td>
    <td><?=$outlay[2][15]?></td>
    <td><?=$outlay[3][15]?></td>
  </tr>
  <tr style='height:77.55pt'>
    <td height="96" colspan="6" align="right" valign="bottom"><p style="text-align:left; margin:4px;">项目负责人（签字）：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;承担单位责任人（签字）：</p>
        <p style="text-align:left;margin:4px;">单位财务负责人（签字）：</p>
      年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;<br />
      （单位公章）&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
  </tr>
  <tr style='height:60.85pt'>
    <td height="89" align="center">财务验收意见</td>
    <td colspan="5" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;财务验收专家组长（签字）：&nbsp;<br />      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日</td>
  </tr>
  <tr style='height:60.85pt'>
    <td height="92" align="center">条件财务处意见</td>
    <td colspan="5" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;条件财务处负责人（签字）：<br />      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
  </tr>
</table>
</body>
</html>