<div class="main">
  <div class="tools"> <a href="javascript:history.go(-1);" class="back">返回</a> <a href="#" onclick="$('#validateForm').submit();return false;" class="save">保存申报资料</a> <a href="<?=site_url("declare/kjjbj/output/id/".$project->getProjectId())?>" class="print">导出打印</a>
  </div>
  <div class="title"> <a href="<?=site_url("declare/kjjbj/index")?>">填表说明</a> <a href="<?=site_url("declare/kjjbj/cover")?>">申请封面</a> <a href="<?=site_url("declare/kjjbj/info")?>">项目基本情况</a>  <a href="<?=site_url("declare/kjjbj/content1")?>">第一页</a> <a href="<?=site_url("declare/kjjbj/content2")?>">第二页</a> <a href="<?=site_url("declare/kjjbj/content3")?>">第三页</a> <a href="<?=site_url("declare/kjjbj/content4")?>">第四页</a> <a href="<?=site_url("declare/kjjbj/content5")?>">第五页</a> <a href="<?=site_url("declare/kjjbj/content6")?>" class="active">第六页</a> <a href="<?=site_url("declare/kjjbj/content7")?>">第七页</a> <a href="<?=site_url("declare/kjjbj/content8")?>">第八页</a></div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption class="caption_title">八、候选人情况表</caption>
        <?php
			$wcren = $project->getDetails()->getWcren();
			if(!$wcren['number']) $wcren['number'] = 1; 
		?>
        <tbody>
        <tr>
        <th colspan="2">候选人个数</th>
        <td colspan="5"><select name="wcren[number]" onchange="$('#validateForm').submit();">
          <?=getOptionString($wcren['number'],array('1'=>'1','2'=>'2','3'=>'3','4'=>'4','5'=>'5','6'=>'6','7'=>'7','8'=>'8','9'=>'9','10'=>'10','11'=>'11','12'=>'12'))?>
        </select>(请首先选择候选人数，选择后系统会自动保存当前的填写，然后生成多个项目参与人员的表以供填写。)</td>
        </tr>
        <?php for($i=1;$i <= $wcren['number'];$i++):?>
		<tr>
        <th width="6%" rowspan="2">第[<?=$i?>]候选人</th>
        <th width="7%">姓名
          <input name="wcren[<?=$i?>][mc]" type="hidden" class="number" value="<?=$i?>" size="5" /></th>
        <td width="30%"><input type="text" name="wcren[<?=$i?>][xm]" value="<?=$wcren[$i][xm]?>" /></td>
        <th width="10%">民 族</th>
        <td width="17%" align="left"><select name="wcren[<?=$i?>][mz]" id="wcren[<?=$i?>][mz]">
          <?=getNationList($wcren[$i][mz])?>
        </select></td>
        <th width="13%">参与开始时间</th>
        <td width="17%" align="left"><input type="text" name="wcren[<?=$i?>][kzsj]" value="<?=$wcren[$i][kzsj]?>" class="date-pick" /></td>
        </tr>
		<tr>
		  <th>性 别</th>
		  <td><select name="wcren[<?=$i?>][xb]" id="wcren[<?=$i?>][xb]">
		    <option value="男" <?php if($wcren[$i][xb]=='男'):?>selected="selected"<?php endif;?>>男</option>
		    <option value="女" <?php if($wcren[$i][xb]=='女'):?>selected="selected"<?php endif;?>>女</option>
		    </select></td>
		  <th width="10%">党  派</th>
		  <td width="17%" align="left"><input type="text" name="wcren[<?=$i?>][dp]" value="<?=$wcren[$i][dp]?>" /></td>
		  <th width="13%">参与结束时间</th>
		  <td width="17%" align="left"><input type="text" name="wcren[<?=$i?>][jssj]" value="<?=$wcren[$i][jssj]?>" class="date-pick" /></td>
		  </tr>
		<tr>
		  <th colspan="2">出生地</th>
		  <td><input type="text" name="wcren[<?=$i?>][csd]" value="<?=$wcren[$i][csd]?>" style="width:99%;" title="如：四川省成都市" /></td>
		  <th>出生日期</th>
		  <td><input type="text" name="wcren[<?=$i?>][csrq]" value="<?=$wcren[$i][csrq]?>" class="date-pick" /></td>
		  <th>外语语种</th>
		  <td><input type="text" name="wcren[<?=$i?>][wyyz]" value="<?=$wcren[$i][wyyz]?>" /></td>
		  </tr>
		<tr>
		  <th colspan="2">电子信箱</th>
		  <td><input type="text" name="wcren[<?=$i?>][email]" value="<?=$wcren[$i][email]?>" class="email" style="width:99%;" /></td>
		  <th>何国华侨</th>
		  <td><input type="text" name="wcren[<?=$i?>][hghq]" value="<?=$wcren[$i][hghq]?>" /></td>
		  <th>熟练程度</th>
		  <td><?=getSkilled($wcren[$i][slcd],'wcren['.$i.'][slcd]')?></td>
		  </tr>
		<tr>
		  <th colspan="2">工作单位</th>
		  <td colspan="3"><input type="text" name="wcren[<?=$i?>][gzdw]" value="<?=$wcren[$i][gzdw]?>" style="width:99%;" /></td>
		  <th>联系电话</th>
		  <td><input type="text" name="wcren[<?=$i?>][lxdh]" value="<?=$wcren[$i][lxdh]?>" /></td>
		  </tr>
		<tr>
		  <th colspan="2">家庭住址</th>
		  <td colspan="3"><input type="text" name="wcren[<?=$i?>][jtdz]" value="<?=$wcren[$i][jtdz]?>" style="width:99%;" /></td>
		  <th>住宅电话</th>
		  <td><input type="text" name="wcren[<?=$i?>][zzdh]" value="<?=$wcren[$i][zzdh]?>" /></td>
		  </tr>
		<tr>
		  <th colspan="2">通讯地址</th>
		  <td colspan="3"><input type="text" name="wcren[<?=$i?>][txdz]" value="<?=$wcren[$i][txdz]?>" style="width:99%;" /></td>
		  <th>邮政编码</th>
		  <td><input type="text" name="wcren[<?=$i?>][yzbm]" value="<?=$wcren[$i][yzbm]?>" class="number" /></td>
		  </tr>
		<tr>
		  <th colspan="2">毕业学校</th>
		  <td><input type="text" name="wcren[<?=$i?>][byxx]" value="<?=$wcren[$i][byxx]?>" /></td>
		  <th>文化程度</th>
		  <td><input type="text" name="wcren[<?=$i?>][whcd]" value="<?=$wcren[$i][whcd]?>" /></td>
		  <th>学位</th>
		  <td><input type="text" name="wcren[<?=$i?>][xw]" value="<?=$wcren[$i][xw]?>" /></td>
		  </tr>
		<tr>
		  <th colspan="2">职务、职称</th>
		  <td><input type="text" name="wcren[<?=$i?>][zc]" value="<?=$wcren[$i][zc]?>" /></td>
		  <th>专业、专长</th>
		  <td><input type="text" name="wcren[<?=$i?>][zy]" value="<?=$wcren[$i][zy]?>" /></td>
		  <th>毕业时间</th>
		  <td><input type="text" name="wcren[<?=$i?>][bysj]" value="<?=$wcren[$i][bysj]?>" class="date-pick" /></td>
		  </tr>
          <tr>
            <th colspan="2">曾获科技奖励情况</th>
            <td colspan="5"><textarea name="wcren[<?=$i?>][jlry]" rows="5" style="width:99%;"><?=$wcren[$i][jlry]?></textarea></td>
          </tr>
		<tr>
		  <th colspan="2">对本项目技术<br/>创造性贡献</th>
		  <td colspan="5"><textarea name="wcren[<?=$i?>][czxgx]" rows="8" style="width:99%;"><?=$wcren[$i][czxgx]?></textarea></td>
		  </tr> 
		<?php endfor;?>                 
        </tbody>
      </table>
      <input type="hidden" name="project_id" value="<?=$project->getProjectId()?>" id="project_id" />
    </form>
  </div>
</div>
