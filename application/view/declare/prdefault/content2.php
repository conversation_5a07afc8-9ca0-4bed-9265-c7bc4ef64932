<div class="main">
  <div class="page-header btn-group-sm"> <a href="javascript:history.go(-1);" class="btn btn-primary">返回</a> <a href="#" onclick="$('#validateForm').submit();return false;" class="btn btn-primary">保存申报资料</a> <?php if(input::getInput("session.userlevel") == 3):?><a href="<?=site_url("unit/propertyright/doSubmit/id/".$propertyright->getProjectId())?>" class="btn btn-warning">上报资料</a><?php else:?><a href="<?=site_url("user/propertyright/doSubmit/id/".$propertyright->getProjectId())?>" class="btn btn-warning">上报资料</a><?php endif;?> <a href="<?=site_url("declare/prdefault/output/id/".$propertyright->getProjectId())?>" class="btn btn-primary">导出打印</a>
  </div>
  <div class="tabbable">
  <ul class="nav nav-tabs" id="myTab">
     <li ><a href="<?=site_url("declare/prdefault/index")?>">填表说明</a></li>
     <li><a href="<?=site_url("declare/prdefault/content1")?>">基本情况</a></li>
     <li class="active"><a href="<?=site_url("declare/prdefault/content2")?>">实施情况</a></li>
     <li><a href="<?=site_url("declare/prdefault/content3")?>">专利情况</a></li>
     <li><a href="<?=site_url("declare/prdefault/content4")?>">经济效益</a></li>
     <li><a href="<?=site_url("declare/prdefault/content5")?>">社会效益</a></li>
     <li><a href="<?=site_url("declare/prdefault/content6")?>">正文上传</a></li>
     <li><a href="<?=site_url("declare/prdefault/content7")?>">附件上传</a></li>
 </ul>
  <div class="tab-content">
    <div class="tab-pane fade in active">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
  <caption>
  <strong><h4>实施情况</h4></strong>
  </caption>
  <tr>
    <th>序号</th>
    <th>实施单位</th>
    <th>许可种类</th>
    <th>许可金额<small>（单位：元）</small></th>
    <th>合同履行情况</th>
  </tr>
  <?php $ssqk = $propertyright->getSsqk();?>
  <?php for($i=0;$i<10;$i++):?>
  <tr>
    <th><?=($i+1)?></th>
    <td><input name="ssqk[ssdw][]" type="text" style="width:98%;" value="<?=$ssqk['ssdw'][$i]?>" /></td>
    <td><input name="ssqk[xkzl][]" type="text" style="width:98%;" value="<?=$ssqk['xkzl'][$i]?>" /></td>
    <td><input name="ssqk[xkje][]" type="text" style="width:98%;" value="<?=$ssqk['xkje'][$i]?>" /></td>
    <td><input name="ssqk[htlxqk][]" type="text" style="width:98%;" value="<?=$ssqk['htlxqk'][$i]?>" /></td>
  </tr>
  <?php endfor;?>
  <tr>
    <td colspan="6" align="center"><input type="submit" name="Submit" value="保存资料" class="btn btn-primary"/>
      <input name="id" type="hidden" value="<?=$propertyright->getProjectId()?>" /></td>
    </tr>
</table>
    </form>
  </div>
</div>
</div>
</div>