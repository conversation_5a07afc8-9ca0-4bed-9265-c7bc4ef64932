<div class="main">
  <div class="page-header btn-group-sm"> <a href="<?=getFromUrl()?>" class="btn btn-primary">返回</a> <a href="<?=site_url("declare/jcgx/output/id/".$devote->getProjectId())?>" class="btn btn-info">导出打印</a>
    <?php if($devote->getUserId() == input::getInput("session.userid") && $devote->isEdit()):?>
    <a href="<?=site_url("declare/jcgx/index/id/".$devote->getProjectId())?>" class="btn btn-primary">修改</a>
    <?php endif;?>
  </div>
  <div class="tabbable">
<ul class="nav nav-tabs" id="myTab">
<li <?php if($page==1) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/1/id/".$devote->getProjectId())?>">第一页</a></li>
<li <?php if($page==2) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/2/id/".$devote->getProjectId())?>">第二页</a></li>
<li <?php if($page==3) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/3/id/".$devote->getProjectId())?>">第三页</a></li>
<li <?php if($page==4) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/4/id/".$devote->getProjectId())?>">第四页</a></li>
<li <?php if($page==5) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/5/id/".$devote->getProjectId())?>">第五页</a></li>
<li <?php if($page==6) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/6/id/".$devote->getProjectId())?>">第六页</a></li>
<li <?php if($page==7) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/7/id/".$devote->getProjectId())?>">第七页</a></li>
<li <?php if($page==8) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/8/id/".$devote->getProjectId())?>">第八页</a></li>
<li <?php if($page==9) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/9/id/".$devote->getProjectId())?>">第九页</a></li>
<li <?php if($page==10) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/10/id/".$devote->getProjectId())?>">第十页</a></li>
<li <?php if($page==11) echo 'class="active"';?>><a href="<?=site_url("declare/jcgx/show/page/11/id/".$devote->getProjectId())?>">主要附件</a></li>
  </ul>
  <div class="box">
    <?php if($page == 1):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      一、候选人基本情况
      </caption>
      <tr>
        <th width="17%">姓    名</th>
        <td width="21%"><?=$devote->getUserName()?></td>
        <th width="16%">性  别</th>
        <td width="14%"><?=$devote->getUserSex()?></td>
        <th width="14%">民  族</th>
        <td width="18%"><?=$devote->getUserNation()?></td>
      </tr>
      <tr>
        <th>出生年月</th>
        <td><?=$devote->getUserDateOfBirth("Y-m-d")?></td>
        <th>出生地</th>
        <td><?=$devote->getUserArea()?></td>
        <th>党  派</th>
        <td><?=$devote->getUserParty()?></td>
      </tr>
      <tr>
        <th>工作单位</th>
        <td colspan="5"><?=$devote->getUserWorkUnit()?$devote->getUserWorkUnit():$unit->getSubject()?></td>
      </tr>
      <tr>
        <th>通讯地址</th>
        <td colspan="3"><?=$devote->getUserAddress()?></td>
        <th>邮政编码</th>
        <td><?=$devote->getUserPostcode()?></td>
      </tr>
      <tr>
        <th>联系电话</th>
        <td><?=$devote->getUserPhone()?></td>
        <th>电子邮箱</th>
        <th colspan="3"><?=$devote->getUserEmail()?></th>
      </tr>
      <tr>
        <th>文化程度</th>
        <td><?=$devote->getUserGrade()?></td>
        <th>学   位</th>
        <td><?=$devote->getUserDegree()?></td>
        <th>授予时间</th>
        <td><?=$devote->getUserDegreeTime()?></td>
      </tr>
      <tr>
        <th>所学专业</th>
        <td colspan="2"><?=$devote->getUserSpecialty()?></td>
        <th>从事专业</th>
        <td colspan="2"><?=$devote->getUserWork()?></td>
      </tr>
      <tr>
        <th>职    称</th>
        <td colspan="2"><?=$devote->getUserJobTitle()?></td>
        <th>职    务</th>
        <td colspan="2"><?=$devote->getUserDuties()?></td>
      </tr>
      <tr>
        <th>推荐部门</th>
        <td colspan="2"><?=$devote->getDepartmentName()?></td>
        <th>推荐日期</th>
        <td colspan="2"><?=$devote->getCreatedAt("Y-m-d")?></td>
      </tr>
      <tr>
        <th height="139">受教育情况</th>
        <td colspan="5"><?=text_to_html($devote->getDetails(true)->getStuyList())?></td>
      </tr>
    </table>
    <?php elseif($page == 2):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      二、工作简历
      </caption>
      <tr>
        <th width="6%">序号</th>
        <th width="28%">年 月  至   年 月</th>
        <th width="40%">工 作 单 位</th>
        <th width="26%">职称、职务</th>
      </tr>
      <?php $job_resume = $devote->getDetails(true)->getJobResume();?>
      <?php for($i=0;$i<20;$i++):?>
      <tr>
        <th><?=($i+1)?></th>
        <td><?=$job_resume['time'][$i]?></td>
        <td><?=$job_resume['unit'][$i]?></td>
        <td><?=$job_resume['duty'][$i]?></td>
      </tr>
      <?php endfor;?>
    </table>
    <?php elseif($page == 3):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      三、候选人的主要科学技术成就和贡献
      </caption>
      <tr>
        <td>请详实、准确、客观地填写候选人从开始工作起至今为止，为科学技术事业发展所做 的创造性工作，应简明、扼要表述以候选人为主完成的科学发现、技术发明或技术创新要点，在学科发展、推动行业技术进步等方面做出的卓越贡献，承担科学技术研究课题情况。请按照学术成就和贡献的重要性及学术影响大小，顺序填写。近5年的成就和贡献请单独列出。总字数不超过5000字</td>
      </tr>
      <tr>
        <td><?=$devote->getDetails(true)->getAchieve()?></td>
      </tr>
    </table>
    <?php elseif($page == 4):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      四、候选人论文或专著发表情况
      </caption>
      <tr>
        <td>请注明第几作者，建议1000字以内</td>
      </tr>
      <tr>
        <td><?=$devote->getDetails(true)->getTreatisePublish()?></td>
      </tr>
    </table>
    <?php elseif($page == 5):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      五、候选人论文或专著被引用情况
      </caption>
      <tr>
        <td>请按照引文的学术影响程度，顺序填写，建议1000字以内</td>
      </tr>
      <tr>
        <td><?=$devote->getDetails(true)->getTreatiseQuote()?></td>
      </tr>
    </table>
    <?php elseif($page == 6):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      六、候选人曾获科技奖励情况
      </caption>
      <tr>
        <th>序号</th>
        <th>获奖时间</th>
        <th>获奖项目名称</th>
        <th>奖项名称</th>
        <th>奖励等级及排名</th>
        <th>授奖部门</th>
      </tr>
      <?php $awards = $devote->getDetails(true)->getAwards();?>
      <?php for($i=0;$i<20;$i++):?>
      <tr>
        <th><?=($i+1)?></th>
        <td><?=$awards['time'][$i]?></td>
        <td><?=$awards['xmmc'][$i]?></td>
        <td><?=$awards['jxmc'][$i]?></td>
        <td><?=$awards['hjdj'][$i]?></td>
        <td><?=$awards['sjbm'][$i]?></td>
      </tr>
      <?php endfor;?>
    </table>
    <?php elseif($page == 7):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      七、主要知识产权证明目录
      </caption>
      <tr>
        <th>序号</th>
        <th>知识产权类别</th>
        <th>知识产权具体名称</th>
        <th>国家(地区)</th>
        <th>授权号</th>
        <th>授权日期</th>
        <th>证书编号</th>
        <th>权利人</th>
        <th>发明人</th>
        <th>发明专利有效状态</th>
      </tr>
      <?php $certificate = $devote->getDetails(true)->getCertificate();?>
      <?php for($i=0;$i<20;$i++):?>
      <tr>
        <th><?=($i+1)?></th>
        <td><?=$certificate['cqlb'][$i]?></td>
        <td><?=$certificate['cqmc'][$i]?></td>
        <td><?=$certificate['gjdq'][$i]?></td>
        <td><?=$certificate['sqhm'][$i]?></td>
        <td><?=$certificate['sqrq'][$i]?></td>
        <td><?=$certificate['zsbh'][$i]?></td>
        <td><?=$certificate['qlr'][$i]?></td>
        <td><?=$certificate['fmr'][$i]?></td>
        <td><?=$certificate['zlzt'][$i]?></td>
      </tr>
      <?php endfor;?>
    </table>
    <?php elseif($page == 8):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      八、候选人工作单位意见
      </caption>
      <?php $unit_note = $devote->getDetails(true)->getUnitNote();?>
      <tr>
        <th colspan="2">候选人工作单位</th>
        <td colspan="5"><?=$unit_note['dwmc']?></td>
      </tr>
      <tr>
        <th width="11%" rowspan="2">候选人联系人</th>
        <th width="11%">姓名</th>
        <td width="24%"><?=$unit_note['lxrxm']?></td>
        <th width="11%">电子邮箱</th>
        <td colspan="3"><?=$unit_note['lxryx']?></td>
      </tr>
      <tr>
        <th>手机</th>
        <td><?=$unit_note['lxrsj']?></td>
        <th>固定电话</th>
        <td width="18%"><?=$unit_note['lxrdh']?></td>
        <th width="7%">传真</th>
        <td width="18%"><?=$unit_note['lxrcz']?></td>
      </tr>
      <tr>
        <th>候选人工作单位意见</th>
        <td colspan="6"><?=text_to_html($unit_note['dwyj'])?></td>
      </tr>
    </table>
    <?php elseif($page == 9):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      九、推荐意见
      </caption>
      <?php $note = $devote->getDetails(true)->getGatherNote();?>
      <tr>
        <th colspan="4" style="text-align:left;">1、推荐单位意见（专家推荐不填此栏）</th>
      </tr>
      <tr>
        <th width="16%" align="center">推荐单位</th>
        <td colspan="3"><?=$note['gather']['dwmc']?></td>
      </tr>
      <tr>
        <th align="center">通讯地址</th>
        <td width="34%"><?=$note['gather']['txdz']?></td>
        <th width="25%" align="center">邮政编码</th>
        <td width="25%"><?=$note['gather']['yzbm']?></td>
      </tr>
      <tr>
        <th align="center">联 系 人</th>
        <td><?=$note['gather']['lxr']?></td>
        <th align="center">联系电话</th>
        <td><?=$note['gather']['lxdh']?></td>
      </tr>
      <tr>
        <th align="center">电子邮箱</th>
        <td><?=$note['gather']['dzyx']?></td>
        <th align="center">传     真</th>
        <td><?=$note['gather']['fax']?></td>
      </tr>
      <tr>
        <th align="center">推荐意见</th>
        <td colspan="3"><?=text_to_html($note['gather']['yj'])?></td>
      </tr>
      <tr>
        <th colspan="4" style="text-align:left;">2、专家推荐意见（单位推荐不填此栏）</th>
      </tr>
      <tr>
        <th align="center">姓名</th>
        <td><?=$note['expert']['xm']?></td>
        <th align="center">身份证号</th>
        <td><?=$note['expert']['sfz']?></td>
      </tr>
      <tr>
        <th align="center">院     士</th>
        <td><?=$note['expert']['ys']?></td>
        <th align="center">学     部</th>
        <td><?=$note['expert']['xb']?></td>
      </tr>
      <tr>
        <th align="center">工作单位</th>
        <td colspan="3"><?=$note['expert']['gzdw']?></td>
      </tr>
      <tr>
        <th align="center">通讯地址</th>
        <td><?=$note['expert']['txdz']?></td>
        <th align="center">邮政编码</th>
        <td><?=$note['expert']['yzbm']?></td>
      </tr>
      <tr>
        <th align="center">电子邮箱</th>
        <td><?=$note['expert']['dzyx']?></td>
        <th align="center">联系电话</th>
        <td><?=$note['expert']['lxdh']?></td>
      </tr>
      <tr>
        <th align="center">推荐意见</th>
        <td colspan="3"><?=text_to_html($note['expert']['yj'])?></td>
      </tr>
    </table>
    <?php elseif($page == 10):?>
    <?php $testify = $devote->getDetails(true)->getTestify();?>
    <?php for($i=0,$n=($testify['number']>0?$testify['number']:1);$i<$n;$i++):?>
    <table width="100%" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      应用证明
      </caption>
      <tr>
        <th colspan="2">项目名称</th>
        <td width="78%"><?=$testify[$i]['xmmc']?></td>
      </tr>
      <tr>
        <th colspan="2">应用单位</th>
        <td><?=$testify[$i]['yydw']?></td>
      </tr>
      <tr>
        <th colspan="2">应用成果起止时间</th>
        <td><?=$testify[$i]['qzsj']?></td>
      </tr>
      <tr>
        <th colspan="2">通信地址</th>
        <td><?=$testify[$i]['txdz']?></td>
      </tr>
      <tr>
        <th width="5%" rowspan="7"><span style="text-align:left;">经济效益(万元)</span></th>
        <th width="17%">年度</th>
        <td><?=$testify[$i]['xynd']?></td>
      </tr>
      <tr>
        <th>项目总投资额</th>
        <td><?=$testify[$i]['tzze']?>
          <span style="text-align:left;">万元</span></td>
      </tr>
      <tr>
        <th><p >新增销售额</p></th>
        <td><?=$testify[$i]['xzxse']?>
        <span style="text-align:left;">万元</span></td>
      </tr>
      <tr>
        <th>新增利润(产量)</th>
        <td><?=$testify[$i]['xzcl']?></td>
      </tr>
      <tr>
        <th>新增利税</th>
        <td><?=$testify[$i]['xzlr']?>
          <span style="text-align:left;">万元</span></td>
      </tr>
      <tr>
        <th>创收外汇（美元）</th>
        <td><?=$testify[$i]['cswh']?>
          <span style="text-align:left;">万元</span></td>
      </tr>
      <tr>
        <th>节支总额</th>
        <td><?=$testify[$i]['jzze']?>
          <span style="text-align:left;">万元</span></td>
      </tr>
    </table>
    <?php endfor;?>
    <?php elseif($page == 11):?>
    <table width="100%" border="0" cellspacing="1" cellpadding="3" id="filemanager" class="tb_data table table-hover table-striped">
      <caption>
      主要附件
      </caption>
      <tr>
        <th>文件名</th>
        <th>文件大小</th>
        <th>上传时间</th>
        <th>下载</th>
      </tr>
      <?php
              $attachments = $devote->getAttachments();
			  while($attachment = $attachments->getObject()):
			  ?>
      <tr id="list_<?=$attachment->getId()?>">
        <td><a href="<?=site_path("up_files/".$attachment->getFilePath())?>" target="_blank">
          <?=$attachment->getFileName()?>
          </a></td>
        <td align="center"><?=$attachment->getFileSize()?></td>
        <td align="center"><?=$attachment->getCreatedAt()?></td>
		<td align="center"><a href="<?=site_path("up_files/".$attachment->getFilePath())?>" target="_blank">下载文件</a></td>
      </tr>
      <?php endwhile;?>
    </table>
    <?php endif;?>
  </div>
</div>
  </div>