<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                待审核的基本资料
            </h3>
            <div class="block-options">
                <!-- 可以在这里添加批量操作按钮 -->
            </div>
        </div>
        <div class="block-content pt-0">
            <div class="main">
                <div class="search">
                    <?php include_once('search_part.php') ?>
                </div>
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <table width="100%" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="20" class="tselect"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th width="17" class="tmore">详</th>
                                <th class="tsubject">平台名称</th>
                                <th class="tuser">第一依托单位</th>
                                <th class="tuser">主管部门</th>
                                <th class="tuser">平台负责人</th>
                                <th class="tuser">联系人</th>
                                <th class="tuser">手机</th>
                                <th width="230" class="text-center">可用操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($pager = $pagers->getObject()):?>
                                <tr>
                                    <td><input name="select_id[]" type="checkbox" value="<?=$pager->getId()?>" /></td>
                                    <td><label class="fold_bar" onclick="$('#show_<?=$pager->getId()?>').toggle()">+</label></td>
                                    <td><a target="_blank" href="<?=site_url('baseinfo/apply/show/id/'.$pager->getPlatformId())?>"><?=$pager->getSubject()?></a></td>
                                    <td><?=$pager->getCorporationName()?></td>
                                    <td><?=$pager->getDepartmentName()?></td>
                                    <td><?=$pager->getPrincipalName()?></td>
                                    <td><?=$pager->getLinkmanName()?></td>
                                    <td><?=$pager->getLinkmanMobile()?></td>
                                    <td align="center">
                                        <?=Button::setName('审核')->setUrl(site_url("baseinfo/company/doAccept/id/".$pager->getPlatformId()))->setClass('btn-alt-success')->setIcon('check')->setWidth('800px')->setHeight('600px')->window()?>
                                        <?=Button::setName('退回')->setUrl(site_url("baseinfo/company/doRejected/id/".$pager->getPlatformId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                                    </td>
                                </tr>
                                <tr class="fold_body" id="show_<?=$pager->getId()?>" style="display:none;">
                                    <td colspan="9">
                                        <?php include('more_part.php')?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="9" align="right">&nbsp;<span class="pager_bar"><?=$pagers->fromTo().$pagers->navbar(10)?></span></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>