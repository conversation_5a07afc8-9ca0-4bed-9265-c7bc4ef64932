<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                平台基本资料列表
            </h3>
            <div class="block-options">
                <?=Button::seturl(site_url('baseinfo/user/guide'))->setIcon('add')->link('新增平台基本资料')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                  <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
                    <div class="no-padding no-margin panel panel-default" >
                      <table align="center" cellpadding="3" cellspacing="1" class="table">
                        <thead>
                        <tr>
                            <th class="hidden-480 text-center"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th class="text-center">详</th>
                                <th class="text-center">重点实验室名称</th>
                                <th class="text-center">第一依托单位</th>
                                <th class="text-center">主管部门</th>
                                <th class="text-center">实验室主任</th>
                                <th class="text-center">联系人</th>
                                <th class="text-center">手机</th>
                                <th class="text-center">状态</th>
                                <th class="text-center">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $i=0;while($pager = $pagers->getObject()):$i++;?>
                            <tr>
                                <td align="center" class="hidden-480"><input name="select_id[]" type="checkbox" value="<?=$pager->getId()?>" /></td>
                                <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$pager->getId()?>').toggle()">+</label></td>
                                <td align="left" class="hidden-480"><a target="_blank" href="<?=site_url('baseinfo/apply/show/id/'.$pager->getPlatformId())?>"><?=$pager->getSubject()?></a></td>
                                <td align="left" class="hidden-480"><?=$pager->getCorporationName()?></td>
                                <td align="left" class="hidden-480"><?=$pager->getDepartmentName()?></td>
                                <td align="center" class="hidden-480"><?=$pager->getPrincipalName()?></td>
                                <td align="center" class="hidden-480"><?=$pager->getLinkmanName()?></td>
                                <td align="center" class="hidden-480"><?=$pager->getLinkmanMobile()?></td>
                                <td align="center" class="hidden-480"><?=$pager->getState()?></td>
                                <td align="center" class="hidden-480">
                                    <?php
                                        if(in_array($pager->getStatement(),[1,3,6,12])):
                                    ?>
                                            <?php
                                            $urls = [
                                                [
                                                    'name'=>'编辑',
                                                    'url'=>site_url("baseinfo/apply/edit/id/".$pager->getPlatformId()),
                                                    'icon'=>'edit',
                                                ],
                                                [
                                                    'name'=>'查看',
                                                    'url'=>site_url("baseinfo/apply/show/id/".$pager->getPlatformId()),
                                                    'icon'=>'show',
                                                ],
                                                [
                                                    'name'=>'上报',
                                                    'url'=>site_url("baseinfo/apply/submit/id/".$pager->getPlatformId()),
                                                    'icon'=>'submit',
                                                ],
                                                [
                                                    'name'=>'分割线',
                                                    'url'=>'-',
                                                ],
                                                [
                                                    'name'=>'删除',
                                                    'url'=>site_url("baseinfo/apply/remove/id/".$pager->getPlatformId()),
                                                    'icon'=>'trash',
                                                    'class'=>'text-danger',
                                                    'event'=>'return showConfirm(\'删除后将不可恢复，确定要删除吗？\',this);',
                                                ]
                                            ];
                                            echo Button::setClass('btn-primary')->group('操作',$urls)?>
                                    <?php
                                        else:
                                    ?>
                                            <a class="btn btn-primary btn-sm" href="<?=site_url('baseinfo/apply/show/id/'.$pager->getPlatformId())?>">查看</a>
                                    <?php
                                        endif;
                                    ?>
                                </td>
                            </tr>
                            <tr id="show_<?=$pager->getId()?>" style="display:none;">
                                <td colspan="10">
                                    <?php include('more_part.php')?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfooter>
                            <?php
                            if($i==0):
                                ?>
                                <tr>
                                    <td colspan="10" align="center">
                                        <?=Button::seturl(site_url('baseinfo/user/guide'))->setSize('btn-lg')->setIcon('add')->link('新增平台基本资料')?>
                                    </td>
                                </tr>
                            <?php
                            endif;
                            if($pagers->getTotal()>0):
                            ?>
                          <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromTo().$pagers->navbar(10)?></td>
                          </tr>
                            <?php endif;?>
                        </tfooter>
                      </table>
                    </div>
                  </form>
            </div>
        </div>
    </div>
</div>