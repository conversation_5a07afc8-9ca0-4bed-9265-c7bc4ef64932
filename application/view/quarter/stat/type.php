<div class="page-header">
    <div class="row">
        <div class="col-sm-12">
            <div class="page-title-box">
                <div class="btn-group pull-right">
                    <?=btn('导出统计表','export/quarter/stat_type/guideid/'.input::getMix('guideid'),'mdi mdi-download')?>
                    <?=btn('导出明细表','export/export/index/table/quarters/guideid/'.$guide->getId(),'mdi mdi-download')?>
                    <?=btn('返回','javascript:history.go(-1);','fa fa-chevron-left','info')?>
                </div>
                <h4 class="page-title"><?=$guide->getSubject()?> - 按平台类别统计</h4>
            </div>
        </div>
    </div>
</div>
<div class="page-body">
    <div class="row">
        <div class="col-lg-12">
            <div class="card m-b-30">
                <div class="card-body">
                    <div class="table-responsive b-0">
                        <table class="table table-bordered">
                            <thead class="thead-default">
                            <tr>
                                <th style="width: 150px">
                                    <p align="center">类别</p>
                                </th>
                                <th style="width: 250px">
                                    <p align="center">指标名称</p>
                                </th>
                                <th style="width: 100px%">
                                    <p align="center">单位</p>
                                </th>
                                <?php
                                    foreach ($types as $type):
                                ?>
                                <th>
                                    <p align="center"><?=$type?></p>
                                </th>
                                <?php endforeach;?>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $topIndexs = getTopIndexs();
                            while($topIndex = $topIndexs->getObject()):
                                $i = 0;
                                $indent = '';
                                $indexs = getIndexs($topIndex->getCode());
                                $j=0;
                                while($index = $indexs->getObject()):
                                    $indent = $index->getIndent();
                                    if($indent>0) $j=0;
                                    ?>
                                    <tr>
                                        <?php
                                        if($i==0):
                                            ?>
                                            <td class="text-center" rowspan="<?=$indexs->getTotal()?>" style="vertical-align: middle">
                                                <?=$topIndex->getSubject()?>
                                            </td>
                                        <?php endif;?>
                                        <td class="text-left" style="text-indent: <?=$indent?>em">
                                            <?=$j==0 ? $index->getQizhong() : ''?><?=$index->getSubject()?>
                                        </td>
                                        <td class="text-center">
                                            <?=$index->getUnit()?>
                                        </td>
                                        <?php
                                        foreach ($types as $type):
                                            ?>
                                            <td class="text-center">
                                                <?=$guide->getQuaterDataByPlatformType($type,$index->getCode()) ?>
                                            </td>
                                        <?php endforeach;?>

                                    </tr>
                                    <?php $i++;$j++;endwhile;?>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>