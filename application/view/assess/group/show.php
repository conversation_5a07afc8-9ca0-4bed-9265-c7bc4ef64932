<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                评审组详情
            </h3>
            <div class="block-options">
                <?=btn('back')?>
                <?php if(!$group->getIsLock()):?>
                    <?=btn('button','删除选中项','button','delete','validateForm.action=\''.site_url("assess/group/remove").'\';$(\'#validateForm\').submit();return false;','btn-sm','btn-danger')?>
                <?php endif;?>
                <?=btn('window','导出项目',site_url("export/export/index"),'export')?>
                <a href="<?=site_url("assess/assign/export/id/".$group->getId())?>" target="_blank" class="btn btn-sm btn-danger" role="button">下载专家名单</a>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="box">
                    <table cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                        <caption>
                            1、参与分组【<?=$group->getGroupSubject()?>】评审的专家名单如下：
                        </caption>
                        <?php if($experts->getTotal()):?>
                            <tr>
                                <th>姓名</th>
                                <th>性别</th>
                                <th>职称</th>
                                <th>角色</th>
                                <th>专家类别</th>
                                <th width="150">工作单位</th>
                                <th width="250">研究领域</th>
                                <th>未评数/未上报/已评数</th>
                            </tr>
                            <?php while($expert = $experts->getObject()):?>
                                <tr>
                                    <td><?=link_to("expert/profile/show/userid/".$expert->getUserId(),$expert->getUserName())?></td>
                                    <td><?=$expert->getUserSex()?></td>
                                    <td><?=$expert->getUserHonor()?></td>
                                    <td><?php if($expert->getRole($group->getId()) == 'CW'):?><span style="color:#F48802;">财务专家</span><?php else:?><span style="color:green;">技术专家</span><?php endif;?></td>
                                    <td><?=$expert->getUserType()?></td>
                                    <td><?=$expert->getWorkUnit()?></td>
                                    <td><?=implode(" / ",$expert->getArrayWithSubjectName())?></td>
                                    <td><?=$expert->hasUnAppraiseAssess($group->getId())?>/<?=$expert->noSubmitAssess($group->getId())?>/<?=$expert->hasAppraiseAssess($group->getId())?>
                                        <!-- /<?=$expert->hasGiveUpAppraiseGroup($group->getId())?> -->
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else:?>
                            <tr>
                                <td class="note">还没有分配专家...</td>
                            </tr>
                        <?php endif;?>
                    </table>
                </div>
                <br />
                <div class="box">
                    <table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                        <caption>
                            2、专家短信通知情况：
                        </caption>
                        <?php $shorts = $group->selectMessage()?>
                        <?php if($shorts->getTotal()):?>
                            <tr>
                                <th width="100">手机</th>
                                <th>内容</th>
                                <th width="120">回复</th>
                                <th width="80">发送时间</th>
                                <th width="80">状态</th>
                                <th width="60">删除</th>
                            </tr>
                            <?php while($short = $shorts->getObject()):?>
                                <tr>
                                    <td><?=$short->getMobile()?></td>
                                    <td><?=$short->getMessage()?></td>
                                    <td><?=$short->getReply()?></td>
                                    <td><?=$short->getSendAt()?></td>
                                    <td><?=$short->getState()?></td>
                                    <td><a href="<?=site_url("message/short/delete/id/".$short->getId())?>" onclick="return confirm('删除将不会发送消息，你确定删除？');">删除</a></td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else:?>
                            <tr>
                                <td class="note">还没有发送短信通知...</td>
                            </tr>
                        <?php endif;?>
                    </table>
                </div>
                <br />
                <div class="box">
                    <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                        <caption>
                            3、分组【<?=$group->getGroupSubject()?>】所包括的项目清单如下：
                        </caption>
                        <form name="searchFrom" method="post" action="">
                            <tr>
                                <td colspan="9">
                                    <div class="alert alert-info">关键词：<input id="search" name="search" class="form-control w-auto custom-control-inline" />
                                        <label><input name="field" type="radio" value="subject" checked="checked" />项目名称</label>
                                        <label><input type="radio" name="field" value="corporation_name" />申报单位</label>
                                        <label><input type="radio" name="field" value="user_name" />项目负责人</label>
                                        <!-- <label><input type="radio" name="field" value="project_domain" />项目领域</label> -->
                                        <!-- <label><input type="radio" name="field" value="guide_field" />指南领域</label> -->
                                        <input type="submit" name="Submit4" class="btn btn-info btn-sm" value=" 搜 索 " />
                                    </div>
                                </td>
                            </tr>
                        </form>
                        <form id="validateForm" name="validateForm" method="post" action="<?=site_url("assess/group/setAssessTpl")?>">
                            <?php if($pager->getTotal()):?>
                                <tr>
                                    <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th>详</th>
                                    <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                                    <th ><?=getColumnStr('研究对象','type_id')?></th>
                                    <th ><?=getColumnStr('项目类别','flow_type')?></th>
                                    <th ><?=getColumnStr('项目负责人','user_id')?></th>
                                    <th width="20%"><?=getColumnStr('申报单位','corporation_id')?></th>
                                    <th width="70">评审得分</th>
                                </tr>
                                <?php while($project = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>"<?php if($group->getIsLock()):?> <?php endif;?> /></td>
                                        <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
                                        <td><?=$project->getMark()?><?=link_to("declare/project/show/id/".$project->getProjectId(),$project->getSubject())?></td>
                                        <td><?=$project->getTypeSubject()?></td>
                                        <td><?=$project->getProjectTypeSubject()?></td>
                                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                                        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                                        <td>
                                            <?php
                                                if($group->getOfficeId()>0):
                                            ?>
                                                <?=$project->getScore()?>
                                            <?php else:?>
                                                <?=$project->getCompanyScore()?>
                                            <?php endif;?>
                                        </td>
                                    </tr>
                                    <tr id="show_<?=$project->getId()?>" style="display:none;">
                                        <td colspan="9"><?php include('more_part.php') ?></td>
                                    </tr>
                                <?php endwhile;?>
                                <tr>
                                    <td colspan="9" align="right">
                                        <input name="group_id" type="hidden" id="group_id" value="<?=$group->getId()?>" />&nbsp;<span class="pager_bar">
            <?=$pager->fromto().$pager->navbar(10)?></span>
                                    </td>
                                </tr>
                            <?php else:?>
                                <tr>
                                    <td colspan="8">暂无符合条件的项目...</td>
                                </tr>
                            <?php endif;?>
                        </form>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

