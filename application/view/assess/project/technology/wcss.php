<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td colspan="2" align="center">

        </td>
        <td align="center" style="width: 20%">
            合计
        </td>
        <?php
        for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
            ?>
            <td align="center" style="width: 15%"><?=$i?>年</td>
        <?php
        endfor;
        ?>
        <?php if(!$configs['action']['download']):?>
            <td align="center" style="width: 86px">
                附件
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td colspan="2" align="center">
            微创手术病例数
        </td>
        <td align="center">
            <?=$project->getSumDataByIndexCode('technology_wcss')?>
        </td>
        <?php
        for($year=$project->getStartYear();$year<=$project->getEndYear();$year++):
            $projectData = $project->getDataByIndexCode('technology_wcss',$year);
            ?>
            <td align="center">
                <?=$projectData->getData('user')?>
            </td>
            <?php
            $i++;
        endfor;
        ?>
        <?php if(!$configs['action']['download']):?>
            <td align="center">
                <div class="btn-group-vertical btn-group-sm" role="group">
                    <?php
                    if (!$configs['action']['download']): ?>
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_technology_wcss').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_technology_wcss/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    <?php endif;?>
                </div>
            </td>
        <?php endif;?>
    </tr>
</table>