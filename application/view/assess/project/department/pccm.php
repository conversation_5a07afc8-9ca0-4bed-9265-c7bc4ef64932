<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="center" colspan="2" style="width: 50%">/</td>
        <td align="center" colspan="2">是/否</td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 85px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    foreach ($configs['pccm'] as $k=>$v):
        if($v==0) continue;
        ?>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['pccm.'.$k]?></td>
            <td align="center" colspan="2"><?=getCheckedStr(['是','否'],$project->getDataByIndexCode('department_pccm_'.$k)->getData())?></td>
            <?php if (!$configs['action']['download']): ?>
                <td align="center" style="width: 85px">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_department_pccm_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_department_pccm_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php endforeach;?>
</table>
<?php if ($configs['department']['pcdesc']):?>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <tr>
            <td height="40" align="left" colspan="4">隶属于PCCM科的MICU或RICU，并具备相应的硬件设施、技术和人力资源条件文字说明（500字以内）</td>
        </tr>
        <tr>
            <td colspan="4"><?=showText($project->getTextByIndexCode('department_pcdesc')->getContent())?>&nbsp;</td>
        </tr>
    </table>
<?php endif;?>
<?php if ($configs['department']['pcbed']):?>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <?php if ($configs['action']['edit']): ?>
                <span style="float:right;">
                  <?=Button::setName('编辑')->setUrl(site_url("engine/{$widget_name}/edit/pcbed/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
                </span>
            <?php endif; ?>
        </caption>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%">/</td>
            <td align="center" colspan="2">数据</td>
            <?php if (!$configs['action']['download']): ?>
                <td align="center" style="width: 85px">附件</td>
            <?php endif;?>
        </tr>
        <?php if ($configs['pcbed']['room']):?>
            <tr>
                <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['pcbed.room']?:'重症医学科隔离病房（间）'?></td>
                <td align="center" colspan="2"><?=$project->getDataByIndexCode('department_pcbed_room')->getData()?></td>
                <?php if (!$configs['action']['download']): ?>
                    <td align="center" style="width: 85px">
                        <div class="btn-group-vertical btn-group-sm" role="group">
                            <?=Button::setName('附件('.$project->getAttachementCount('apply_department_pcbed_room').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_department_pcbed_room/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                        </div>
                    </td>
                <?php endif;?>
            </tr>
        <?php endif;?>
        <?php if ($configs['pcbed']['doctor']):?>
            <tr>
                <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['pcbed.doctor']?:'重症医学专业执业医师数量（人）'?></td>
                <td align="center" colspan="2"><?=$project->getDataByIndexCode('department_pcbed_doctor')->getData()?></td>
                <?php if (!$configs['action']['download']): ?>
                    <td align="center" style="width: 85px">
                        <div class="btn-group-vertical btn-group-sm" role="group">
                            <?=Button::setName('附件('.$project->getAttachementCount('apply_department_pcbed_doctor').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_department_pcbed_doctor/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                        </div>
                    </td>
                <?php endif;?>
            </tr>
        <?php endif;?>
        <?php if ($configs['pcbed']['nurse']):?>
            <tr>
                <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['pcbed.nurse']?:'重症医学科护士（人）'?></td>
                <td align="center" colspan="2"><?=$project->getDataByIndexCode('department_pcbed_nurse')->getData()?></td>
                <?php if (!$configs['action']['download']): ?>
                    <td align="center" style="width: 85px">
                        <div class="btn-group-vertical btn-group-sm" role="group">
                            <?=Button::setName('附件('.$project->getAttachementCount('apply_department_pcbed_nurse').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_department_pcbed_nurse/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                        </div>
                    </td>
                <?php endif;?>
            </tr>
        <?php endif;?>
        <?php if ($configs['pcbed']['bed']):?>
            <tr>
                <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['pcbed.bed']?:'重症医学科床位（张）'?></td>
                <td align="center" colspan="2"><?=$project->getDataByIndexCode('department_pcbed_bed')->getData()?></td>
                <?php if (!$configs['action']['download']): ?>
                    <td align="center" style="width: 85px">
                        <div class="btn-group-vertical btn-group-sm" role="group">
                            <?=Button::setName('附件('.$project->getAttachementCount('apply_department_pcbed_bed').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_department_pcbed_bed/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                        </div>
                    </td>
                <?php endif;?>
            </tr>
        <?php endif;?>
        <?php if ($configs['pcbed']['bed_doctor']):?>
            <tr>
                <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['bed.bed_doctor']?:'重症医学专业执业医师与床位比'?></td>
                <td align="center" colspan="2"><?=$project->getDataByIndexCode('department_pcbed_bed_doctor')->getData()?></td>
                <?php if (!$configs['action']['download']): ?>
                    <td align="center" style="width: 85px">
                        -
                    </td>
                <?php endif;?>
            </tr>
        <?php endif;?>
        <?php if ($configs['pcbed']['bed_nurse']):?>
            <tr>
                <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['pcbed.bed_nurse']?:'重症医学科护士与床位比'?></td>
                <td align="center" colspan="2"><?=$project->getDataByIndexCode('department_pcbed_bed_nurse')->getData()?></td>
                <?php if (!$configs['action']['download']): ?>
                    <td align="center" style="width: 85px">
                        -
                    </td>
                <?php endif;?>
            </tr>
        <?php endif;?>

    </table>
<?php endif;?>
