<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="center" colspan="2" style="width: 65%">/</td>
        <td align="center" colspan="2"><?=$project->getDeclareYear()?>年</td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 85px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    $i=0;
    foreach ($configs['mdt'] as $k=>$isChecked):
        if($isChecked==0) continue;
        $name = $configs['langs']['mdt.'.$k];
        ?>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%"><?=$name?></td>
            <td align="center" colspan="2"><?=$project->getDataByIndexCode('department_'.$k,$project->getDeclareYear())->getData()?></td>
            <?php if (!$configs['action']['download']): ?>
                <td align="center" style="width: 85px">
                    <?php
                    if (!$configs['action']['download']): ?>
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_department_mdt_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_department_mdt_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    <?php endif;?>
                </td>
            <?php endif;?>
        </tr>
    <?php
    endforeach;
    ?>
</table>