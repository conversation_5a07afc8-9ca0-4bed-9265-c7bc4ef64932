<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="left" colspan="2" style="width: 60%">1.对住院患者是否进行血栓和出血风险评估
        </td>
        <td colspan="2" align="center" ><?=getCheckedStr(['是','否'],$project->getDataByIndexCode('safety_vte_fxpg')->getData('user'))?></td>
        <?php
        $itemType = 'vte1';
        if (!$configs['action']['download'] && $configs['needfile']['common']['vte']): ?>
            <td align="center" style="width: 86px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_safety_common_'.$itemType).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_safety_'.$itemType.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td height="40" align="left" colspan="2">2.对血栓高风险病例是否进行预防深静脉血栓处理
        </td>
        <td colspan="2" align="center" ><?=getCheckedStr(['是','否'],$project->getDataByIndexCode('safety_vte_xscl')->getData('user'))?></td>
        <?php
        $itemType = 'vte2';
        if (!$configs['action']['download'] && $configs['needfile']['common']['vte']): ?>
            <td align="center">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_safety_common_'.$itemType).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_safety_'.$itemType.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
</table>
<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td><b>3.简述预防深静脉血栓的措施与疗程</b>（500字以内）</td>
    </tr>
    <tr>
        <td><?=showText($project->getTextByIndexCode('safety_vte_content')->getContent())?>&nbsp;</td>
    </tr>
</table>