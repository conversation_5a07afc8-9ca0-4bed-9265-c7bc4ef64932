<table class="table table-bordered">
    <tbody>
    <tr>
        <td colspan="2" align="center" style="width: 20%">

        </td>
        <td align="center" style="width: 20%">
            均值
        </td>
        <?php
        for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
            ?>
            <td align="center" style="width: 20%"><?=$i?>年</td>
        <?php
        endfor;
        ?>
    </tr>
    <tr>
        <td colspan="2" align="center">
            <?=ltrim(str_replace('（','<br>（',$configs['langs']['common.zdfx']),'<br>')?>
        </td>
        <td align="center">
            <?=$project->getAvgDataByIndexCode('safety_zdfx')?>
        </td>
        <?php
        for($year=$project->getStartYear();$year<=$project->getEndYear();$year++):
            $projectData = $project->getDataByIndexCode('safety_zdfx',$year);
            ?>
            <td align="center">
                <?=$projectData->getData()?>
            </td>
        <?php endfor;?>
    </tr>
    <?php
    if($project->isCityCountryLevel()):
        ?>
        <tr>
            <td colspan="2" align="center">全省该专科三甲医院平均水平</td>
            <td align="center"><?=$project->getProvinceAvgData('safety_zdfx')?></td>
            <td colspan="3" align="center"></td>
        </tr>
    <?php else:?>
        <tr>
            <td colspan="2" align="center">全省该专科平均水平</td>
            <td align="center"><?=$project->getProvinceAvgData('safety_zdfx')?></td>
            <td align="center"><?=$project->getProvinceAvgData('safety_zdfx',[$project->getStartYear()])?></td>
            <td align="center"><?=$project->getProvinceAvgData('safety_zdfx',[$project->getEndYear()-1])?></td>
            <td align="center"><?=$project->getProvinceAvgData('safety_zdfx',[$project->getEndYear()])?></td>
        </tr>
    <?php endif;?>
    </tbody>
</table>