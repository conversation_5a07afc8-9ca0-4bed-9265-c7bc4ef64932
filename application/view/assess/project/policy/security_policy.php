<table class="table table-bordered">
    <tr>
        <th>内容</th>
        <th style="width: 90px" align="center">附件</th>
    </tr>
    <tr>
        <td><?=showText($project->getTextByIndexCode('policy_plan')->getContent())?></td>
        <td><?=Button::setName('附件('.$project->getAttachementCount('apply_policy_security_policy').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_policy_security_policy/id/'.$project->getProjectId().'/is_show/1'))->setClass('btn-alt-success')->window()?></td>
    </tr>
</table>