<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tbody>
    <tr>
        <td align="center" style="width: 45%">
            医疗区布局情况
        </td>
        <td align="center">
            数值
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    $jjzls = [
        ['index'=>1,'name'=>'是否具有与“120”中心的合作协议','isYesNo'=>true],
        ['index'=>2,'name'=>'积极参与院前急救工作，急诊科接到调度中心指令3分钟出车率','isYesNo'=>false],
        ['index'=>3,'name'=>'积极参与院前急救工作，急救反应时间（城区）','isYesNo'=>false],
        ['index'=>4,'name'=>'积极参与院前急救工作，急救反应时间（郊区）','isYesNo'=>false],
    ];
    foreach ($jjzls as $jjzl):
        $k = $jjzl['index'];
        $name = $jjzl['name'];
        $isYesNo = $jjzl['isYesNo'];
        ?>
        <tr>
            <td align="center">
                （<?=$k?>）<?=$name?>
            </td>
            <td align="center">
                <?php
                if($isYesNo):
                    ?>
                    <?=getCheckedStr(['是','否'],$project->getDataByIndexCode('prehospital_jzk_'.$k)->getData('user'))?>
                <?php
                else:
                    ?>
                    <?=(int)$project->getDataByIndexCode('prehospital_jzk_'.$k)->getData('user')?>
                <?php endif;?>
            </td>
            <?php
            $itemType = 'jzk';
            if (!$configs['action']['download'] && $configs['needfile']['prehospital'][$itemType]): ?>
                <td align="center" style="width: 85px">
                    <?=Button::setName('附件('.$project->getAttachementCount('apply_prehospital_'.$itemType.'_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_prehospital_'.$itemType.'_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                </td>
            <?php endif;?>
        </tr>
    <?php
    endforeach;
    ?>
    </tbody>
</table>
