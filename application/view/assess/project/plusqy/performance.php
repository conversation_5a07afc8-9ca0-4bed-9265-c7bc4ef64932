<?php
$worker = $project->worker();
$configs = $worker->getConfigs('requirement');
?>
<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="center" colspan="2" style="width: 50%">绩效考核</td>
        <td align="center" colspan="2">等级或排名</td>
    </tr>
    <tr>
        <td height="40" align="center" colspan="2" style="width: 50%">公立医院绩效考核中“国考”结果等级<br>（最近1次）</td>
        <td align="center" colspan="2"><?=$project->getDataByIndexCode('requirement_performance_nation')->getData()?></td>
    </tr>
    <tr>
        <td height="40" align="center" colspan="2" style="width: 50%">公立医院绩效考核中“省考”同类机构排名<br>（最近1次）</td>
        <td align="center" colspan="2"><?=$project->getDataByIndexCode('requirement_performance_province')->getData()?></td>
    </tr>
    <?php
    if($configs['performance']['fygk']):
        $name = $configs['langs']['performance.fygk']?:'妇幼保健机构绩效考核中“国考”结果等级（最近1次）';
        $name = ltrim(str_replace('（','<br>（',$name),'<br>');
        ?>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%"><?=$name?></td>
            <td align="center" colspan="2"><?=$project->getDataByIndexCode('requirement_performance_fygk')->getData()?></td>
        </tr>
    <?php endif;?>
    <?php
    if($configs['performance']['fysk']):
        $name = $configs['langs']['performance.fysk']?:'妇幼保健机构绩效考核中“省考”同类机构排名百分比（最近1次）（%）';
//                $name = ltrim(str_replace('（','<br>（',$name),'<br>');
        ?>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%"><?=$name?></td>
            <td align="center" colspan="2"><?=$project->getDataByIndexCode('requirement_performance_fysk')->getData()?></td>
        </tr>
    <?php endif;?>
</table>