<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="left" colspan="4"><?=$configs['readme']['mszb']?></td>
        <td height="40" align="center" style="width: 85px">附件</td>
    </tr>
    <tr>
        <td colspan="4"><?=showText($project->getTextByIndexCode('pubprevent_mszb')->getContent())?>&nbsp;</td>
        <td align="center" style="vertical-align: middle">
            <?=Button::setName('附件('.$project->getAttachementCount('apply_pubprevent_mszb').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_pubprevent_mszb/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
        </td>
    </tr>
</table>