<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td align="center" style="width: 50px">序号</td>
        <td align="center" style="width: 80px">姓名</td>
        <td align="center">防治工作名称<br>或内容</td>
        <td align="center" style="width: 70px">防治工作<br>级别</td>
        <td align="center" style="width: 60px">担任<br>职务</td>
        <td align="center" style="width: 70px">时间</td>
        <?php if(!$configs['action']['download'] || $configs['action']['edit']):?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    $works = $project->works('fzgz');
    while($work = $works->getObject()):
        ?>
        <tr>
            <td align="center"><?=$works->getIndex()?></td>
            <td align="center"><?=$work->getUserName()?></td>
            <td><?=$work->getSubject()?></td>
            <td align="center"><?=$work->getLevel()?></td>
            <td align="center"><?=$work->getDuty()?></td>
            <td align="center"><?=$work->getDate()?></td>
            <?php if(!$configs['action']['download'] || $configs['action']['edit']):?>
                <td align="center">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?php if($configs['action']['edit']):?>
                            <?=Button::setName('编辑')->setUrl(site_url("engine/pubprevent/fzgz/edit/engine/".$configs['engine']."/pid/".$work->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-primary')->widget()?>
                            <?=Button::setName('删除')->setUrl(site_url("engine/pubprevent/fzgz/remove/engine/".$configs['engine']."/pid/".$work->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-danger')->widget()?>
                        <?php endif;?>
                        <?php
                        $itemType = 'fzgz';
                        if (!$configs['action']['download']): ?>
                            <?=Button::setName('附件('.$project->getAttachementCount('apply_pubprevent_'.$itemType.'_'.$work->getId()).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_pubprevent_'.$itemType.'_'.$work->getId().'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                        <?php endif;?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php
    endwhile;
    ?>
    <?php if($works->getTotal()==0):?>
        <tr>
            <td colspan="<?=!$configs['action']['download']?7:6?>" align="center">无</td>
        </tr>
    <?php endif;?>
</table>
<p>（近三年，担任市级及以上相关呼吸道传染病防控专家组长或副组长，指导区域内呼吸道传染病防治工作。）</p>