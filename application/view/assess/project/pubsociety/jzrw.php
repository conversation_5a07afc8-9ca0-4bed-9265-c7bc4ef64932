<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td align="center">序号</td>
        <td align="center">任务名称或内容</td>
        <td align="center">任务执行周期</td>
        <td align="center">执行人数</td>
        <td align="center">备注</td>
        <?php if(!$configs['action']['download'] || $configs['action']['edit']):?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    $missions = $project->missions();
    while($mission = $missions->getObject()):
        ?>
        <tr>
            <td align="center"><?=$missions->getIndex()?></td>
            <td><?=$mission->getSubject()?></td>
            <td align="center"><?=$mission->getPeriod()?></td>
            <td align="center"><?=$mission->getNnt()?></td>
            <td><?=$mission->getNote()?></td>
            <?php if(!$configs['action']['download'] || $configs['action']['edit']):?>
                <td align="center">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?php if($configs['action']['edit']):?>
                            <?=Button::setName('编辑')->setUrl(site_url("engine/pubsociety/jzrw/edit/engine/".$configs['engine']."/pid/".$mission->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-primary')->widget()?>
                            <?=Button::setName('删除')->setUrl(site_url("engine/pubsociety/jzrw/remove/engine/".$configs['engine']."/pid/".$mission->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-danger')->widget()?>
                        <?php endif;?>
                        <?php
                        $itemType = 'jzrw';
                        if (!$configs['action']['download']): ?>
                            <?=Button::setName('附件('.$project->getAttachementCount('apply_pubsociety_'.$itemType.'_'.$mission->getId()).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_pubsociety_'.$itemType.'_'.$mission->getId().'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                        <?php endif;?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php
    endwhile;
    ?>
    <?php if($missions->getTotal()==0):?>
        <tr>
            <td colspan="<?=!$configs['action']['download']?6:5?>" align="center">无</td>
        </tr>
    <?php endif;?>
</table>