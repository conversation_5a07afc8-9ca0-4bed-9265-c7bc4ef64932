<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="center" colspan="2" style="width: 50%">/</td>
        <td align="center" colspan="2">数据</td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 85px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    foreach ($configs['kh'] as $k=>$v):
        if($v==0) continue;
        if($k=='tgl') continue;
        ?>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['kh.'.$k]?></td>
            <td align="center" colspan="2"><?=getCheckedStr(['是','否'],$project->getDataByIndexCode('edulevel_kh_'.$k)->getData())?></td>
            <?php if (!$configs['action']['download']): ?>
                <td align="center" style="width: 85px">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_edulevel_kh_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_edulevel_kh_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php endforeach;?>
    <?php
    foreach ($configs['kh'] as $k=>$v):
        if($k!='tgl') continue;
        ?>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['kh.'.$k]?></td>
            <td align="center" colspan="2"><?=$project->getDataByIndexCode('edulevel_kh_'.$k)->getData()?></td>
            <?php if (!$configs['action']['download']): ?>
                <td align="center" style="width: 85px">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_edulevel_kh_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_edulevel_kh_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php endforeach;?>
</table>