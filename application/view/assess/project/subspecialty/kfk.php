<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tbody>
    <tr>
        <td align="center">
            亚专科设置
        </td>
        <td align="center">
            神经早期<br>床旁康复
        </td>
        <td align="center">
            神经康复<br>住院病区
        </td>
        <td align="center">
            神经康复<br>医师<br>（人）
        </td>
        <td align="center">
            神经康复<br>治疗师<br>（人）
        </td>
        <td align="center">
            神经康复<br>护士<br>（人）
        </td>
        <td align="center">
            神经康复<br>患者占住院<br>患者比例<br>（%）
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            神经康复
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_sjkf_cpkf')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_sjkf_brbq')->getData('user'))?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_sjkf_ys')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_sjkf_zls')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_sjkf_hs')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_sjkf_hzbl')->getData('user')?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_sjkf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_sjkf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">

        </td>
        <td align="center">
            骨科早期<br>床旁康复
        </td>
        <td align="center">
            骨科康复<br>住院病区
        </td>
        <td align="center">
            骨科康复<br>医师<br>（人）
        </td>
        <td align="center">
            骨科康复<br>治疗师<br>（人）
        </td>
        <td align="center">
            骨科康复<br>护士<br>（人）
        </td>
        <td align="center">
            骨科康复患者占住院<br>患者比例<br>（%）
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            骨科康复
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_gkkf_cpkf')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_gkkf_brbq')->getData('user'))?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_gkkf_ys')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_gkkf_zls')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_gkkf_hs')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_gkkf_hzbl')->getData('user')?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_gkkf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_gkkf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">

        </td>
        <td align="center" colspan="2">
            重症康复<br>（含心脏内科、外科、呼吸、重症医学科等）
        </td>
        <td align="center" colspan="2">
            重症康复<br>医师（含重症医学科医师共同参与亦可）<br>（人）
        </td>
        <td align="center">
            重症康复<br>治疗师<br>（人）
        </td>
        <td align="center">
            重症康复<br>护士<br>（人）
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            重症康复
        </td>
        <td align="center" colspan="2">
            <?=$project->getDataByIndexCode('subspecialty_kfk_zzkf_yw')->getData('user')=='有' ? '☑' : '□'?> 有，含：<u>&nbsp;<?=$project->getDataByIndexCode('subspecialty_kfk_zzkf_yw_desc')->getData('user')?>&nbsp;</u><br>
            <?=$project->getDataByIndexCode('subspecialty_kfk_zzkf_yw')->getData('user')=='无' ? '☑' : '□'?> 无
        </td>
        <td align="center" colspan="2">
            <?=$project->getDataByIndexCode('subspecialty_kfk_zzkf_ys')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_zzkf_zls')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_zzkf_hs')->getData('user')?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_zzkf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_zzkf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">

        </td>
        <td align="center" colspan="2">
            呼吸康复
        </td>
        <td align="center" colspan="2">
            呼吸康复<br>医师（含呼吸科医师共同参与亦可）<br>（人）
        </td>
        <td align="center">
            呼吸康复<br>治疗师<br>（人）
        </td>
        <td align="center">
            呼吸康复<br>护士<br>（人）
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            呼吸康复
        </td>
        <td align="center" colspan="2">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_hxkf_yw')->getData('user'))?>
        </td>
        <td align="center" colspan="2">
            <?=$project->getDataByIndexCode('subspecialty_kfk_hxkf_ys')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_hxkf_zls')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_hxkf_hs')->getData('user')?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_hxkf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_hxkf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">

        </td>
        <td align="center" colspan="2">
            烧伤及皮肤康复
        </td>
        <td align="center" colspan="2">
            烧伤康复<br>医师（含烧伤科医师共同参与亦可）<br>（人）
        </td>
        <td align="center">
            烧伤及皮肤康复<br>治疗师<br>（人）
        </td>
        <td align="center">
            烧伤康复<br>护士<br>（人）
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            烧伤及皮肤康复
        </td>
        <td align="center" colspan="2">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_sskf_yw')->getData('user'))?>
        </td>
        <td align="center" colspan="2">
            <?=$project->getDataByIndexCode('subspecialty_kfk_sskf_ys')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_sskf_zls')->getData('user')?>
        </td>
        <td align="center">
            <?=$project->getDataByIndexCode('subspecialty_kfk_sskf_hs')->getData('user')?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_sskf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_sskf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">

        </td>
        <td align="center" colspan="2">
            骨骼肌肉系统
        </td>
        <td align="center">
            呼吸系统
        </td>
        <td align="center">
            消化系统
        </td>
        <td align="center">
            心血管系统
        </td>
        <td align="center">
            神经系统
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            老年康复
        </td>
        <td align="center" colspan="2">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_lnkf_ggjr')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_lnkf_hxxt')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_lnkf_xhxt')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_lnkf_xxg')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_lnkf_sjxt')->getData('user'))?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_lnkf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_lnkf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">

        </td>
        <td align="center" colspan="2">
            儿童神经
        </td>
        <td align="center">
            生长发育
        </td>
        <td align="center">
            疾病康复
        </td>
        <td align="center">
            眼科康复
        </td>
        <td align="center">
            心理康复
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            儿童康复
        </td>
        <td align="center" colspan="2">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_etkf_etsj')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_etkf_szfy')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_etkf_jbkf')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_etkf_ykkf')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_etkf_xlkf')->getData('user'))?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_etkf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_etkf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">

        </td>
        <td align="center">
            心胸外科
        </td>
        <td align="center">
            胃肠外科
        </td>
        <td align="center">
            肝胆外科
        </td>
        <td align="center">
            乳腺外科
        </td>
        <td align="center">
            泌尿外科
        </td>
        <td align="center">
            其他
        </td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <tr>
        <td align="center">
            加速外科康复
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_wkkf_xxwk')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_wkkf_wcwk')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_wkkf_gdwk')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_wkkf_rxwk')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_wkkf_mnwk')->getData('user'))?>
        </td>
        <td align="center">
            <?=getCheckedStr(['有','无'],$project->getDataByIndexCode('subspecialty_kfk_wkkf_qt')->getData('user'))?>
        </td>
        <?php
        $itemType = 'kfk';
        if (!$configs['action']['download'] && $configs['needfile']['subspecialty'][$itemType]): ?>
            <td align="center" style="width: 85px">
                <?=Button::setName('附件('.$project->getAttachementCount('apply_subspecialty_'.$itemType.'_wkkf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_subspecialty_'.$itemType.'_wkkf'.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            </td>
        <?php endif;?>
    </tr>
    </tbody>
</table>
