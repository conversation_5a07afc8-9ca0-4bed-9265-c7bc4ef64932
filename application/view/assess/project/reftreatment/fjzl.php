<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="left" colspan="4">建立符合心血管疾病诊疗特点的分级诊疗体系（500字以内）</td>
        <td height="40" align="center" style="width: 85px">附件</td>
    </tr>
    <tr>
        <td colspan="4"><?=showText($project->getTextByIndexCode('reftreatment_fjzl')->getContent())?>&nbsp;</td>
        <td align="center" style="vertical-align: middle">
            <?=Button::setName('附件('.$project->getAttachementCount('apply_reftreatment_fjzl').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_reftreatment_fjzl/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
        </td>
    </tr>
</table>