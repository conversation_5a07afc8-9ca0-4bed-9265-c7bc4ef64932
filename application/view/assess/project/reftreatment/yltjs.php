<?php
foreach ($configs['yltjs'] as $k=>$isChecked):
    if($isChecked==0) continue;
    ?>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <span style="float:right;">
            <?php if (!$configs['action']['download']): ?>
                <?=Button::setName('附件('.$project->getAttachementCount('apply_reftreatment_yltjs_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_reftreatment_yltjs_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
            <?php endif;?>
            <?php if ($configs['action']['edit']): ?>
                <?=Button::setName('编辑')->setUrl(site_url("engine/{$widget_name}/edit/".$k."/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
            <?php endif;?>
            </span>
        </caption>
        <tr>
            <td height="40" align="left" colspan="4"><?=$configs['langs']['yltjs.'.$k]?>（500字以内）</td>
        </tr>
        <tr>
            <td colspan="4"><?=showText($project->getTextByIndexCode('reftreatment_'.$k)->getContent())?>&nbsp;</td>
        </tr>
    </table>
<?php endforeach;?>