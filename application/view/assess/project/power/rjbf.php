<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <caption>
        <span style="float:left;"><?=$configs['langs']['power.rjbf']?:'5.日间病房设置'?></span>
        <span style="float:right;">
                    <?php if (!$configs['action']['download']): ?>
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_power_rjbf').')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_power_rjbf/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    <?php endif;?>
                    <?php if ($configs['action']['edit']): ?>
                        <?=Button::setName('编辑')->setUrl(site_url("engine/{$widget_name}/edit/rjbf/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
                    <?php endif; ?>
                </span>
    </caption>
    <tr>
        <td height="40" align="center" colspan="2" style="width: 50%">/</td>
        <td align="center" colspan="2">是否开展</td>
    </tr>
    <?php
    foreach ($configs['rjbf'] as $k=>$v):
        if($v==0) continue;
        ?>
        <tr>
            <td height="40" align="center" colspan="2" style="width: 50%"><?=$configs['langs']['rjbf.'.$k]?></td>
            <td align="center" colspan="2">
                <?php
                $data = $project->getDataByIndexCode('power_rjbf_'.$k)->getData();
                ?>
                <?=getCheckedStr(['是','否'],$data)?>
            </td>
        </tr>
    <?php endforeach;?>
</table>