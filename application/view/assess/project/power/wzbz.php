<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
        <td height="40" align="center" style="width: 25%">&nbsp;</td>
        <td align="center" colspan="3">病种清单</td>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 85px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    foreach ($configs['wzbz'] as $k=>$v):
        if($v==0) continue;
        if($k!='wzbz') continue;
        $datas = $project->getTextByIndexCode('power_wzbz_child')->getArr();
        $totalCount = $project->getSzxWzbzCount();
        $selCount = count($datas);
        $percent = round($selCount/$totalCount*100,2);
        ?>
        <tr>
            <td height="40" align="center"><?=$configs['langs']['wzbz.'.$k]?></td>
            <td align="center" colspan="3">共<?=$project->getSzxWzbzCount()?>个，已勾选
                <?php if (!$configs['action']['download']): ?>
                    <?=Button::setUrl(site_url("engine/power/edit/wzbzshow/engine/".$configs['engine']."/id/" . $project->getProjectId()))->window($selCount)?>
                <?php else:?>
                    <?=$selCount?>
                <?php endif; ?>个，覆盖率：<?=$percent?>%
            </td>
            <?php if (!$configs['action']['download']): ?>
                <td align="center" style="width: 85px">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_power_wzbz_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_power_wzbz_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php endforeach;?>
    <?php
    foreach ($configs['wzbz'] as $k=>$v):
        if($v==0) continue;
        if($k=='wzbz') continue;
        ?>
        <tr>
            <td height="40" align="center"><?=$configs['langs']['wzbz.'.$k]?></td>
            <td align="center" colspan="3"><?=$project->getDataByIndexCode('power_wzbz_'.$k)->getData()?></td>
            <?php if (!$configs['action']['download']): ?>
                <td align="center" style="width: 85px">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?=Button::setName('附件('.$project->getAttachementCount('apply_power_wzbz_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_power_wzbz_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php endforeach;?>
</table>