<?php
if($configs['feature']['restrict']):
    ?>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <span style="float:left;"><?=$configs['langs']['feature.restrict']?:'（1）本专科开展限制类技术情况'?></span>
            <?php if ($configs['action']['edit']): ?>
                <span style="float:right;">
<?=Button::setName('新增')->setUrl(site_url("engine/feature/edit/restrict/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
</span>
            <?php endif; ?>
        </caption>
    </table>
    <?=getReadMe($configs,'restrict')?>
    <?php
    $technologys = $project->technologys('restrict');
    $options = ['国家级','省级'];
    if($project->isCityCountryLevel()){
        $options = ['国家级','省级','市级','县级'];
    }
    while($technology = $technologys->getObject()):
        ?>
        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
            <tr>
                <td colspan="2" align="center" style="vertical-align: middle;width: 150px">限制类技术名称</td>
                <td colspan="2" align="center">限制类技术级别</td>
                <td align="center">开展例数</td>
                <?php if(!$configs['action']['download']):?>
                    <td align="center" style="width: 86px">操作</td>
                <?php endif;?>
            </tr>
            <tr>
                <td colspan="2"><?=$technology->getSubject()?></td>
                <td colspan="2" align="center"><?=getCheckedStr(['国家级','省级'],$technology->getLevel())?></td>
                <td align="center"><?=$technology->getCase('kzls')?></td>
                <?php if(!$configs['action']['download']):?>
                    <td align="center" rowspan="4">
                        <div class="btn-group-vertical btn-group-sm" role="group">
                            <?php if($configs['action']['edit']):?>
                                <?=Button::setName('编辑')->setUrl(site_url("engine/feature/edit/restrict/engine/".$configs['engine']."/sid/".$technology->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-primary')->widget()?>
                                <?=Button::setName('删除')->setUrl(site_url("engine/feature/edit/remove/engine/".$configs['engine']."/sid/".$technology->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-danger')->widget()?>
                            <?php endif;?>
                            <?php
                            $itemType = 'restrict';
                            if (!$configs['action']['download'] && $configs['needfile']['feature'][$itemType]): ?>
                                <?=Button::setName('附件('.$project->getAttachementCount('apply_feature_'.$itemType.'_'.$technology->getId()).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_feature_'.$itemType.'_'.$technology->getId().'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                            <?php endif;?>
                        </div>
                    </td>
                <?php endif;?>
            </tr>
            <tr>
                <td colspan="5">限制类技术开展情况（300字以内）：</td>
            </tr>
            <tr>
                <td colspan="5"><?=showText($technology->getContent())?>&nbsp;</td>
            </tr>
        </table>
    <?php
    endwhile;
    if($technologys->getTotal()==0):
        ?>
        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
            <tr>
                <td colspan="2" align="center" style="vertical-align: middle;width: 150px">限制类技术名称</td>
                <td colspan="2" align="center">限制类技术级别</td>
                <td align="center">开展例数</td>
            </tr>
            <tr>
                <td colspan="2">&nbsp;</td>
                <td colspan="2" align="center"><?=getCheckedStr(['国家级','省级'],'')?></td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="5">限制类技术开展情况（300字以内）：</td>
            </tr>
            <tr>
                <td colspan="5">&nbsp;</td>
            </tr>
        </table>
    <?php endif;?>
<?php endif;?>
<?php
if($configs['feature']['common']):
    ?>
    <?php
    if($configs['langs']['feature.common']=='通用') $configs['langs']['feature.common'] = $configs['langs']['feature'];
    ?>
    <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
        <caption>
            <span style="float:left;"><?=$configs['langs']['feature.common']?:'2.技术特色'?></span>
            <?php if ($configs['action']['edit']): ?>
                <span style="float:right;">
<?=Button::setName('新增')->setUrl(site_url("engine/feature/edit/index/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
</span>
            <?php endif; ?>
        </caption>
    </table>
    <?=getReadMe($configs,'common')?>
    <?php
    $technologys = $project->technologys();
    $options = ['国际领先','国际先进','国内领先','国内先进','省内领先','省内先进'];
    if($project->isCityCountryLevel()){
        $options = ['国际领先','国际先进','国内领先','国内先进','省内领先','省内先进','市内领先','市内先进'];
    }
    while($technology = $technologys->getObject()):
        ?>
        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
            <tr>
                <td colspan="2" rowspan="2" align="center" style="vertical-align: middle;width: 150px">特色技术名称</td>
                <td colspan="3" align="center">近3年开展例数</td>
                <?php if(!$configs['action']['download']):?>
                    <td align="center" rowspan="2" style="width: 86px">操作</td>
                <?php endif;?>
            </tr>
            <tr>
                <?php
                for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
                    ?>
                    <td align="center"><?=$i?></td>
                <?php
                endfor;
                ?>
            </tr>
            <tr>
                <td colspan="2" rowspan="3"><?=$technology->getSubject()?></td>
                <?php
                for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
                    ?>
                    <td align="center"><?=$technology->getCase('y'.$i)?></td>
                <?php
                endfor;
                ?>
                <?php if(!$configs['action']['download']):?>
                    <td align="center" rowspan="5">
                        <div class="btn-group-vertical btn-group-sm" role="group">
                            <?php if($configs['action']['edit']):?>
                                <?=Button::setName('编辑')->setUrl(site_url("engine/feature/edit/index/engine/".$configs['engine']."/sid/".$technology->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-primary')->widget()?>
                                <?=Button::setName('删除')->setUrl(site_url("engine/feature/edit/remove/engine/".$configs['engine']."/sid/".$technology->getId()."/id/".$project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-danger')->widget()?>
                            <?php endif;?>
                            <?php
                            $itemType = 'common';
                            if (!$configs['action']['download'] && $configs['needfile']['feature'][$itemType]): ?>
                                <?=Button::setName('附件('.$project->getAttachementCount('apply_feature_'.$itemType.'_'.$technology->getId()).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_feature_'.$itemType.'_'.$technology->getId().'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                            <?php endif;?>
                        </div>
                    </td>
                <?php endif;?>
            </tr>
            <tr>
                <td colspan="3" align="center">特色技术先进性</td>
            </tr>
            <tr>
                <td colspan="3"><?=getCheckedStr($options,$technology->getLevel())?></td>
            </tr>
            <tr>
                <td colspan="5">特色技术开展情况（300字以内）：</td>
            </tr>
            <tr>
                <td colspan="5"><?=showText($technology->getContent())?>&nbsp;</td>
            </tr>
        </table>
    <?php
    endwhile;
    if($technologys->getTotal()==0):
        ?>
        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
            <tr>
                <td colspan="2" rowspan="2" align="center" style="vertical-align: middle;width: 150px">特色技术名称</td>
                <td colspan="3" align="center">近3年开展例数</td>
            </tr>
            <tr>
                <?php
                for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
                    ?>
                    <td align="center"><?=$i?></td>
                <?php
                endfor;
                ?>
            </tr>
            <tr>
                <td colspan="2" rowspan="3">&nbsp;</td>
                <?php
                for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
                    ?>
                    <td align="center">&nbsp;</td>
                <?php
                endfor;
                ?>
            </tr>
            <tr>
                <td colspan="3" align="center">特色技术先进性</td>
            </tr>
            <tr>
                <td colspan="3"><?=getCheckedStr($options,'')?></td>
            </tr>
            <tr>
                <td colspan="5">特色技术开展情况（300字以内）：</td>
            </tr>
            <tr>
                <td colspan="5">&nbsp;</td>
            </tr>
        </table>
    <?php endif;?>
<?php endif;?>
