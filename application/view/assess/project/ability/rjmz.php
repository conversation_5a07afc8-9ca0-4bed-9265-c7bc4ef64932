<table class="table table-bordered">
    <tbody>
    <tr>
        <td colspan="2" align="center" style="width: 20%">

        </td>
        <td align="center" style="width: 20%">
            均值
        </td>
        <?php
        for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
            ?>
            <td align="center" style="width: 20%"><?=$i?>年</td>
        <?php
        endfor;
        ?>
    </tr>
    <tr>
        <td colspan="2" align="center">
            <?=ltrim(str_replace('（','<br>（',$configs['langs']['common.rjmz']),'<br>')?>
        </td>
        <td align="center">
            <?=$project->getAvgDataByIndexCode('ability_rjmz')?>
        </td>
        <?php
        for($year=$project->getStartYear();$year<=$project->getEndYear();$year++):
        $projectData = $project->getDataByIndexCode('ability_rjmz',$year);
        ?>
        <td align="center">
            <?=$projectData->getData()?>
        </td>
        <?php endfor;?>
    </tr>
    </tbody>
</table>