<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <caption>
        <?php if ($configs['action']['edit']): ?>
            <span style="float:right;">
<?=Button::setName('编辑')->setUrl(site_url("engine/ability/blk/edit/engine/".$configs['engine']."/id/" . $project->getProjectId()))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setIcon('edit')->widget()?>
</span>
        <?php endif; ?>
        <?=getScoreButton($project->getProjectId(),'ability_blk')?>
    </caption>
    <tbody>
    <tr>
        <td colspan="2" align="center">

        </td>
        <td align="center" style="width: 20%">
            均值
        </td>
        <?php
        for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
            ?>
            <td align="center" style="width: 15%"><?=$i?>年</td>
        <?php
        endfor;
        ?>
        <?php if(!$configs['action']['download']):?>
            <td align="center" style="width: 86px">
                附件
            </td>
        <?php endif;?>
    </tr>
    <?php
    $i=0;
    $indexs = ['blk2'=>'各项分子病理室间质评合格率（%）'];
    foreach ($indexs as $k=>$name):
        ?>
        <tr>
            <td colspan="2" align="center">
                <?=ltrim(str_replace('（','<br>（',$name),'<br>')?>
            </td>
            <td align="center">
                <?=$project->getAvgDataByIndexCode('ability_'.$k,'','user')?>
            </td>
            <?php
            for($year=$project->getStartYear();$year<=$project->getEndYear();$year++):
                $projectData = $project->getDataByIndexCode('ability_'.$k,$year);
                ?>
                <td align="center">
                    <?=$projectData->getData('user')?>
                </td>
                <?php
                $i++;
            endfor;
            ?>
            <?php if(!$configs['action']['download']):?>
                <td align="center">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <?php
                        if (!$configs['action']['download'] && $configs['needfile']['blk']): ?>
                            <?=Button::setName('附件('.$project->getAttachementCount('apply_ability_blk_'.$k).')')->setUrl(site_url('engine/attachement/upload/index/item_type/apply_ability_blk_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                        <?php endif;?>
                    </div>
                </td>
            <?php endif;?>
        </tr>
    <?php
    endforeach;
    ?>
    </tbody>
</table>