<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                复制评审表 - <?=$rule->getSubject()?>
            </h3>
            <div class="block-options">
                <?=Button::setUrl(site_url('assess/rulemanager/index'))->setIcon('back')->link('返回')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <input name="id" type="hidden" id="id" value="<?=$rule->getRuleId()?>" />
                        <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-bordered">
                            <caption>基本信息</caption>
                            <tr>
                                <th width="110">评分表名 <em>*</em></th>
                                <td colspan="2"><input name="subject" type="text" class="form-control required" id="subject" value="<?=$rule->getSubject()?>" size="36" placeholder="XXX评分表"/>
                                </td>
                            </tr>
                            <tr>
                                <th>标记</th>
                                <td colspan="2"><input name="code" type="text" class="form-control required" id="code" value="<?=strtolower($rule->getSubjectCode())?><?=$rule->getMaxId()?>" size="20"/>（要求唯一）</td>
                            </tr>
                            <tr>
                                <th>专科代码</th>
                                <td colspan="2"><input name="subject_code" type="text" class="form-control required" id="code" value="<?=strtoupper($rule->getSubjectCode())?>" size="20"/></td>
                            </tr>
                            <tr>
                                <td colspan="3" align="center">
                                    <?=Button::setType('submit')->setEvent('btnLoading(this)')->setIcon('fa fa-check-circle')->setSize('btn-lg')->button('保存评分表')?>
                                </td>
                            </tr>
                        </table>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1.3rem;height: 1.3rem"></span> 正在处理...');
        validateForm.submit();
    }
</script>