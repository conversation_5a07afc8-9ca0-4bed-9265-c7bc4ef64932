<div class="col-xs-12 col-sm-12 col-md-12">
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
    <table width="100%" cellpadding="3" cellspacing="1" class="table table-bordered">
      <caption>评审指标导入</caption>
        <tr>
          <th width="9%">评分表</th>
          <td width="91%"><select class="form-control" onchange="get_ruleitem_list(this.value)">
          <option value="0">===选择评审表===</option>
            <?php while($rule = $rules->getObject()):?>
            <option value="<?=$rule->getId()?>">
              <?=$rule->getSubject()?>-<?=$rule->getCode()?>
              </option>
            <?php endwhile;?>
          </select></td>
        </tr>
        <tr>
          <th>评审指标列表<input class="btn btn-alt-primary" type="button" name="button" id="selectAll" value="全选" /></th>
          <td id="ruleitem_list">&nbsp;</td>
        </tr>
        <tr>
          <td align="center" colspan="2">
              <?=Button::setType('submit')->setEvent('btnLoading(this)')->setClass('btn-alt-danger')->setSize('btn-lg')->setIcon('import')->button('导入')?>
            <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
        </tr>
    </table></form>
  </div>
</div>
<script type="text/javascript">
function get_ruleitem_list(rule_id)
{
	var htmlStr = '';
	$.post("<?=site_url("assess/rulemanager/ajax_ruleitem_list")?>",{"rule_id":rule_id},function(json){
		json = eval("json = "+ json);
		if(json.state){
			for(i = 0,n= json.data.length;i<n;i++){
				htmlStr += '<label><input name="ids[]" type="checkbox" value="' + json.data[i].id + '" />' + json.data[i].name + '</label><br />';	
			}
			$("#ruleitem_list").html(htmlStr);
		}else{
			$("#ruleitem_list").html(json.msg);
		}
	});	
}

$(function (){
    $("#selectAll").click(function (){
        $("input[name='ids[]']").each(function(){
                if(this.checked)
                    $(this).prop("checked",false);
                else $(this).prop("checked",true);
            }
        );
        if($('#selectAll').prop("checked"))
            $('#selectAll').prop("checked",false);
        else $('#selectAll').prop("checked",true);
    });
});

function btnLoading(me)
{
    $(me).attr("disabled","disabled");
    $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1.3rem;height: 1.3rem"></span> 正在导入...');
    validateForm.submit();
}
</script>