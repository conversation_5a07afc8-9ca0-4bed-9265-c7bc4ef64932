<div class="content">
<div class="block">
    <div class="block-header">
        <h3 class="block-title">
            《<?=$item->getSubject3()?>》的子指标
        </h3>
    </div>
    <div class="block-content">
        <div class="main">
            <div class="box">
                <form id="validateForm" name="validateForm" method="post" action="">
                    <table width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-bordered">

                        <caption>评审指标</caption>
                        <?php
                        $childs = $item->getChildren();
                        $i=1;
                        while($child = $childs->getObject()):?>
                            <tr class="rule-<?=$i?>">
                                <th colspan="3" class="text-center block-header-default" data-item="指标<?=$i?>">子指标（<?=$i?>）<span class="float-right"><?=Button::setUrl(site_url('assess/rulemanager/child/item_id/'.$child->getId().'/id/'.$rule->getRuleId()))->setClass('btn-alt-success')->setSize('btn-xs')->window('子指标（'.getChildAssessCount($child->getId()).'）')?>&nbsp;<a class="btn btn-alt-primary add-row" data-id="<?=$i?>">添加</a>&nbsp;<a class="btn btn-alt-danger remove-row" data-id="<?=$i?>">删除</a></span></th>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th width="100">一级指标
                                    <input class="input" name="item[<?=$i?>][id]" type="hidden" value="<?=$child->getId()?>" />
                                    <input class="input" name="item[<?=$i?>][is_delete]" type="hidden" value="0" />
                                </th>
                                <td width="956" colspan="2"><input class="input form-control" name="item[<?=$i?>][subject1]" type="text" value="<?=$child->getSubject1()?>" size="36" /></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th width="100">二级指标</th>
                                <td width="956" colspan="2"><input class="input form-control" name="item[<?=$i?>][subject2]" type="text" value="<?=$child->getSubject2()?>" size="36" /></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th width="100">三级指标</th>
                                <td width="956" colspan="2"><input class="input form-control" name="item[<?=$i?>][subject3]" type="text" value="<?=$child->getSubject3()?>" size="36" /></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标说明</th>
                                <td colspan="2"><textarea class="input form-control" name="item[<?=$i?>][note]" rows="3"><?=$child->getNote()?></textarea></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>评分标准</th>
                                <td colspan="2"><textarea class="input form-control" name="item[<?=$i?>][standard]" rows="3"><?=$child->getStandard()?></textarea></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标权重</th>
                                <td colspan="2"><input class="input weight form-control w-auto custom-control-inline" name="item[<?=$i?>][weight]" type="number" min="0" value="<?=$child->getWeight()?:0?>" size="8" />
                                <p>该项指标的满分</p>
                                </td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>特殊属性</th>
                                <td colspan="2"><input name="item[<?=$i?>][is_veto]" type="radio" value="0"<?php if($child->getIsVeto()==0):?> checked<?php endif;?> />普通&nbsp; <input name="item[<?=$i?>][is_veto]" type="radio" value="9"<?php if($child->getIsVeto()==9):?> checked<?php endif;?> />一票否决</td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标类型</th>
                                <td colspan="2"><input name="item[<?=$i?>][is_objective]" type="radio" value="0"<?php if($child->getIsObjective()==0):?> checked<?php endif;?> />主观指标&nbsp; <input name="item[<?=$i?>][is_objective]" type="radio" value="1"<?php if($child->getIsObjective()==1):?> checked<?php endif;?> />客观指标</td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标标记</th>
                                <td colspan="2">
                                    <select class="input form-control" name="item[<?=$i?>][mark]">
                                        <option value="">请选择</option>
                                        <?=getSelectFromArray(getIndexs(true,'subject_code','ALL,'.$rule->getCode()),$child->getMark(),false)?>
                                    </select>
                                </td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>上级指标</th>
                                <td colspan="2">
                                    <select class="input form-control" name="item[<?=$i?>][pid]">
                                        <option value="0">顶级</option>
                                        <?=getSelectFromArray(getTopRuleItems($rule->getId()),$child->getPid(),false)?>
                                    </select>
                                </td>
                            </tr>
                            <tr class="rule-<?=$i?>" style="display: none">
                                <th>指标权限</th>
                                <td colspan="2"><?=get_checkbox(['JS'=>'技术专家/管理专家','CW'=>'财务专家'],'item['.$i.'][role]',$child->getRole(),'',false)?><p><strong>温馨提示：</strong>仅对“分别平均”计分有效，选中的角色可以看见。</p></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标排序</th>
                                <td colspan="2"><input class="sort-<?=$i?> form-control w-auto custom-control-inline" name="item[<?=$i?>][sort]" type="number" min="1" value="<?=$child->getSort()?:$item->getSort()+$i?>" size="8" /></td>
                            </tr>
                        <?php
                            $i++;
                        endwhile;
                        if($childs->getTotal()==0):
                        ?>
                            <tr class="rule-<?=$i?>">
                                <th colspan="3" class="text-center block-header-default" data-item="指标<?=$i?>">子指标（<?=$i?>）<span class="float-right"><a class="btn btn-alt-primary add-row" data-id="<?=$i?>">添加</a>&nbsp;<a class="btn btn-alt-danger remove-row" data-id="<?=$i?>">删除</a></span></th>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th width="100">一级指标

                                </th>
                                <td width="956" colspan="2"><input class="input form-control" name="item[<?=$i?>][subject1]" type="text" value="<?=$item->getSubject1()?>" size="36" /></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th width="100">二级指标</th>
                                <td width="956" colspan="2"><input class="input form-control" name="item[<?=$i?>][subject2]" type="text" value="<?=$item->getSubject2()?>" size="36" /></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th width="100">三级指标</th>
                                <td width="956" colspan="2"><input class="input form-control" name="item[<?=$i?>][subject3]" type="text" value="" size="36" /></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标说明</th>
                                <td colspan="2"><textarea class="input form-control" name="item[<?=$i?>][note]" rows="3"></textarea></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>评分标准</th>
                                <td colspan="2"><textarea class="input form-control" name="item[<?=$i?>][standard]" rows="3"></textarea></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标权重</th>
                                <td colspan="2"><input class="input weight form-control w-auto custom-control-inline" name="item[<?=$i?>][weight]" type="number" min="0" value="0" size="8" />
                                    <p>该项指标的满分</p>
                                </td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>特殊属性</th>
                                <td colspan="2"><input name="item[<?=$i?>][is_veto]" type="radio" value="0" checked />普通&nbsp; <input name="item[<?=$i?>][is_veto]" type="radio" value="9"/>一票否决</td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标类型</th>
                                <td colspan="2"><input name="item[<?=$i?>][is_objective]" type="radio" value="0" />主观指标&nbsp; <input name="item[<?=$i?>][is_objective]" type="radio" value="1" />客观指标</td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标标记</th>
                                <td colspan="2">
                                    <select class="input form-control" name="item[<?=$i?>][mark]">
                                        <option value="">请选择</option>
                                        <?=getSelectFromArray(getIndexs(true,'subject_code','ALL,'.$rule->getCode()),'',false)?>
                                    </select>
                                </td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>上级指标</th>
                                <td colspan="2">
                                    <select class="input form-control" name="item[<?=$i?>][pid]">
                                        <option value="0">顶级</option>
                                        <?=getSelectFromArray(getTopRuleItems($rule->getId()),'',false)?>
                                    </select>
                                </td>
                            </tr>
                            <tr class="rule-<?=$i?>" style="display: none">
                                <th>指标权限</th>
                                <td colspan="2"><?=get_checkbox(['JS'=>'技术专家/管理专家','CW'=>'财务专家'],'item['.$i.'][role]','','',false)?><p><strong>温馨提示：</strong>仅对“分别平均”计分有效，选中的角色可以看见。</p></td>
                            </tr>
                            <tr class="rule-<?=$i?>">
                                <th>指标排序</th>
                                <td colspan="2"><input class="sort-<?=$i?> form-control w-auto custom-control-inline" name="item[<?=$i?>][sort]" type="number" min="1" value="<?=$item->getSort()+$i?>" size="8" /></td>
                            </tr>
                        <?php endif;?>
                        <tr id="submit-area">
                            <td colspan="3" align="center">
                                <button type="submit" class="btn btn-alt-primary"><i class="fa fa-check-circle mr-1"></i> 保存</button>
                                <input name="id" type="hidden" id="id" value="<?=$rule->getRuleId()?>" />
                                <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
                        </tr>
                    </table>
                </form>
            </div>
        </div>
    </div>
</div>
</div>
<script>
    $(function(){
        $(document).on('click','.add-row',function(){
            var ruleId = $(this).data('id');
            var ruleNumber = $(".block-header-default").length;
            var newRuleId = parseInt(ruleNumber)+1;
            //克隆指标
            var ruleStr = '';
            $(".rule-"+ruleId).each(function (){
                ruleStr += $(this).clone()[0].outerHTML;
            });
            //替换行id
            var reg1 = '/\\['+ruleId+'\\]/ig';
            var reg2 = '/rule-'+ruleId+'/ig';
            var reg3 = '/评分指标（'+ruleId+'）/ig';
            var reg4 = '/data-id="'+ruleId+'"/ig';
            var reg5 = '/sort-'+ruleId+'/ig';
            var newruleStr = ruleStr.replace(eval(reg1), '['+newRuleId+']');
            newruleStr = newruleStr.replace(eval(reg2), 'rule-'+newRuleId);
            newruleStr = newruleStr.replace(eval(reg3), '评分指标（'+newRuleId+'）');
            newruleStr = newruleStr.replace(eval(reg4), 'data-id="'+newRuleId+'"');
            newruleStr = newruleStr.replace(eval(reg5), 'sort-'+newRuleId+'');
            //追加至表格
            $(newruleStr).insertBefore('#submit-area').find('.input').val('');
            $('.sort-'+newRuleId).val(newRuleId);
            $("input[name^='item["+newRuleId+"][subject1]']").val($("input[name^='item["+ruleNumber+"][subject1]']").val());
            $("input[name^='item["+newRuleId+"][subject2]']").val($("input[name^='item["+ruleNumber+"][subject2]']").val());
            $("input[name^='item["+newRuleId+"][subject3]']").val($("input[name^='item["+ruleNumber+"][subject3]']").val());
            $("input[name^='item["+newRuleId+"][sort]']").val(parseInt($("input[name^='item["+ruleNumber+"][sort]']").val())+1);
            $("select[name^='item["+newRuleId+"][pid]']").val($("select[name^='item["+ruleNumber+"][pid]']").val());
            $("input[name^='item["+newRuleId+"][weight]']").val($("input[name^='item["+ruleNumber+"][weight]']").val());
            $("input[name^='item["+newRuleId+"][subject3]']").focus();
            $("input[name='number']").val($(".block-header-default").length);

            //按钮变为减号
            // $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
            // $(this).find('span').removeClass('glyphicon-plus').addClass('glyphicon-minus');
        });

        $(document).on('click','.remove-row',function(){
            if(confirm('确定要删除该指标吗？')){
                var ruleId = $(this).data('id');
                $(".rule-"+ruleId).addClass('d-none');
                $("input[name^='item["+ruleId+"][is_delete]']").val(1);
            }
        });


        //指标目录
        var html = '';
        $(".tb_data tr .block-header-default").each(function (){
            var className = $(this).hasClass('objective') ? 'objective bg-success' : 'subjective';

            var content = $(this).data('item');
            if(content!=undefined){
                var indexNo = noSpaceBr(content.replace(/指标/,''));
                html += '<li class="'+className+' index-'+indexNo+'"><a href="javascript:;" onclick="scrollToLocation(\''+content+'\')" data-content="'+content+'">'+content+'</a></li>';
            }
        });
        if(html){
            $("#index-ul").html(html);
        }

    });

    function noSpaceBr(text){
        text = text.replace(/&nbsp;/ig, "");
        text = text.replace(/<br>/ig, "");
        text = text.trim();
        return text
    }

    function scrollToLocation(target) {
        var mainContainer = $('html');
        if(target!=undefined){
            var pos = $(".block-header-default[data-item='"+target+"']").offset().top-70;
            mainContainer.scrollTop(pos);
        }
    }

    function calculateWidth()
    {
        var width = $(".left-area").width();
        $("#page-nav").css('width',width-30);
    }

    window.onload = function (){
        calculateWidth();
    }
</script>