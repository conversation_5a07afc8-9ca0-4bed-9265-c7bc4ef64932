<link rel="stylesheet" href="<?=site_path('assets/css/bootstrapValidator.min.css')?>" />
<script src="<?=site_path('assets/js/bootstrapValidator.min.js')?>"></script>
<div class="page-header">
    <div class="row">
        <div class="col-sm-12">
            <div class="page-title-box">
                <h4 class="page-title">导出调查表</h4>
            </div>
        </div>
    </div>
</div>
<div class="page-body">
    <div class="row">
        <div class="col-lg-12">
            <div class="card m-b-30">
                <div class="card-body">
                    <form class="form-horizontal" name="form1" id="validateForm" action="<?=site_url('export/quarter/doExport')?>" method="post" enctype="multipart/form-data">
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">报表名称 <font color="red">*</font></label>
                            <div class="col-sm-10">
                                <select class="form-control" name="guideid" id="guideid" required>
                                    <?=getSelectFromArray(getGuideArray(),input::getMix('guideid'),false)?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label">报表状态 <font color="red">*</font></label>
                            <div class="col-sm-10">
                                <select class="form-control" name="statement" id="statement" required>
                                    <?=getSelectFromArray([
                                            0=>'所有',
                                            1=>'填写中',
                                            2=>'待审核',
                                            3=>'被退回',
                                            10=>'已审核',
                                    ],'',false)?>
                                </select>
                            </div>
                        </div>

                        <div class="clearfix"></div>
                        <div class="form-group col-xs-12 col-sm-12 col-md-12">
                            <div class="text-center">
                                <?=btn('导出','javascript:form1.submit();','ti-export','primary')?>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $("#department_id_sel").change(function () {
        $("#department_id").val($(this).val());
    });
</script>