<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
    <tbody>
    <tr>
        <td align="center">
            基本急救及床旁检测技术开展例数
        </td>
        <td align="center" style="width: 75px">
            近3年<br>总数
        </td>
        <?php
        for($i=$project->getStartYear();$i<=$project->getEndYear();$i++):
            ?>
            <td align="center" style="width: 15%"><?=$i?>年</td>
        <?php
        endfor;
        ?>
        <?php if (!$configs['action']['download']): ?>
            <td align="center" style="width: 86px">附件</td>
        <?php endif;?>
    </tr>
    <?php
    $ztbjs = [
        ['index'=>1,'name'=>'开展动静脉穿刺置管术例数'],
        ['index'=>2,'name'=>'开展胸腔闭式引流术例数'],
        ['index'=>3,'name'=>'开展腰椎穿刺引流术例数'],
        ['index'=>4,'name'=>'开展腹腔穿刺引流术例数'],
        ['index'=>5,'name'=>'开展清创缝合术例数'],
        ['index'=>6,'name'=>'开展气管插管术例数'],
        ['index'=>7,'name'=>'开展肺复苏术例数'],
        ['index'=>8,'name'=>'使用呼吸机（有创、无创）例数'],
        ['index'=>9,'name'=>'开展床旁即时检测（POCT）技术例数'],
    ];
    foreach ($ztbjs as $ztbj):
        $k = $ztbj['index'];
        $name = $ztbj['name'];
        ?>
        <tr>
            <td align="center">
                <?=$name?>
            </td>
            <td align="center">
                <?=$project->getSumDataByIndexCode('technique_jbjj_'.$k)?>
            </td>
            <?php
            for($year=$project->getStartYear();$year<=$project->getEndYear();$year++):
                $projectData = $project->getDataByIndexCode('technique_jbjj_'.$k,$year);
                ?>
                <td align="center">
                    <?=(int)$projectData->getData('user')?>
                </td>
                <?php
                $i++;
            endfor;
            ?>
            <?php
            $itemType = 'jbjj';
            if (!$configs['action']['download'] && $configs['needfile']['technique'][$itemType]): ?>
                <td align="center" style="width: 85px">
                    <?=Button::setName('附件('.$project->getAttachementCount('apply_technique_'.$itemType.'_'.$k).')')->setUrl(site_url('engine/completeattachement/upload/index/item_type/apply_technique_'.$itemType.'_'.$k.'/id/'.$project->getProjectId().'/is_show/'.(int)$configs['action']['show']))->setWidget($widget_name)->setProjectId($project->getProjectId())->setAction($configs['action'])->setWidgetUrl(site_url("engine/".$configs['engine']."/reloadWidget"))->setClass('btn-alt-success')->widget()?>
                </td>
            <?php endif;?>
        </tr>
    <?php
    endforeach;
    ?>
    </tbody>
</table>

