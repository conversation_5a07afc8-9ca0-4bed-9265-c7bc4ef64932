<div class="block-content">
<div class="main">
  <div class="search">
    <form id="form1" name="form1" method="post" action="">
      关键词
      <input name="search" type="text" id="search" size="15" class="form-control w-auto custom-control-inline" />
      <label>
        <input name="field" type="radio" id="radio" value="personname" checked="checked" />
        姓名</label>
      <label>
        <input name="field" type="radio" id="radio" value="user_idcard" />
        身份证号码</label>
      <label>
        <input type="radio" name="field" id="radio2" value="user_mobile" />
        手机</label>
        <?=btn('button','搜索','submit','find')?>
      </label>
    </form>
  </div>
  <div class="box">
      <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table">
        <thead>
      <tr>
        <th class="text-center"><?=getColumnStr('姓名','personname')?></th>
        <th class="text-center"><?=getColumnStr('身份证号','user_idcard')?></th>
        <th class="text-center"><?=getColumnStr('手机','user_mobile')?></th>
        <th class="text-center"><?=getColumnStr('所属单位','corporation_id')?></th>
        <th class="text-center">操作</th>
      </tr>
        </thead>
        <tbody>
      <?php while($user = $pager->getObject()):?>
      <tr>
        <td align="center"><a href="<?=site_url("user/profile/show/userid/".$user->getUserId())?>"><?=$user->getPersonname()?></a></td>
        <td align="center"><?=getMask($user->getUserIdcard(),8,6)?></td>
        <td align="center"><?=getMask($user->getUserMobile(),4,3)?></td>
        <td align="center"><?=$user->getCorporationName()?></td>
        <td align="center">
            <?=Button::setEvent("parent.todo({username:'".$user->getPersonname()."',userid:'".$user->getUserId()."',itemid:'".$itemid."'});")->setIcon('check')->button('选取')?>
      </tr>
      <?php endwhile;?>
        </tbody>
        <tfoot>
      <tr>
        <td colspan="5"><?=$pager->navbar(5)?></td>
      </tr>
        </tfoot>
    </table>
  </div>
</div>
</div>