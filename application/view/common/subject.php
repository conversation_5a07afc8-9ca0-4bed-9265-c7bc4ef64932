<script language="javascript" type="text/javascript">
function setValue(obj)
{
	$("#subject_id").val(obj.value);
	$("#subject_name").val(obj.options[obj.selectedIndex].text);
}

function getSonForTwo(obj)
{
	setValue(obj);
	$.post('<?=site_url("common/subject")?>',{code:obj.value,level:2},function(data){
		$("#subject2").empty();
		$('<option value="">--请选择二级学科分类--</option>').appendTo("#subject2");
		$(data).appendTo("#subject2");
	});	
}

function getSonForThree(obj)
{
	setValue(obj);
	$.post('<?=site_url("common/subject")?>',{code:obj.value,level:3},function(data){
		$("#subject3").empty();
		$('<option value="">--请选择三级学科分类--</option>').appendTo("#subject3");
		$(data).appendTo("#subject3");
	});	
}
</script>
<div class="note">
<ul>
<li><h2>请选择一级学科</h2><select name="subject1" id="subject1" onchange="getSonForTwo(this);">
<option value="">--请选择一级学科--</option>
<?php while($subject = $subjects->getObject()):?>
<option value="<?=$subject->getCode()?>"><?=$subject->getSubject()?></option>
<?php endwhile;?>
    </select></li>
<li><h2>请选择二级学科</h2>
  <select name="subject2" id="subject2" onchange="getSonForThree(this);">
  	<option value="">--请选择一级学科--</option>
  </select>
</li>
<li><h2>请选择三级学科</h2>
  <select name="subject3" id="subject3" onchange="setValue(this)">
  	<option value="">--请选择一级学科--</option>
  </select>
</li>
  <li> <input type="button" name="button" id="button" value=" 选 择 " onclick="parent.loadbox.complete({id:$('#subject_id').val(),subject:$('#subject_name').val()});"/>
      <input type="hidden" name="subject_id" id="subject_id" />
      <input type="hidden" name="subject_name" id="subject_name" />
      <input type="button" name="button2" id="button2" value=" 清 空 " onclick="parent.loadbox.complete({id:'',subject:''});"/>
  </li>
</ul>
<p class="note"><em>温馨提示：</em>请由范围从上到下依次选择。<br />
如：要选择“蔬菜学”。学科大类 选择“农业科学”；一级学科 选择“农学”；二级学科 选择“圆艺学”；三级学科 选择“蔬菜学”；然后点“选取”</p>
</div>
