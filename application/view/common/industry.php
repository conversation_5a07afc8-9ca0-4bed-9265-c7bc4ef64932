<script language="javascript" type="text/javascript">
function setValue(obj)
{
	$("#subject_id").val(obj.value);
	$("#subject_name").val(obj.options[obj.selectedIndex].text);
}

function getSonForTwo(obj)
{
	setValue(obj);
	$.post('<?=site_url("common/subject")?>',{code:obj.value,level:2},function(data){
		$("#subject2").empty();
		$('<option value="">--请选择二级行业分类--</option>').appendTo("#subject2");
		$(data).appendTo("#subject2");
	});	
}

function getSonForThree(obj)
{
	setValue(obj);
	$.post('<?=site_url("common/subject")?>',{code:obj.value,level:3},function(data){
		$("#subject3").empty();
		$('<option value="">--请选择三级行业分类--</option>').appendTo("#subject3");
		$(data).appendTo("#subject3");
	});	
}
</script>

<div class="page-body">
  <div class="form-group col-xs-12 col-sm-12 col-md-12">
    <label class="control-label col-xs-12 col-sm-3 col-md-3">请选择一级行业</label>
    <div class="col-xs-12 col-sm-5 col-md-3">
      <select name="subject1" id="subject1" onchange="getSonForTwo(this);">
        <option value="">--请选择一级行业--</option>
        <?php while($subject = $subjects->getObject()):?>
        <option value="<?=$subject->getCode()?>">
        <?=$subject->getSubject()?>
        </option>
        <?php endwhile;?>
      </select>
    </div>
  </div>
  <div class="form-group col-xs-12 col-sm-12 col-md-12">
    <label class="control-label col-xs-12 col-sm-3 col-md-3">请选择二级行业</label>
    <div class="col-xs-12 col-sm-5 col-md-3">
      <select name="subject2" id="subject2" onchange="getSonForThree(this);">
        <option value="">--请选择二级行业--</option>
      </select>
    </div>
  </div>
  <div class="form-group col-xs-12 col-sm-12 col-md-12">
    <label class="control-label col-xs-12 col-sm-3 col-md-3">请选择三级行业</label>
    <div class="col-xs-12 col-sm-5 col-md-3">
      <select name="subject3" id="subject3" onchange="setValue(this)">
        <option value="">--请选择三级行业--</option>
      </select>
    </div>
  </div>
  <div class="form-group col-xs-12 col-sm-12 col-md-12">
    <div class="col-xs-12 col-sm-5 col-md-9">
      <input type="button" name="button" id="button" value=" 选 择 " onclick="parent.loadbox.complete({id:$('#subject_id').val(),subject:$('#subject_name').val()});"/>
      <input type="hidden" name="subject_id" id="subject_id" />
      <input type="hidden" name="subject_name" id="subject_name" />
      <input type="button" name="button2" id="button2" value=" 清 空 " onclick="parent.loadbox.complete({id:'',subject:''});"/>
    </div>
  </div>
  <div class="alert alert-info" role="alert">请由范围从上到下依次选择。如：要选择"水稻种植"。行业大类选择"农业"；一级行业选择"农业"；二级行业选择"谷物种植"；三级行业 选择"水稻种植"；然后点"选取"</div>
  <p style="clear:both"></p>
</div>