<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <p style="clear:both;"></p>
  </div>
  <ul class="search">
    <li>系统检测到以下的帐号可能属于你，你可以验证这些帐号然后合并为一个帐号方便管理。</li>
    <li>帐号合并后其他帐号的相关信息也会自动归并到你的新帐号下面。</li>
    <li class="blue">帐号合并步骤：</li>
    <li class="blue">1、进行账号验证，成功验证后显示"已验"；</li>
    <li class="blue">2、点击"合并已验帐号"按钮进行合并操作；</li>
    <li class="red">3、该功能只支持负责人、专家、单位之间的合并（如果你同类型的账号有多个，请先联系客服电话帮你合并后再自助合并）；</li>
    <li class="red">4、如有帐号不想合并，则不用验证；只有已经通过验证的帐号才会被合并</li>
  </ul>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("tools/merge/getMergeguide")?>">
      <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
      <th>选</th>
      <th>帐号类型</th>
      <th>姓名</th>
      <th>身份证号</th>
      <th>移动电话</th>
      <th>电子邮箱</th>
      <th>工作单位</th>
      <th width="150">可用操作</th>
      </tr>
      <?php foreach($list as $user):?>
      <tr>
      <td align="center"><input name="select_id[]" type="checkbox" value="<?=$user['userid']?>"<?php if(!$user['validate']):?> disabled="disabled"<?php else:?> checked="checked"<?php endif;?> /></td>
      <td align="center"><?=$user['type']?></td>
      <td align="center"><?=$user['username']?></td>
      <td align="center"><?=getMask($user['idcard'],6,9)?></td>
      <td align="center"><?=getMask($user['mobile'],4,4)?></td>
      <td align="center"><?=getMask($user['email'],4,2)?></td>
      <td><?=$user['corporation']?></td>
      <td align="center"><?php if($user['validate']):?><i class="btn btn-info" role="button"></i>已验<?php else:?><a href="javascript:void(0);" onclick="return showWindow('验证','<?=site_url("tools/merge/validate/role/".$user['role']."/userid/".$user['userid'])?>',500,360);" class="btn btn-info btn-xs" role="button"><i class="glyphicon glyphicon-cog"></i>验证</a><?php endif;?></td>
      </tr>
      <?php endforeach;?>
      <tr>
      <td colspan="8"><input type="submit" class="btn btn-primary btn-sm" value="合并已验帐号" onclick="return confirm('该操作不可恢复！你确定要合并选中项？')"/></td>
      </tr>
      </table>
    </form>
  </div>
</div>