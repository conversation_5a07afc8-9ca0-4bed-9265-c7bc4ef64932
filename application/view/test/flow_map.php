<script src="http://cdnjs.cloudflare.com/ajax/libs/raphael/2.3.0/raphael.min.js"></script>
<script src="<?=site_path('assets/js/flowchart.min.js')?>"></script>
<script>

    window.onload = function () {
        var btn = document.getElementById("run"),
            cd = document.getElementById("code"),
            chart;

        (btn.onclick = function () {
            var code = cd.value;
            if (chart) {
                chart.clean();
            }
            chart = flowchart.parse(code);
            chart.drawSVG('canvas',{
                'flowstate' : {
                    'past' : { 'fill' : '#FFFF99'},
                    'current' : {'fill' : 'green', 'font-color' : 'white', 'font-weight' : 'bold'},
                    'invalid': {'fill' : '#eee', 'font-color' : 'gray'},
                }
            });
        })();

    };

    function myFunction(event, node) {
        console.log("You just clicked this node:", node);
    }

</script>
<div><textarea id="code" style="width: 100%;" rows="11">
<?=$flow->getFlowchartStr()?>

start@>operation2({"stroke":"Red"})@>condition3({"stroke":"Red"})@>operation5({"stroke":"Red"})@>end({"stroke":"Red"})
</textarea></div>
<div><button id="run" type="button">Run</button></div>
<div id="canvas"></div>
