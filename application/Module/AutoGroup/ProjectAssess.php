<?php namespace App\Module\AutoGroup;
use App\Module\AutoGroup\Contracts\Factory;
use App\Module\AutoGroup\Assess;
use App\Models\Project;
use App\Models\ProjectGroup;
use App\Models\GroupProject;
use App\Models\GroupExpert;
use Sofast\Core\Config;
use App\Models\Expert as ExpertModel;

class ProjectAssess extends Assess implements Factory
{
	public function __construct()
	{
		parent::__construct();
		$this->project = new Project;
		$this->group = new ProjectGroup;
		$this->group_project = new GroupProject;
		$this->group_expert = new GroupExpert;
		$this->expert = new ExpertModel;
	}

	public function loadConfig($templete='')
	{
		return $this->basegroup->loadConfig($templete);
	}

	public function distribution(array $config)
	{
		$this->config = $config;
		$projects = $this->getProjects(['statement'=>config::get("PLAN_ACCEPT",10),'declare_year'=>config::get('current_declare_year',date('Y'))]);
		//获取分组数组
		$group = $this->basegroup->getGroupArray($projects,$this->config['count']);
		if(count($group)==0) return false;
		return $this->createGroup($group);
	}

    public function getGroupInfo(array $config)
    {
    	$result = array();
    	$this->config = $config;
		$projects = $this->getProjects(['statement'=>config::get("PLAN_ACCEPT",10),'declare_year'=>config::get('current_declare_year',date('Y'))]);
		$result['projectCount'] = count($projects);
		if($config['count']==0) $result['groupCount']=1;
		else $result['groupCount'] = ceil($result['projectCount']/$config['count']);
		return $result;
    }

    public function addExpertToGroup($group_id,array $ids)
    {
    	if(!is_array($ids) || !$group_id) return false;
    	$data = array();
		foreach($ids as $index=>$id)
		{
			if(!$id) continue;
			$data[$index]['group_id'] = $group_id;
			$data[$index]['expert_id'] = $id;
			$data[$index]['type'] = 'project';
		}
		if(is_array($data))
			return $this->group_expert->insert($data);
    }

	public function createGroup($group=array())
	{
		$dataArray = array();
		$index = sprintf("%03d", rand(100,999));
		$group_note = $this->config['group_note']?$this->config['group_note']:'系统自动分组'.$index;
		foreach($group as $key=>$value)
		{
			$dataArray[$key]['project_count'] = count($value);
			$dataArray[$key]['group_note'] = $group_note.'(第'.($key+1).'组)';
			$dataArray[$key]['group_subject'] = $this->createGroupSubject($key+1);
			$dataArray[$key]['group_year'] = config::get('current_declare_year',date('Y'));
			$dataArray[$key]['subject_ids'] = $this->basegroup->getSubjectIdTop($key,10);
			$dataArray[$key]['created_at'] = date('Y-m-d H:i:s');
			$dataArray[$key]['updated_at'] = date('Y-m-d H:i:s');
			$_group[$dataArray[$key]['group_subject']] = $value;
		}
		if(is_array($dataArray))
			$this->group->insert($dataArray);
		return $this->addProjectToGroup($_group);
	}

	public function addProjectToGroup($groups=array())
	{
		$index=0;
		foreach($groups as $group_subject=>$group)
		{
			if(!$group_subject) continue;
			$group_id = $this->group->where('group_subject',$group_subject)->first()->id;
			foreach($group as $key=>$value)
			{
				$data[$index]['group_id'] = $group_id;
				$data[$index]['project_id'] = $value;
				$data[$index]['type'] = 'project';
				$index++;
			}
			$this->updateStatus($group);
		}
		if(is_array($data))
			return $this->group_project->insert($data);
	}

	public function updateStatus($data=array())
	{
		return Project::whereIn('project_id',$data)->update(['state_for_budget_book'=>'13']);
	}

	//专家适配列表
	public function listExperts($level=3)
	{
		return $this->getExpertList($level);
	}

}