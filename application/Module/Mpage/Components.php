<?php
namespace App\Module\Mpage;

class Components
{
	private $id;
	private $config=[];

	/**
	 * 表格定制处理
	 * @return [type] [description]
	 */
	function haddle($id,$configs){
		$this->id = $id;
		foreach($configs as $method=>$config){
			if(method_exists($this,$method)){
				$this->config = $config;
				call_user_func([$this,$method]);
			}
		}
	}

	function getConfigurePage($id,$config,$options){
		$this->id = $id;
		$html = '';
		$options = array_filter(explode('|',$options));

		foreach($options as $option){
			$this->config = $config[$option];
			$method = $option.'Config';
			if(method_exists($this,$method)){
				$html .= call_user_func([$this,$method]);
			}
		}
		return $html;
	}

	protected function getId(){
		return $this->id;
	}

	protected function getConfig($key=''){
		if($key) return $this->config[$key];
		return $this->config;
	}

	protected function document(){
		return pq()->html();
	}

	/**
	 * 过滤内容，只留汉字
	 * @param  [type] $html [description]
	 * @return [type]       [description]
	 */
	protected function filterStr($html){
		preg_match_all('/[\x{4e00}-\x{9fff}]+/u', $html, $matches);
		$html = join('', $matches[0]);
		return $html;
	}
}
?>