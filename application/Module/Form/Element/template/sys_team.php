<?php if($label):?>
	<div class="header no-margin-top"><?=$label?></div>
<?php endif;?>

<?php if($tips):?>
<div class="help-block alert alert-info"><?=$tips?'<i class="fa fa-exclamation-circle fa-lg orange"></i> '.$tips:''?></div>
<hr/>
<?php endif;?>
<div class="clearfix"></div>

<?=Form::input($name."[subject]",$value['subject'],'团队名称')
       ->setWidth(6,6,6)
       ->filter($config['presetfields'],'subject')
       ->render()?>

<?=Form::input($name."[user_name]",$value['user_name'],'带头人姓名')
	   ->filter($config['presetfields'],'user_name')
	   ->setWidth(6,6,6)
	   ->render()?>

<?=Form::datetime($name."[user_birthday]",$value['user_birthday'],'带头人出生年月')
	   ->setAttribute(['format'=>"yyyy-mm-dd"])
	   ->filter($config['presetfields'],'user_birthday')
	   ->setWidth(6,6,6)
	   ->render()?>

<?=Form::radio($name."[user_degree]",$value['user_degree'],'带头人学位')
	   ->setOptions(['博士','硕士','学士','其他'])
	   ->filter($config['presetfields'],'user_degree')
	   ->setWidth(6,6,6)
	   ->render()?>

<?=Form::radio($name."[user_sex]",$value['user_sex'],'带头人性别')
	   ->setOptions(['男','女'])
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_sex')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::select($name."[user_nation]",$value['user_nation'],'带头人民族')
	   ->setOptions(get_select_data('nation'))
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_nation')
	   ->setWidth(6,6,6)
	   ->render()?>

<?=Form::radio($name."[user_honor]",$value['user_honor'],'带头人职称')
	   ->setOptions(['高级','中级','初级','其他'])
	   ->filter($config['presetfields'],'user_honor')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_phone]",$value['user_phone'],'带头人联系电话')
	   ->filter($config['presetfields'],'user_phone')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_fax]",$value['user_fax'],'带头人传真')
	   ->filter($config['presetfields'],'user_fax')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_mobile]",$value['user_mobile'],'带头人手机号码')
	   ->filter($config['presetfields'],'user_mobile')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_email]",$value['user_email'],'带头人E-mail')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_email')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_work]",$value['user_work'],'带头人从事专业')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_work')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[degreeConfer]",$value['degreeConfer'],'带头人最终学位授予国或地区')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'degreeConfer')
	   ->setWidth(6,6,6)
	   ->render()?>

<?=Form::input($name."[user_occupation]",$value['user_occupation'],'带头人职务')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_occupation')
	   ->setWidth(6,6,6)
	   ->render()?>

<?=Form::input($name."[user_adminjob]",$value['user_adminjob'],'带头人行政职务')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_adminjob')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_workplace]",$value['user_workplace'],'带头人所在工作单位（院、系、所、实验室、中心）')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_workplace')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_research]",$value['user_research'],'带头人研究方向')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_research')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::input($name."[user_address]",$value['user_address'],'带头人通讯地址')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_address')
	   ->setWidth(6,6,6)
	   ->render()?>	
<?=Form::input($name."[user_postalcode]",$value['user_postalcode'],'带头人邮编')
	   ->setWidth(3)
	   ->filter($config['presetfields'],'user_postalcode')
	   ->setWidth(6,6,6)
	   ->render()?>
<?=Form::checkbox($name."[user_type][]",explode('|', $value['user_type']),'带头人学术荣誉')
	   ->setOptions(['A.国家杰青','B.长江学者','C.百千万人才工程','D.新世纪优秀人才计划','E.省青年基金资助计划','F省学术技术带头人','其它'])
	   ->filter($config['presetfields'],'user_type')
	   ->setWidth(9,3,12)
	   ->render()?>

<?=Form::checkbox($name."[research_place][]",explode('|',$value['research_place']),'所在研究基地（平台）')
       ->setOptions([
        'A.国家实验室'=>'A.国家实验室',
        'B.国家重点实验室'=>'B.国家重点实验室',
        'C.国家部委重点实验室'=>'C.国家部委重点实验室',
        'D.四川省重点实验室'=>'D.四川省重点实验室',
        'E.厅局重点实验室'=>'E.厅局重点实验室',
        'F.国家工程（技术）研究中心'=>'F.国家工程（技术）研究中心',
        'G.四川省工程研究中心'=>'G.四川省工程研究中心',
        'K.其它'=>'K.其它'])
       ->filter($config['presetfields'],'research_place')
       ->setWidth(9,3,12)
       ->render()?>

<?php if(in_array('team_number',$config['presetfields'])):?>
<table class="table table-bordered table-striped table-hover">
    <tr>
        <th class="text-center">团队构成人数(人)</th>
        <td align="center">
            <input name="<?=$name?>[team_number][total]" type="text" size="8" class="number" value="<?=$value['team_number']['total']?>" />
        </td>
        <th class="text-center" >高级</th>
        <td align="center" >
            <input name="<?=$name?>[team_number][senior]" type="text" size="8" class="number" value="<?=$value['team_number']['senior']?>" /></td>
        <th class="text-center" >中级</th>
        <td align="center" >
            <input name="<?=$name?>[team_number][medium]" type="text" size="8" class="number" value="<?=$value['team_number']['medium']?>" /></td>
        <th class="text-center" >初级</th>
        <td align="center" >
            <input name="<?=$name?>[team_number][junior]" type="text" size="8" class="number" value="<?=$value['team_number']['junior']?>" /></td>
        <th class="text-center" >博士后</th>
        <td align="center" >
            <input name="<?=$name?>[team_number][postdoctor]" type="text" size="8" class="number" value="<?=$value['team_number']['postdoctor']?>" /></td>
        <th class="text-center" >博士生</th>
        <td align="center" >
            <input name="<?=$name?>[team_number][doctor]" type="text" size="8" class="number" value="<?=$value['team_number']['doctor']?>" /></td>
        <th class="text-center" >硕士生</th>
        <td align="center" >
            <input name="<?=$name?>[team_number][master]" type="text" size="8" class="number" value="<?=$value['team_number']['master']?>" /></td>
    </tr>
</table>
<?php endif;?> 

<?php if(in_array('team_leader',$config['presetfields'])):?>
<table class="table table-striped table-hover presetfields_teamleader">

<thead>
<tr >
        <th class="text-left" colspan="9">研究骨干</th>
    </tr>
    <tr >
        <th class="text-center">身份证号</th>
        <th class="text-center" mark="<?=$name?>_name">姓名</th>
        <th class="text-center" mark="<?=$name?>_sex">性别</th>
        <th class="text-center" mark="<?=$name?>_birthday">出生年月</th>
        <th class="text-center" mark="<?=$name?>_honor">专业技术职称</th>
        <th class="text-center" mark="<?=$name?>_degree">学位</th>
        <th class="text-center" mark="<?=$name?>_research">研究方向</th>
        <th class="text-center" mark="<?=$name?>_effect">在团队中的作用</th>
        <th class="text-center" mark="<?=$name?>_company_name">单位</th>
        <th class="text-center" style="width:70px;">加/减</th>
    </tr>
</thead>
<tbody>
<?php if(!$value['team_leader']) $value['team_leader'][0]=[];
foreach($value['team_leader'] as $key=>$val):?>
    <tr >
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_leader][certificate][]" onblur="return fillmember(this);" style="width:100%;" value="<?=$value['team_leader'][$key]['certificate']?>"/></td>
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_leader][name][]" value="<?=$value['team_leader'][$key]['name']?>" class=" form-control" style="width:100%;" /></td>
    <td class="text-center">
    <select name="<?=$name?>[team_leader][sex][]" class="form-control">
        <?=getSelectFromArray(['男','女'],$value['team_leader'][$key]['sex'])?>
    </select></td>
    <td class="text-center">
    <?=Form::input($name.'[team_leader][birthday][]',$value['team_leader'][$key]['birthday'])->setFormType('baseform')->render()?>
    </td>
    <td class="text-center">
    <select name="<?=$name?>[team_leader][honor][]" class="form-control">
            <?=getWorkList($value['team_leader'][$key]['honor'])?>
    </select></td>
    <td class="text-center">
    <select name="<?=$name?>[team_leader][degree][]" class="form-control">
            <?=getDegreeList($value['team_leader'][$key]['degree'])?>
    </select></td>
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_leader][research][]" value="<?=$value['team_leader'][$key]['research']?>" class=" form-control" style="width:100%;" />
    </td>
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_leader][effect][]" value="<?=$value['team_leader'][$key]['effect']?>" class=" form-control" style="width:100%;" /></td>
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_leader][company_name][]" value="<?=$value['team_leader'][$key]['company_name']?>" class=" form-control" style="width:100%;" /></td>
    <td class="text-center">
        <input name="<?=$name?>[team_leader][user_id][]" type="hidden" value="<?=$value['team_leader'][$key]['user_id']?>"/>
        <input name="<?=$name?>[team_leader][company_id][]" type="hidden" value="<?=$value['team_leader'][$key]['company_id']?>"/>
        <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
        <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
    </td>
    </tr>
<?php endforeach;?>
</tbody>
</table>
<?php endif;?>

<?php if(in_array('team_member',$config['presetfields'])):?>
<table class="table table-striped table-hover presetfields_teammember">
<thead>
<tr >
        <th class="text-left" colspan="9">团队成员</th>
    </tr>
    <tr >
        <th class="text-center">身份证号</th>
        <th class="text-center" mark="<?=$name?>_name">姓名</th>
        <th class="text-center" mark="<?=$name?>_sex">性别</th>
        <th class="text-center" mark="<?=$name?>_birthday">出生年月</th>
        <th class="text-center" mark="<?=$name?>_honor">专业技术职务</th>
        <th class="text-center" mark="<?=$name?>_degree">学位</th>
        <th class="text-center" mark="<?=$name?>_research">研究方向</th>
        <th class="text-center" mark="<?=$name?>_effect">在团队中的作用</th>
        <th class="text-center" mark="<?=$name?>_company_name">单位</th>
        <th class="text-center" style="width:70px;">加/减</th>
    </tr>
</thead>
<tbody>
<?php if(!$value['team_member']) $value['team_member'][0]=[];
foreach($value['team_member'] as $key=>$val):?>
    <tr >
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_member][certificate][]" onblur="return fillmember(this);" style="width:100%;" value="<?=$value['team_member'][$key]['certificate']?>"/></td>
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_member][name][]" value="<?=$value['team_member'][$key]['name']?>" class=" form-control" style="width:100%;" /></td>
    <td class="text-center">
    <select name="<?=$name?>[team_member][sex][]" class="form-control">
        <?=getSelectFromArray(['男','女'],$value['team_member'][$key]['sex'])?>
    </select></td><td class="text-center">
    <?=Form::input($name.'[team_member][birthday][]',$value['team_member'][$key]['birthday'])->setFormType('baseform')->render()?>
    </td>
    <td class="text-center">
    <select name="<?=$name?>[team_member][honor][]" class="form-control">
            <?=getWorkList($value['team_member'][$key]['honor'])?>
    </select></td>
    <td class="text-center">
    <select name="<?=$name?>[team_member][degree][]" class="form-control">
            <?=getDegreeList($value['team_member'][$key]['degree'])?>
    </select></td><td class="text-center">
    <input type="text" name="<?=$name?>[team_member][research][]" value="<?=$value['team_member'][$key]['research']?>" class=" form-control" style="width:100%;" />
    </td><td class="text-center">
    <input type="text" name="<?=$name?>[team_member][effect][]" value="<?=$value['team_member'][$key]['effect']?>" class=" form-control" style="width:100%;" /></td>
    <td class="text-center">
    <input type="text" name="<?=$name?>[team_member][company_name][]" value="<?=$value['team_member'][$key]['company_name']?>" class=" form-control" style="width:100%;" /></td>
    <td class="text-center">
        <input name="<?=$name?>[team_member][user_id][]" type="hidden" value="<?=$value['team_member'][$key]['user_id']?>"/>
        <input name="<?=$name?>[team_member][company_id][]" type="hidden" value="<?=$value['team_member'][$key]['company_id']?>"/>
        <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
        <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
    </td>
    </tr>
<?php endforeach;?>
</tbody>
</table>
<?php endif;?>

<div class="clearfix"></div>
<input type="hidden" id="presetfields_teamleader" value="<?=implode(',',$config['presetfields_teamleader'])?>" />
<input type="hidden" id="presetfields_teammember" value="<?=implode(',',$config['presetfields_teammember'])?>" />
<script language="javascript" type="text/javascript">
$(function(){
  filterTh('presetfields_teamleader','team_leader');
  filterTh('presetfields_teammember','team_member');
});
function filterTh(id,flag)
{
	var fields = $('#'+id).val();
  if(!fields) return false;
    fields = fields.split(',');
    $('.'+id+' th[mark^="<?=$name?>_"]').each(function(){
        var mark = $(this).attr('mark');
        mark = mark.replace('<?=$name?>_','');
        if($.inArray(mark,fields)==-1)
        {
            $(this).remove();
            $('.'+id+' [name^="<?=$name?>['+flag+']['+mark+']"]').closest('td').remove();
        }
    });
}
</script>