<?php if($label):?>
    <div class="clearfix"></div>
    <div class="header no-margin-top"><?=$label?></div>
<?php endif;?>

<?php if($tips):?>
<div class="help-block alert alert-info"><?=$tips?'<i class="fa fa-exclamation-circle fa-lg orange"></i> '.$tips:''?></div>
<hr/>
<?php endif;?>
<div class="clearfix"></div>

<?php if(in_array('total',$config['presetfields_team'])):?>
<table class="table table-bordered table-striped table-hover presetfields_team">
    <tr>
    <?php if(in_array('total',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_total">项目组人数(人)</th>
        <td align="center">
            <input name="<?=$name?>[team][total]" type="text" size="8" class="number" value="<?=$value['team']['total']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('senior_all',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_senior_all">高级</th>
        <td align="center" >
            <input name="<?=$name?>[team][senior_all]" type="text" size="8" class="number" value="<?=$value['team']['senior_all']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('senior',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_senior">正高</th>
        <td align="center" >
            <input name="<?=$name?>[team][senior]" type="text" size="8" class="number" value="<?=$value['team']['senior']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('vice_senior',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_vice_senior">副高</th>
        <td align="center" >
            <input name="<?=$name?>[team][vice_senior]" type="text" size="8" class="number" value="<?=$value['team']['vice_senior']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('medium',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_medium">中级</th>
        <td align="center" >
            <input name="<?=$name?>[team][medium]" type="text" size="8" class="number" value="<?=$value['team']['medium']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('junior',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_junior">初级</th>
        <td align="center" >
            <input name="<?=$name?>[team][junior]" type="text" size="8" class="number" value="<?=$value['team']['junior']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('notitle',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_notitle">无职称</th>
        <td align="center" >
            <input name="<?=$name?>[team][notitle]" type="text" size="8" class="number" value="<?=$value['team']['notitle']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('postdoctor',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_postdoctor">在读博士后</th>
        <td align="center" >
            <input name="<?=$name?>[team][postdoctor]" type="text" size="8" class="number" value="<?=$value['team']['postdoctor']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('doctor',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_doctor">在读博士</th>
        <td align="center" >
            <input name="<?=$name?>[team][doctor]" type="text" size="8" class="number" value="<?=$value['team']['doctor']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('master',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_master">在读硕士</th>
        <td align="center" >
            <input name="<?=$name?>[team][master]" type="text" size="8" class="number" value="<?=$value['team']['master']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('bs',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_bs">博士</th>
        <td align="center" >
            <input name="<?=$name?>[team][doctor]" type="text" size="8" class="number" value="<?=$value['team']['doctor']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('ss',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_ss">硕士</th>
        <td align="center" >
            <input name="<?=$name?>[team][master]" type="text" size="8" class="number" value="<?=$value['team']['master']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('bachelor',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_bachelor">学士</th>
        <td align="center" >
            <input name="<?=$name?>[team][bachelor]" type="text" size="8" class="number" value="<?=$value['team']['bachelor']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('other',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_other">其它</th>
        <td align="center" >
            <input name="<?=$name?>[team][other]" type="text" size="8" class="number" value="<?=$value['team']['other']?>" />
        </td>
    <?php endif;?>
        </tr>
        <tr>
    <?php if(in_array('flow_total',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_flow_total">参与项目的流动人员</th>
        <td align="center" >
            <input name="<?=$name?>[team][flow_total]" type="text" size="8" class="number" value="<?=$value['team']['flow_total']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('temp_total',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_temp_total">临时聘用人员</th>
        <td align="center" >
            <input name="<?=$name?>[team][temp_total]" type="text" size="8" class="number" value="<?=$value['team']['temp_total']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('time_total',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_time_total">固定研究人员合计工作时间(人月)</th>
        <td align="center" >
            <input name="<?=$name?>[team][time_total]" type="text" size="8" class="number" value="<?=$value['team']['time_total']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('student_total',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_student_total">参与项目的在校学生</th>
        <td align="center" >
            <input name="<?=$name?>[team][student_total]" type="text" size="8" class="number" value="<?=$value['team']['student_total']?>" />
        </td>
    <?php endif;?>
    <?php if(in_array('people_per_month',$config['presetfields_team'])):?>
        <th class="text-center" mark="<?=$name?>_people_per_month">投入人月数</th>
        <td align="center" >
            <input name="<?=$name?>[team][people_per_month]" type="text" size="8" class="number" value="<?=$value['team']['people_per_month']?>" />
        </td>
    <?php endif;?>
    </tr>
</table>
<?php endif;?>

<?php if(in_array('name',$config['presetfields_member'])):?>
<table class="table table-striped table-hover presetfields_member">
<thead>
    <tr >
        <th class="text-center">身份证号</th>
        <?php if(in_array('name',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_name">姓名</th>
        <?php endif;?>
        <?php if(in_array('sex',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_sex">性别</th>
        <?php endif;?>
        <?php if(in_array('age',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_age">年龄</th>
        <?php endif;?>
        <?php if(in_array('birthday',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_birthday">出生日期</th>
        <?php endif;?>
        <?php if(in_array('degree',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_degree">学历</th>
        <?php endif;?>
        <?php if(in_array('professional_title',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_professional_title">专业技术职称</th>
        <?php endif;?>
        <?php if(in_array('title',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_title">职称</th>
        <?php endif;?>
        <?php if(in_array('title_type',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_title_type">职称分类</th>
        <?php endif;?>
        <?php if(in_array('subject_id',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_subject_id">学科代码</th>
        <?php endif;?>
        <?php if(in_array('subject_name',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_subject_name">学科名称</th>
        <?php endif;?>
        <?php if(in_array('company_name',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_company_name">工作单位</th>
        <?php endif;?>
        <?php if(in_array('workdepartment',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_workdepartment">所在部门</th>
        <?php endif;?>
        <?php if(in_array('works',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_works">项目分工</th>
        <?php endif;?>
        <?php if(in_array('project_type',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_project_type">项目类型</th>
        <?php endif;?>
        <?php if(in_array('marjor',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_marjor">从事专业</th>
        <?php endif;?>
        <?php if(in_array('learn_marjor',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_learn_marjor">所学专业</th>
        <?php endif;?>
        <?php if(in_array('time',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_time">投入本项目的工作时间(月)</th>
        <?php endif;?>
        <?php if(in_array('pay',$config['presetfields_member'])):?>
        <th class="text-center" mark="<?=$name?>_pay">是否有工资性收入</th>
        <?php endif;?>
        <th class="text-center" style="width:70px;">加/减</th>
    </tr>
</thead>
<tbody>
<?php if(!$value['members']) $value['members'][0]=[];
foreach($value['members'] as $key=>$val):?>
    <tr >
    <td class="text-center">
    <input type="text" name="<?=$name?>[members][certificate][]" onblur="return fillmember(this);" style="width:100%;" value="<?=$value['members'][$key]['certificate']?>"/>

    <input type="hidden" name="<?=$name?>[members][ids][]" value="<?=$value['members'][$key]['id']?>" />
    </td>
<?php if(in_array('name',$config['presetfields_member'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[members][name][]" value="<?=$value['members'][$key]['name']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('sex',$config['presetfields_member'])):?>
    <td class="text-center">
        <select name="<?=$name?>[members][sex][]" class="form-control">
            <?=getSelectFromArray(['男','女'],$value['members'][$key]['sex'])?>
        </select>
    </td>
<?php endif;?>
<?php if(in_array('age',$config['presetfields_member'])):?>
    <td class="text-center">
        <input type="text" name="<?=$name?>[members][age][]" value="<?=$value['members'][$key]['age']?>" class="width-30" />
    </td>
<?php endif;?>
<?php if(in_array('birthday',$config['presetfields_member'])):?>
    <td class="text-center">
        <input type="text" name="<?=$name?>[members][birthday][]" value="<?=$value['members'][$key]['birthday']?>"/>
    </td>
<?php endif;?>
<?php if(in_array('degree',$config['presetfields_member'])):?>
    <td class="text-center">
        <select name="<?=$name?>[members][degree][]" class="form-control">
            <?=getDegreeList($value['members'][$key]['degree'])?>
        </select>
    </td>
<?php endif;?>
<?php if(in_array('professional_title',$config['presetfields_member'])):?>
    <td class="text-center">
        <input type="text" name="<?=$name?>[members][professional_title][]" value="<?=$value['members'][$key]['professional_title']?>" style="width:100%;"/>
    </td>
<?php endif;?>
<?php if(in_array('title',$config['presetfields_member'])):?>
    <td class="text-center">
        <select name="<?=$name?>[members][title][]" class="form-control">
            <?=getWorkList($value['members'][$key]['title'])?>
        </select>
    </td>
<?php endif;?>
<?php if(in_array('title_type',$config['presetfields_member'])):?>
    <td class="text-center">
        <select name="<?=$name?>[members][title_type][]" class="form-control">
            <?=getSelectFromArray(['正高级','副高级','中级','初级','其他'],$value['members'][$key]['title_type'])?>
        </select>
    </td>
<?php endif;?>
<?php if(in_array('subject_id',$config['presetfields_member'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[members][subject_id][]" value="<?=$value['members'][$key]['subject_id']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('subject_name',$config['presetfields_member'])):?>
    <td class="text-center">
      <input type="text" name="<?=$name?>[members][subject_name][]" value="<?=$value['members'][$key]['subject_name']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('company_name',$config['presetfields_member'])):?>
    <td class="text-center">
      <input type="text" name="<?=$name?>[members][company_name][]" value="<?=$value['members'][$key]['company_name']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('workdepartment',$config['presetfields_member'])):?>
    <td class="text-center">
      <input type="text" name="<?=$name?>[members][workdepartment][]" value="<?=$value['members'][$key]['workdepartment']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('works',$config['presetfields_member'])):?>
    <td class="text-center">
        <textarea class="form-control" name="<?=$name?>[members][works][]" style="width:100%;height:33px" class="">
        <?=$value['members'][$key]['works']?>
        </textarea>
    </td>
<?php endif;?>
<?php if(in_array('project_type',$config['presetfields_member'])):?>
    <td class="text-center">
        <input type="text" name="<?=$name?>[members][project_type][]" value="<?=$value['members'][$key]['project_type']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('marjor',$config['presetfields_member'])):?>
    <td class="text-center">
        <input type="text" name="<?=$name?>[members][marjor][]" value="<?=$value['members'][$key]['marjor']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('learn_marjor',$config['presetfields_member'])):?>
    <td class="text-center">
        <input type="text" name="<?=$name?>[members][learn_marjor][]" value="<?=$value['members'][$key]['learn_marjor']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('time',$config['presetfields_member'])):?>
    <td class="text-center">
        <input type="text" name="<?=$name?>[members][time][]" value="<?=$value['members'][$key]['time']?>" class=" form-control" style="width:100%;" />
    </td>
<?php endif;?>
<?php if(in_array('pay',$config['presetfields_member'])):?>
    <td class="text-center">
        <select name="<?=$name?>[members][pay][]" class="form-control">
            <?=getSelectFromArray(['是','否'],$value['members'][$key]['pay'])?>
        </select>
    </td>
<?php endif;?>
    <td class="text-center">
        <input name="<?=$name?>[members][user_id][]" type="hidden" value="<?=$value['members'][$key]['user_id']?>"/>
        <input name="<?=$name?>[members][company_id][]" type="hidden" value="<?=$value['members'][$key]['company_id']?>"/>
        <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
        <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
    </td>
    </tr>
<?php endforeach;?>
</tbody>
</table>
<?php endif;?>

<div class="clearfix"></div>

<input type="hidden" id="presetfields_member" value="<?=implode(',',$config['presetfields_member'])?>" />
<input type="hidden" id="presetfields_team" value="<?=implode(',',$config['presetfields_team'])?>" />
<script language="javascript" type="text/javascript">
$(function(){
    // var fields_member = $('#presetfields_member').val();
    // fields_member = fields_member.split(',');
    // $('.presetfields_member th[mark^="<?=$name?>_"]').each(function(){
    //     var mark = $(this).attr('mark');
    //     mark = mark.replace('<?=$name?>_','');
    //     if($.inArray(mark,fields_member)==-1)
    //     {
    //         $(this).remove();
    //         $('.presetfields_member [name^="<?=$name?>[members]['+mark+']"]').closest('td').remove();
    //     }
    // });

    // var fields_team = $('#presetfields_team').val();
    // fields_team = fields_team.split(',');
    // $('.presetfields_team th[mark^="<?=$name?>_"]').each(function(){
    //     var mark = $(this).attr('mark');
    //     mark = mark.replace('<?=$name?>_','');
    //     if($.inArray(mark,fields_team)==-1)
    //     {
    //         $(this).remove();
    //         $('.presetfields_team [name^="<?=$name?>[team]['+mark+']"]').closest('td').remove();
    //     }
    // });
});
</script>
