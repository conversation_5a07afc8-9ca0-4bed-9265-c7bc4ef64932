<?php namespace App\Module\Form\Element;
use App\Module\Form\Widget;
class Input extends Widget
{
    public $default_config = [
        'type'=>'text',
        'group_width'=>'12',
        'label_width'=>'3',
        'width'=>'3',
        'page'=>'1'
    ];

    public function form()
    {
        $this->_config = array_merge($this->default_config,$this->getConfig());
        $no_padding = !$this->getFormGroupLabel()?'no-padding':'';

        $str = '
			<div class="form-group row">
				'.$this->getFormGroupLabel().'
				<div class="col-sm-10">
					'.$this->baseForm().'
					'.$this->getAddonElement().'
					<p class="help-block">'.$this->tips.'</p>
				</div>
				'.$this->getContainElement().'
				<div class="clearfix"></div>
			</div>
		';
        $str = str_replace('@group_width', $this->_config['group_width'],$str);
        $str = str_replace('@width', $this->_config['width'],$str);
        return $str;
    }

    public function baseForm()
    {
        $attributes = $this->getAttributes();
        $str = '<input type="@type" id="@id" name="@name" title="@title" class="form-control @class" @events '.$attributes.' value="@value" />';
        $str = str_replace('@name', $this->name,$str);
        $str = str_replace('@title', $this->_config['title'],$str);
        $str = str_replace('@type', $this->_config['type'],$str);
        $str = str_replace('@id', $this->id,$str);
        $str = str_replace('@events', $this->_config['events'],$str);
        $str = str_replace('@class', $this->_config['class'],$str);
        $str = str_replace('@value', $this->value,$str);
        return $str;
    }

    public function value()
    {

    }
}