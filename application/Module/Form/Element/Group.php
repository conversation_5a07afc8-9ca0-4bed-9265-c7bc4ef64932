<?php namespace App\Module\Form\Element;
use App\Module\Form\Widget;

class Group extends Widget
{
	public $default_config = [
		'group_width'=>'12',
		'label_width'=>'3',
		'width'=>'9',
		'page'=>'1'
	];

	public function addItem($element=null)
	{
		if($element)
			$this->_config['group_element'] = $element;
		return $this;
	}

	//处理包含元素的格式
	public function getGroupElement()
	{
		$contain_elements = [];
		$result='';
		
		if(is_array($this->_config['group_element']))
			$group_elements = $this->_config['group_element'];
		else $group_elements[0] = $this->_config['group_element'];

		foreach($group_elements as $element)
		{
			$config = $element->getConfig();
			$config['group_width'] = $config['group_width']?:6;
			$element->setConfig($config);
			$html=$element->render();
			//$config = $element->getConfig();
			// $html = '
			// 	<div class="col-xs-12 col-sm-'.$config['width'].' col-md-'.$config['width'].' no_padding">
			// 		<div class="input-group col-xs-12 col-md-12">
			// 		'.$element->render('baseform').'
			// 		'.$element->getAddonElement().'
			// 		</div>
			// 		<small class="help-block">'.$element->tips.'</small>
			// 	</div>
			// ';

			// $config = $element->getConfig();
			// $form_type=$config['form_type'];
			// if(!$element) continue;
			// if($form_type=='baseform')
			// 	$html = $element->render();
			// else
			// {
			// 	$html = $element->render();
			// 	$html = str_replace('<div class="form-group">', '',$html);
			// 	$html = str_replace('<div class="form-group no-padding">', '',$html);
			// 	$html = str_replace('<div class="clearfix"></div>', '',$html);
			// 	if(strrpos($html,'</div>'))
			// 		$html = substr_replace($html,'',strrpos($html,'</div>'),strlen('</div>'));
			// }

			$result .= $html;
		}

		return $result;
	}

	public function form()
	{
		$this->_config = array_merge($this->default_config,$this->getConfig());
		$str = '
			<div class="form-group @class col-xs-12 col-sm-@group_width col-md-@group_width" >
				'.$this->getFormGroupLabel().'
				<div class="col-xs-12 col-sm-@width col-md-@width">
					<div class="input-group col-xs-12 col-md-12 no-margin no-padding">
					'.$this->getGroupElement().'
					</div>
					<small class="help-block">'.$this->tips.'</small>
				</div>
				<div class="clearfix"></div>
			</div>
		';
		$str = str_replace('@group_width', $this->_config['group_width'],$str);
		$str = str_replace('@width', $this->_config['width'],$str);
		$str = str_replace('@class', $this->_config['class'],$str);

		return $str;
	}

	public function getFormGroupLabel()
	{
		$str='';
		if(!$this->name || strripos($this->name,'element_')!==false) return $str;
		$str .='<label class="control-label col-xs-12 col-sm-@label_width col-md-@label_width text-right">@label</label>';

		$str = str_replace('@label_width', $this->_config['label_width'],$str);
		$str = str_replace('@label', $this->name,$str);
		return $str;
	}

	public function baseForm()
	{
		
	}

    public function value()
    {

    }
}