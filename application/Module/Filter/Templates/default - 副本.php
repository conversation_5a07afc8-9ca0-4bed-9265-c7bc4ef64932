<script type="text/javascript">
$(function(){
	$('.option-more').click(function(){
		var $li = $(this).closest('li');
		if($li.css('overflow')=='hidden')
		{
			$li.css({'overflow':'auto','height':'auto'});
			if($li.height()>300)
			{
				scroll($li);
				$li.children('.listdiv').css({'overflow-y':'scroll','height':'300px'}).end().prepend('<div class="indexsearch"><input placeholder="输入关键字进行查找..." onkeyup="filterOption(this);"/></div>').children('.indexsearch').children('input').focus();
			}
			$(this).html('收起 <i class="ace-icon fa fa-angle-up"></i>');
		}
		else
		{
			$li.scrollTop(0).css({'overflow':'hidden','height':''}).children('.listdiv').css({'overflow':'hidden','height':''}).siblings('.indexsearch').remove();
			$(this).html('更多 <i class="ace-icon fa fa-angle-down"></i>').closest('li').children('.listdiv').children('span.option').removeClass('hide');
		}
	});

	$('.filter-list-more span').click(function(){
		if($(this).attr('class')=='filter-close')
		{
			$(this).attr('class','filter-open').html('隐藏条件 <i class="ace-icon fa fa-angle-up"></i>');
			$('.filter-list li.hide').removeClass('hide');
		}
		else
		{
			$(this).attr('class','filter-close').html('更多条件 <i class="ace-icon fa fa-angle-down"></i>');
			$('.filter-list li.hide').removeClass('hide');
			$('.filter-list li:eq(5)').nextAll().not(':last-child').addClass('hide');
		}
	});

//增加条件
	$('.option').click(function(){
		loading();
		var type = '?type=custom';
		var name = $(this).attr('name');
		var value = $(this).attr('value');
		var html = $(this).html();
		var url = getRootPath();
		var str = name+'|'+value+'|'+html;
		var _str = GetQueryString('str');
		url = url.substring(0,url.indexOf('?'));
		if(_str)
			url += type+'&str='+getEncodeStr(getDecodeStr(_str)+'&'+str);
		else 
			url += type+'&str='+getEncodeStr(str);

		window.top.location.href = url;
	});

	if($('.filter-list li.hide').length==0)
		$('.filter-list-more span').addClass('hide');
	else $('.filter-list-more span').removeClass('hide');

//删除条件
	$('.query-option').click(function(){
		loading();
		var type = '?type=custom';
		var name = $(this).attr('name');
		var _str = GetQueryString('str');
		var str = getDecodeStr(_str);
		var url = getRootPath();
		url = url.substring(0,url.indexOf('?'));
		str = filterQueryString(str,name);
		if(str && $.trim(str))
			url += type+'&str='+getEncodeStr(str);
		window.top.location.href = url;
	});

});

function filterOption(_this)
{
	var value = $.trim($(_this).val());
	var $option = $(_this).closest('li').children('.listdiv').children('span.option');
	if(value)
		$option.addClass('hide').filter(":contains('"+value+"')").removeClass('hide');
	else $option.removeClass('hide');
}

function addToFilter()
{
	return showPrompt('筛选器备注',' ',0,function(value,index){saveSearchFilter('',value,index);});
}

function filterQueryString(strs,str)
{
	var strArray = strs.split('&');
	for(var i=0;i<strArray.length;i++)
		if(strArray[i].indexOf(str)>=0)
			strArray.splice(i,1);
	if(strArray.length==0) return false;
	else return strArray.join('&');
}

function getEncodeStr(str)
{
	return ajaxHtml('<?=site_url("ajax/encrypt_str")?>',{str:str},'',{'async':false});
}

function getDecodeStr(str)
{
	return ajaxHtml('<?=site_url("ajax/decrypt_str")?>',{str:str},'',{'async':false});
}

function scroll($li)
{
	$li.on('scroll',function(){
	    var top = $(this).scrollTop();
	    $li.find('.option-more').css({'top':top});
	});
}

function reFreshPage()
{
	loading();
	var url = getRootPath();
	url = url.substring(0,url.indexOf('?'));
	window.top.location.href = url;
}
</script>

<form action="" method="post" id="search" class="form-horizontal" >
<div class="">
	<div class="header">
		<a href="javascript:void(0);" onClick="javascript:reFreshPage();return false;">全部</a>
		<i class="ace-icon fa fa-angle-right"></i>

		<?php foreach($data['queryOptions'] as $key=>$option):?>
			<a class="query-option" name="<?=$key?>" title="<?=$data['fields'][$key]['subject'].':'.$option?>">
				<span><?=$data['fields'][$key]['subject'].':'.$option?></span>
				<i class="ace-icon fa fa-times red"></i>
			</a>
		<?php endforeach;?>
		<?php if($data['queryOptions']):?>
		<i class="ace-icon fa fa-angle-right"></i>
		<?php endif;?>

		<input type="text" name="search" placeholder="在当前条件下搜索" value="<?=$data['search']?>" class="input-short" />

		<button type="submit" name="Submit4" class="btn btn-sm btn-primary btn_search btn-radius" >搜索</button>
		<button type="button" class="btn btn-sm btn-primary btn_search btn-radius" title="添加到筛选器" onClick="addToFilter();"><i class="ace-icon fa fa-filter"></i></button>

		<!-- <a class="pull-right" href="#" onclick="javascript:$('#search_new').addClass('hide');$('#search_old').removeClass('hide');">切换到旧版搜索</a> -->
	</div>

	<ul class="filter-list no-margin col-sm-12 no-padding">
		<li class="filter-list-title col-sm-1 bg-primary"><?=$data['fields']['common']['subject']?></li>
		<li class="filter-list-content col-sm-11 ">
			<div class="listdiv">
			<?=$data['fields']['common']['content']?>
			</div>
		</li>

		<?php $index=1;$i=1;foreach($data['fields'] as $name=>$field):if(!$field['except']):if(!array_key_exists($name, $data['queryOptions'])):?>
		<li class="filter-list-title col-sm-1 <?php if($i>3):?> hide <?php endif;?> "><?=$field['subject']?></li>
		<li class="filter-list-content col-sm-11 option<?=$index?> <?php if($i>3):?> hide <?php endif;?> ">
			<div class="listdiv">
			<?php foreach($field['options'] as $key=>$value):?>
				<span class="option" name="<?=$name?>" value="<?=$key?>" title="<?=$value?>"><?=$value?></span>
			<?php endforeach;?>
			<?php if($field['has_more']):?>
				<span class="option-more">更多 <i class="ace-icon fa fa-angle-down"></i></span>
			<?php endif;?>
			</div>
		</li>
		<?php $i++;endif;$index++;endif;endforeach;?>
		<li class="filter-list-more"><span class="filter-close">更多条件 <i class="ace-icon fa fa-angle-down"></i></span></li>
	</ul>
	<div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<input name="type" type="hidden" value="custom" />
</form>
<div class="space-4"></div>