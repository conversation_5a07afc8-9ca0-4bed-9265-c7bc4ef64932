<?php
namespace App\Module\interimreport;
use App\Module\Contracts\InterimReportContract;
use App\Models\ProjectInterimReport as InterimReportModel;
use App\Models\Project;
use App\Models\History;
use Sofast\Support\View;
use Sofast\Core\Config;
use Sofast\Core\sf;
use App\Facades\PDF;

class interimreport implements InterimReportContract
{
	private $session;
	private $project;

	public function __construct()
	{
		$this->session = $_SESSION;
	}

	// 中期报告列表
	public function lists($pagesize,$page,$path){
		return Project::where('state_for_interimreport','>',0)
		       ->where('user_id',$this->session['roleuserid'])
		       ->orderBy('updated_at','DESC')
		       ->paginate($pagesize,['*'],'page',$page)
		       ->setPath($path);
	}

	// 中期报告分页列表
	public function availableProjects($pagesize,$page,$path){
		return Project::where('statement',29)
			   ->whereIn('state_for_interimreport',['0','4'])
			   ->where('state_for_plan_book',10)  //归口部门同意签署
		       ->where('user_id',$this->session['roleuserid'])
		       ->orderBy('updated_at','DESC')
		       ->paginate($pagesize,['*'],'page',$page)
		       ->setPath($path);
	}

	// 根据项目id获取中期报告
	public function selectByProjectId($project_id){
		return Project::where('project_id',$project_id)->first();
	}

	// 单位意见
	public function unitOpinion(InterimReportModel $InterimReportModel){

	}

	// 单位意见
	public function officeOpinion(InterimReportModel $InterimReportModel){

	}

	// 保存数据
	public function save($data){
		if(!$data['id']) return false;
		$ir = InterimReportModel::firstOrNew(['project_id'=>$data['id']]);
		$ir->user_id = $this->session['roleuserid'];
		$ir->subject = $data['subject'];
		$ir->start_at = $data['start_at'];
		$ir->end_at = $data['end_at'];
		$ir->dwxz = $data['dwxz'];
		$ir->qytx = $data['qytx'];
		$ir->jjlx = $data['jjlx'];
		$ir->xmly = $data['xmly'];
		$ir->principal = $data['principal'];
		$ir->principal_mobile = $data['principal_mobile'];
		$ir->fzr = $data['fzr'];
		$ir->fzr_mobile = $data['fzr_mobile'];
		$ir->cdfzr = $data['cdfzr'];
		$ir->cdfzr_mobile = $data['cdfzr_mobile'];
		$ir->writeman = $data['writeman'];
		$ir->writeman_mobile = $data['writeman_mobile'];
		$ir->declare_money = $data['declare_money'];
		$ir->source = $data['source'];
		$ir->linkman = $data['linkman'];
		$ir->linkman_phone = $data['linkman_phone'];
		$ir->linkman_email = $data['linkman_email'];
		$data['release_at'] && $ir->release_at = $data['release_at'];
		if($ir->save())
		{
			$this->addHistory($data['id'],'内容编辑','interimreport');
			return true;
		}
		else return false;
	}

	// 删除数据
	public function delete(InterimReportModel $InterimReportModel){

	}

	// 显示数据
	public function preview($project_id,$template){
		$this->project = $this->selectByProjectId($project_id);

		View::set('project',$this->project);
		$attachment_dir = site_path('/up_files/').'/';
		
		$data['content'] = View::getContent($template);
		$data['content_end'] = View::getContent($template.'_end');
		$content = $this->project->attachments()->where('item_type','interimreport')->whereNotNull('item_id')->orderBy('id','DESC')->take(1)->first();
		$data['attachment'] = $attachment_dir.$content->file_path;
		return $this->makePDF($data,true);
	}

	// 导出
	public function output($project_id,$template){
		$this->project = $this->selectByProjectId($project_id);

		View::set('project',$this->project);
		View::set('notes',$this->getNotes($project_id));
		$attachment_dir = site_path('/up_files/').'/';
		
		$data['content'] = View::getContent($template);
		$data['content_end'] = View::getContent($template.'_end');
		$content = $this->project->attachments()->where('item_type','interimreport')->whereNotNull('item_id')->orderBy('id','DESC')->take(1)->first();
		$data['attachment'] = $attachment_dir.$content->file_path;

		$pdf = $this->makePDF($data,false,true);

        $pdf_url = $this->getDocumentPath().'/'.$pdf;

        $pdf_url = $this->getSavePath().'/'.$pdf;
        //if(Input::session('username')=='pengjie') dd($pdf_url);
        header("Content-type: application/pdf");
        header('Content-Disposition: attachment; filename="'.$this->project->project_id.'.pdf"');
        header('Content-Transfer-Encoding: binary');
        //header('Content-Length: '.sprintf('%d', filesize($pdf_url)));
        header('Expires: 0');
        //主要是这个判断
        if (isset($_SERVER['HTTP_USER_AGENT']) && ((strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE') !== false)))
        {
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
        }else
        {
            header('Pragma: no-cache');
        }

        exit(readfile($pdf_url));
	}

	// 上报
	public function submit($project_id){
		$this->project = $this->selectByProjectId($project_id);
		if(!$this->project->interimreport->id) return false;
		
		$this->project->state_for_interimreport=1;
		$this->project->interimreport->unit_note='';
		if($this->project->save())
		{
			$this->project->interimreport->save();
			$this->addHistory($project_id,'上报中期检查报告','interimreport');
			return true;
		}
		else return false;
	}

	// 显示意见
	public function getNotes($project_id){
		$this->project = $this->selectByProjectId($project_id);
		if(!$this->project->interimreport->id) return false;

		$result['unit_note'] = $this->project->interimreport->unit_note;
		$result['office_note'] = $this->project->interimreport->getOfficeNote();

		return $result;
	}

	// 上报
	public function unitSubmit($project_id){
		$this->project = $this->selectByProjectId($project_id);
		if(!$this->project->interimreport->id) return false;
		
		$this->project->state_for_interimreport=5;
		$this->project->interimreport->unit_note='同意项目的中期自检自查报告，上报市局。';
		if($this->project->save())
		{
			$this->project->interimreport->save();
			$this->addHistory($project_id,'单位同意','interimreport');
			return true;
		}
		else return false;
	}

	//单位驳回
	public function unitBack($project_id,$note){
		$this->project = $this->selectByProjectId($project_id);
		if(!$this->project->interimreport->id) return false;
		
		$this->project->state_for_interimreport=4;
		$this->project->interimreport->unit_note=$note;
		if($this->project->save())
		{
			$this->project->interimreport->save();
			$this->addHistory($project_id,'单位驳回','interimreport');
			return true;
		}
		else return false;
	}

	// 单位待审核
	public function unitWaitList($pagesize,$page,$path){
		return Project::where('state_for_interimreport',1)
		       ->where('corporation_id',$this->session['roleuserid'])
		       ->orderBy('updated_at','DESC')
		       ->paginate($pagesize,['*'],'page',$page)
		       ->setPath($path);
	}
	// 单位已上报
	public function unitList($pagesize,$page,$path)
	{
		return Project::where('state_for_interimreport','>',0)
		       ->where('corporation_id',$this->session['roleuserid'])
		       ->orderBy('updated_at','DESC')
		       ->paginate($pagesize,['*'],'page',$page)
		       ->setPath($path);
	}

	// 上报
	public function officeSubmit($project_id,$note){
		$this->project = $this->selectByProjectId($project_id);
		if(!$this->project->interimreport->id) return false;
		
		$this->project->state_for_interimreport=10;
		$this->project->interimreport->setOfficeNote($note);
		if($this->project->save())
		{
			$this->project->interimreport->save();
			$this->addHistory($project_id,'市局填写意见','interimreport');
			return true;
		}
		else return false;
	}

	// 市局待审核
	public function officeWaitList($pagesize,$page,$path){
		return Project::where('state_for_interimreport',5)
		       ->where('office_id',$this->session['office_id'])
		       ->orderBy('updated_at','DESC')
		       ->paginate($pagesize,['*'],'page',$page)
		       ->setPath($path);
	}
	// 市局已上报
	public function officeList($pagesize,$page,$path)
	{
		return Project::where('state_for_interimreport','>',0)
		       ->where('office_id',$this->session['office_id'])
		       ->orderBy('updated_at','DESC')
		       ->paginate($pagesize,['*'],'page',$page)
		       ->setPath($path);
	}

	// 查询
	public function search($sql){

	}

	// 上报检查
	public function submitCheck(Project $project)
	{
		$msg = array();
		//如果是老板本忽略
		if($project->radicate_year < 2010) return $msg;
		if($project->statement < 29) $msg[] = '项目立项后才能填写中期检查报告！';
		return $msg;
	}

	private function makePDF($data,$preview=false,$download=false)
    {
        if(!$this->pdfExist() || $preview || $download)
        {
            $pdf_name = 'interimreport.pdf';
            $style = WEBROOT.'/css/template.css';
            $watermark = '中期报告临时预览';
            $pdf = PDF::setContent($data['content'])
                        ->setContent($data['attachment'],'file')
                        ->setContent($data['content_end'])
                        ->setStyle($style)
                        ->setWaterMark($watermark,0.1)
                        ->setTitle($this->project->subject)
                        ->setSubject($this->project->subject)
                        ->setCreator('admin')
                        ->setAuthor($this->project->user_name);
            if($preview)
              return $pdf->show();
            else
              $pdf = $pdf->save($this->getSavePath().'/'.$pdf_name);

            //保存pdf名
          	$_data = $this->project->getFilePath();
            $_data['interimreport'] = $pdf_name;
            $this->project->update(['file_path'=>$this->project->setFilePath($_data)]);
        }
        else $pdf_name = $this->project->getFilePath('interimreport');
        return $pdf_name;
    }

    private function pdfExist()
    {
        //数据库无记录或文件不存在则重新生成
        if(!is_file($this->getSavePath().'/'.$this->project->getFilePath('interimreport'))) return false;
        else return true;
    }

    //获取文档存放路径，url方式获取
    private function getDocumentPath()
    {
        return site_path('/').'Documents/'.substr($this->project->project_id,0,1).'/'.$this->project->project_id;;
    }

    private function getSavePath()
    {
        $path = config::get('document_dir',WEBROOT.'/Documents').'/'.substr($this->project->project_id,0,1).'/'.$this->project->project_id;
        @mkdir($path,0777,true);
        return $path;
    }

    private function addHistory($project_id,$content,$type)
    {
    	return sf::getModel('Historys')->addHistory($project_id, $content, $type);
    }
}